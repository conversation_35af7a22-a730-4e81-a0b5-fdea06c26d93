# KTV营业数据跨库查询解决方案

## 问题分析

### 当前架构现状
根据代码分析，系统存在以下数据库分离情况：

1. **rms2019数据库** (yy.tang-hui.com.cn)
   - 包含：`opencacheinfo`表（开台信息）
   - 字段：开台时间、房间号、客户信息、渠道信息等

2. **operatedata数据库** (需要确认连接配置)
   - 包含：`rmcloseinfo`表（结账信息）
   - 字段：结账时间、消费金额、支付方式等

### 核心问题
- 两个关键表分布在不同数据库中
- 当前系统缺少operatedata数据库的连接配置
- 跨库JOIN查询存在性能和复杂性问题

## 解决方案

### 方案一：分离查询 + 应用层关联（推荐）

#### 1.1 数据获取策略
```
步骤1：从rms2019数据库获取开台数据
步骤2：从operatedata数据库获取结账数据  
步骤3：在应用层通过InvNo进行数据关联
步骤4：计算"直落"现象和统计分析
```

#### 1.2 实现流程

**第一步：获取开台数据**
```sql
-- 从rms2019.opencacheinfo获取昨天开台数据
SELECT 
    Ikey,
    InvNo,
    RmNo,
    ShopId,
    BookDateTime,
    CustName,
    Numbers,
    CtName,
    OrderUserName
FROM rms2019.dbo.opencacheinfo 
WHERE CAST(BookDateTime AS DATE) = '2025-07-17'
    AND InvNo IS NOT NULL 
    AND InvNo != ''
```

**第二步：获取结账数据**
```sql
-- 从operatedata.rmcloseinfo获取对应结账数据
SELECT 
    InvNo,
    CloseDateTime,
    TotalAmount,
    Cash,
    Vesa,
    WXPay,
    PaymentMethod
FROM operatedata.dbo.rmcloseinfo 
WHERE InvNo IN (@InvNoList)
    AND CAST(CloseDateTime AS DATE) = '2025-07-17'
```

**第三步：应用层数据关联**
```csharp
// 伪代码示例
var openData = GetOpenCacheData(date);
var invNos = openData.Select(x => x.InvNo).ToList();
var closeData = GetCloseInfoData(invNos);

var businessData = from open in openData
                   join close in closeData on open.InvNo equals close.InvNo into closeGroup
                   from closeInfo in closeGroup.DefaultIfEmpty()
                   select new KtvBusinessData
                   {
                       InvNo = open.InvNo,
                       OpenTime = open.BookDateTime,
                       CloseTime = closeInfo?.CloseDateTime,
                       Amount = closeInfo?.TotalAmount ?? 0,
                       IsDirect = IsDirectFall(open, closeInfo)
                   };
```

#### 1.3 优势
- 避免跨库JOIN的复杂性
- 查询性能更可控
- 易于维护和扩展
- 支持分布式部署

### 方案二：数据同步方案

#### 2.1 实时同步
- 在operatedata数据库中创建opencacheinfo的同步表
- 通过触发器或定时任务保持数据同步
- 在operatedata中进行本地JOIN查询

#### 2.2 定时ETL
- 每日凌晨将前一天的数据同步到统一的报表库
- 在报表库中进行数据分析和查询

### 方案三：视图联合查询（需要数据库链接）

#### 3.1 创建数据库链接
```sql
-- 在operatedata数据库中创建到rms2019的链接
EXEC sp_addlinkedserver 
    @server = 'RMS2019_SERVER',
    @srvproduct = 'SQL Server'
```

#### 3.2 创建联合视图
```sql
CREATE VIEW v_KtvBusinessData AS
SELECT 
    o.InvNo,
    o.BookDateTime as OpenTime,
    c.CloseDateTime as CloseTime,
    o.RmNo,
    o.ShopId,
    c.TotalAmount
FROM [RMS2019_SERVER].rms2019.dbo.opencacheinfo o
LEFT JOIN rmcloseinfo c ON o.InvNo = c.InvNo
```

## 推荐实现方案

### 技术实现步骤

#### 步骤1：配置operatedata数据库连接
```xml
<!-- 在database.config中添加 -->
<add name="operatedataEntities" 
     connectionString="metadata=res://*/OperateData.OperateData_DB.csdl|res://*/OperateData.OperateData_DB.ssdl|res://*/OperateData.OperateData_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=[服务器地址];initial catalog=operatedata;user id=sa;password=********;MultipleActiveResultSets=True;App=EntityFramework&quot;" 
     providerName="System.Data.EntityClient" />
```

#### 步骤2：创建数据访问层
```csharp
// 创建OperateData仓储
public class OperateDataRepository : BaseRepository<RmCloseInfo>
{
    // 获取结账数据的方法
    public List<RmCloseInfo> GetCloseInfoByInvNos(List<string> invNos, DateTime date)
    {
        return db.RmCloseInfo
                 .Where(x => invNos.Contains(x.InvNo) && 
                            DbFunctions.TruncateTime(x.CloseDateTime) == date.Date)
                 .ToList();
    }
}
```

#### 步骤3：创建业务服务层
```csharp
public class KtvBusinessDataService
{
    public List<KtvBusinessDataModel> GetDailyBusinessData(DateTime date)
    {
        // 1. 获取开台数据
        var openData = _rmsRepository.GetOpenCacheByDate(date);
        
        // 2. 获取结账数据
        var invNos = openData.Select(x => x.InvNo).ToList();
        var closeData = _operateDataRepository.GetCloseInfoByInvNos(invNos, date);
        
        // 3. 数据关联和分析
        return ProcessBusinessData(openData, closeData);
    }
}
```

#### 步骤4：直落现象分析逻辑
```csharp
private bool IsDirectFall(OpenCacheInfo openInfo, RmCloseInfo closeInfo)
{
    if (closeInfo == null) return false;
    
    // 直落判断条件：
    // 1. 开台和结账在同一天
    // 2. 时间间隔在合理范围内（如2-8小时）
    // 3. 没有中间换台记录
    
    var timeDiff = closeInfo.CloseDateTime - openInfo.BookDateTime;
    return timeDiff.TotalHours >= 2 && timeDiff.TotalHours <= 8;
}
```

## 性能优化建议

### 1. 索引优化
```sql
-- rms2019.opencacheinfo
CREATE INDEX IX_opencacheinfo_BookDateTime_InvNo ON opencacheinfo(BookDateTime, InvNo)

-- operatedata.rmcloseinfo  
CREATE INDEX IX_rmcloseinfo_InvNo_CloseDateTime ON rmcloseinfo(InvNo, CloseDateTime)
```

### 2. 缓存策略
- 使用Redis缓存当日营业数据
- 历史数据可以缓存更长时间
- 实现分页查询减少内存占用

### 3. 异步处理
- 大数据量查询使用异步方法
- 实现查询进度反馈
- 支持查询结果导出

## 数据模型设计

### KTV营业数据模型
```csharp
public class KtvBusinessDataModel
{
    public string InvNo { get; set; }           // 单据号
    public DateTime OpenTime { get; set; }      // 开台时间
    public DateTime? CloseTime { get; set; }    // 结账时间
    public string RmNo { get; set; }            // 房间号
    public int ShopId { get; set; }             // 店铺ID
    public string CustName { get; set; }        // 客户姓名
    public int Numbers { get; set; }            // 人数
    public decimal TotalAmount { get; set; }    // 消费金额
    public string PaymentMethod { get; set; }   // 支付方式
    public string Channel { get; set; }         // 渠道信息
    public bool IsDirect { get; set; }          // 是否直落
    public TimeSpan? Duration { get; set; }     // 消费时长
}
```

## 总结

**推荐采用方案一（分离查询 + 应用层关联）**，理由：
1. 实现简单，维护成本低
2. 性能可控，易于优化
3. 不依赖数据库特性，兼容性好
4. 支持灵活的业务逻辑处理

**关键成功因素：**
1. 确认operatedata数据库连接配置
2. 建立合适的索引提升查询性能
3. 实现有效的缓存策略
4. 设计清晰的数据模型和业务逻辑
