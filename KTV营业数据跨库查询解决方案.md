# KTV营业数据跨库查询解决方案

## 问题分析

### 当前架构现状
根据代码库分析，发现以下数据库架构：

1. **rms2019数据库** (yy.tang-hui.com.cn)
   - 包含 `opencacheinfo` 表（开台信息）
   - 包含房间、预订等基础数据

2. **dbfood数据库** (************)
   - 包含 `RmCloseInfo` 表（结账信息）
   - 包含消费、账单等数据

### 重要发现
通过代码分析发现：
- 系统中实际使用的是 **dbfood数据库** 而不是operatedata数据库
- RmCloseInfo表位于dbfood数据库中
- 两个关键表分别位于不同的数据库服务器
- 无法直接使用JOIN进行跨库查询

## 解决方案

### 方案一：分离查询 + 应用层关联（推荐）

#### 1.1 数据获取策略
```
步骤1：从rms2019数据库获取开台数据
步骤2：从operatedata数据库获取结账数据  
步骤3：在应用层通过InvNo进行数据关联
步骤4：计算"直落"现象和统计分析
```

#### 1.2 实现流程

**第一步：获取开台数据**
```sql
-- 从rms2019.opencacheinfo获取昨天开台数据
SELECT 
    Ikey,
    InvNo,
    RmNo,
    ShopId,
    BookDateTime,
    CustName,
    Numbers,
    CtName,
    OrderUserName
FROM rms2019.dbo.opencacheinfo 
WHERE CAST(BookDateTime AS DATE) = '2025-07-17'
    AND InvNo IS NOT NULL 
    AND InvNo != ''
```

**第二步：获取结账数据**
```sql
-- 从dbfood.RmCloseInfo获取对应结账数据
SELECT
    InvNo,
    Cash,
    Cash_Targ,
    Vesa,
    VesaName,
    WXPay,
    CloseDateTime
FROM dbfood.dbo.RmCloseInfo
WHERE InvNo IN (@InvNoList)
    AND CAST(CloseDateTime AS DATE) = '2025-07-17'
```

**第三步：应用层数据关联**
```csharp
// 伪代码示例
var openData = GetOpenCacheData(date);
var invNos = openData.Select(x => x.InvNo).ToList();
var closeData = GetCloseInfoData(invNos);

var businessData = from open in openData
                   join close in closeData on open.InvNo equals close.InvNo into closeGroup
                   from closeInfo in closeGroup.DefaultIfEmpty()
                   select new KtvBusinessData
                   {
                       InvNo = open.InvNo,
                       OpenTime = open.BookDateTime,
                       CloseTime = closeInfo?.CloseDateTime,
                       Amount = closeInfo?.TotalAmount ?? 0,
                       IsDirect = IsDirectFall(open, closeInfo)
                   };
```

#### 1.3 优势
- 避免跨库JOIN的复杂性
- 查询性能更可控
- 易于维护和扩展
- 支持分布式部署

### 方案二：数据同步方案

#### 2.1 实时同步
- 在dbfood数据库中创建opencacheinfo的同步表
- 通过触发器或定时任务保持数据同步
- 在dbfood中进行本地JOIN查询

#### 2.2 定时ETL
- 每日凌晨将前一天的数据同步到统一的报表库
- 在报表库中进行数据分析和查询

### 方案三：视图联合查询（需要数据库链接）

#### 3.1 创建数据库链接
```sql
-- 在dbfood数据库中创建到rms2019的链接
EXEC sp_addlinkedserver
    @server = 'RMS2019_SERVER',
    @srvproduct = 'SQL Server',
    @datasrc = 'yy.tang-hui.com.cn'
```

#### 3.2 创建联合视图
```sql
CREATE VIEW v_KtvBusinessData AS
SELECT
    o.InvNo,
    o.BookDateTime as OpenTime,
    c.CloseDateTime as CloseTime,
    o.RmNo,
    o.ShopId,
    c.Cash + c.Vesa + c.WXPay as TotalAmount
FROM [RMS2019_SERVER].rms2019.dbo.opencacheinfo o
LEFT JOIN RmCloseInfo c ON o.InvNo = c.InvNo
```

## 推荐实现方案

### 技术实现步骤

#### 步骤1：确认dbfood数据库连接
当前系统已配置dbfood数据库连接：
```xml
<!-- database.config中已存在 -->
<add name="dbfoodEntities"
     connectionString="metadata=res://*/DbFood.DbFood_DB.csdl|res://*/DbFood.DbFood_DB.ssdl|res://*/DbFood.DbFood_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=dbfood;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;"
     providerName="System.Data.EntityClient" />
```

#### 步骤2：扩展现有数据访问层
```csharp
// 扩展DbFood仓储，添加RmCloseInfo查询方法
public partial class RmCloseInfoRepository : BaseRepository<RmCloseInfo>
{
    // 获取结账数据的方法
    public List<RmCloseInfo> GetCloseInfoByInvNos(List<string> invNos, DateTime date)
    {
        return db.RmCloseInfo
                 .Where(x => invNos.Contains(x.InvNo))
                 .ToList();
    }

    // 获取指定日期的所有结账数据
    public List<RmCloseInfo> GetCloseInfoByDate(DateTime date)
    {
        // 注意：需要确认CloseDateTime字段是否存在
        return db.RmCloseInfo.ToList();
    }
}
```

#### 步骤3：创建业务服务层
```csharp
public class KtvBusinessDataService
{
    private readonly opencacheinfoRepository _rmsRepository;
    private readonly RmCloseInfoRepository _dbFoodRepository;

    public List<KtvBusinessDataModel> GetDailyBusinessData(DateTime date)
    {
        // 1. 获取开台数据
        var openData = _rmsRepository.GetOpenCacheByDate(date);

        // 2. 获取结账数据
        var invNos = openData.Where(x => !string.IsNullOrEmpty(x.Invno))
                           .Select(x => x.Invno).ToList();
        var closeData = _dbFoodRepository.GetCloseInfoByInvNos(invNos, date);

        // 3. 数据关联和分析
        return ProcessBusinessData(openData, closeData);
    }

    private List<KtvBusinessDataModel> ProcessBusinessData(
        List<opencacheinfo> openData,
        List<RmCloseInfo> closeData)
    {
        var closeDict = closeData.ToDictionary(x => x.InvNo);

        return openData.Select(open => new KtvBusinessDataModel
        {
            InvNo = open.Invno,
            OpenTime = open.BookDateTime ?? DateTime.MinValue,
            CloseTime = closeDict.ContainsKey(open.Invno) ?
                       GetCloseTime(closeDict[open.Invno]) : null,
            RmNo = open.RmNo,
            ShopId = open.ShopId ?? 0,
            CustName = open.CustName,
            Numbers = open.Numbers ?? 0,
            TotalAmount = closeDict.ContainsKey(open.Invno) ?
                         CalculateTotalAmount(closeDict[open.Invno]) : 0,
            IsDirect = IsDirectFall(open, closeDict.GetValueOrDefault(open.Invno))
        }).ToList();
    }
}
```

#### 步骤4：直落现象分析逻辑
```csharp
private bool IsDirectFall(opencacheinfo openInfo, RmCloseInfo closeInfo)
{
    if (closeInfo == null) return false;
    if (!openInfo.BookDateTime.HasValue) return false;

    // 直落判断条件：
    // 1. 有开台记录且有结账记录
    // 2. 同一个房间号
    // 3. 时间间隔在合理范围内（如1-12小时）
    // 4. 金额大于0

    var totalAmount = CalculateTotalAmount(closeInfo);
    if (totalAmount <= 0) return false;

    // 注意：需要确认RmCloseInfo中是否有时间字段
    // 如果没有CloseDateTime，可能需要其他方式判断
    return true; // 简化判断：有开台有结账就算直落
}

private decimal CalculateTotalAmount(RmCloseInfo closeInfo)
{
    return (closeInfo.Cash ?? 0) +
           (closeInfo.Vesa ?? 0) +
           (closeInfo.WXPay ?? 0);
}

private DateTime? GetCloseTime(RmCloseInfo closeInfo)
{
    // 注意：RmCloseInfo表中可能没有CloseDateTime字段
    // 需要根据实际表结构调整
    return null; // 暂时返回null，需要确认实际字段
}
```

## 性能优化建议

### 1. 索引优化
```sql
-- rms2019.opencacheinfo
CREATE INDEX IX_opencacheinfo_BookDateTime_InvNo ON opencacheinfo(BookDateTime, Invno)
WHERE Invno IS NOT NULL AND Invno != ''

-- dbfood.RmCloseInfo
CREATE INDEX IX_RmCloseInfo_InvNo ON RmCloseInfo(InvNo)
WHERE InvNo IS NOT NULL AND InvNo != ''
```

### 2. 缓存策略
- 使用Redis缓存当日营业数据
- 历史数据可以缓存更长时间
- 实现分页查询减少内存占用

### 3. 异步处理
- 大数据量查询使用异步方法
- 实现查询进度反馈
- 支持查询结果导出

## 数据模型设计

### KTV营业数据模型
```csharp
public class KtvBusinessDataModel
{
    public string InvNo { get; set; }           // 单据号
    public DateTime OpenTime { get; set; }      // 开台时间
    public DateTime? CloseTime { get; set; }    // 结账时间
    public string RmNo { get; set; }            // 房间号
    public int ShopId { get; set; }             // 店铺ID
    public string CustName { get; set; }        // 客户姓名
    public int Numbers { get; set; }            // 人数
    public decimal TotalAmount { get; set; }    // 消费金额
    public string PaymentMethod { get; set; }   // 支付方式
    public string Channel { get; set; }         // 渠道信息
    public bool IsDirect { get; set; }          // 是否直落
    public TimeSpan? Duration { get; set; }     // 消费时长
}
```

## 总结

**推荐采用方案一（分离查询 + 应用层关联）**，理由：
1. 实现简单，维护成本低
2. 性能可控，易于优化
3. 不依赖数据库特性，兼容性好
4. 支持灵活的业务逻辑处理
5. 利用现有的数据库连接配置

**关键成功因素：**
1. 利用现有的dbfood数据库连接配置
2. 建立合适的索引提升查询性能
3. 实现有效的缓存策略
4. 设计清晰的数据模型和业务逻辑
5. 确认RmCloseInfo表的实际字段结构

**注意事项：**
1. 需要确认RmCloseInfo表中是否有时间相关字段
2. 验证InvNo字段在两个表中的数据一致性
3. 处理数据缺失或格式不一致的情况
4. 考虑大数据量查询的性能优化
