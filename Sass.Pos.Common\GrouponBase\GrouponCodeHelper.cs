﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.GrouponBase
{
    /// <summary>
    /// 卡券帮助类
    /// </summary>
    public class GrouponCodeHelper
    {
        public static Tuple<DateTime, DateTime> ValidEndPattern(ValidEndPatternModel model)
        {
            var startTime = new DateTime();
            var endTime = startTime;
            //总间隔天数
            var day = (int)(model.ValidEnd - model.Valid).TotalDays;

            switch (model.ValidModelNo)
            {
                //正常模式
                case 1:
                    startTime = model.Valid;
                    endTime = model.ValidEnd;
                    break;
                //销售之日起n天有效
                case 2:
                    startTime = model.BuyTime;
                    endTime = model.BuyTime.AddDays(day);
                    break;
                //开始日期可变，结束日期固定
                case 3:
                    var buyDiff = (model.Valid - model.BuyTime).TotalDays + day;
                    startTime = model.ValidEnd.AddDays(buyDiff * -1);
                    endTime = model.ValidEnd;
                    break;
            }

            return new Tuple<DateTime, DateTime>(startTime, endTime);
        }
    }

    public class ValidEndPatternModel
    {
        public int ValidModelNo { get; set; }

        public DateTime Valid { get; set; }

        public DateTime ValidEnd { get; set; }

        public DateTime BuyTime { get; set; }
    }
}
