﻿<?xml version="1.0" encoding="utf-8" ?>
<objects xmlns="http://www.springframework.net">
  <!--<object name="SpringClass" type="Saas.Pos.Model.TestClass,Saas.Pos.Model">
    <property name="Token" value="123"></property>
    --><!--<property name="sub" ref="SpringClassSub"></property>--><!--

  </object>
  <object name="SpringClass1" type="TestClass,Saas.Pos.Model">
    <property name="Token" value="123_001"></property>
    --><!--<property name="sub" ref="SpringClassSub"></property>--><!--

  </object>-->

  <object name="MQServer" type="MiddlewareLibrary.Context.MQServerContext,MiddlewareLibrary">
    <property name="SerName" value="111.230.193.116"></property>
    <property name="SerCode" value="hd_mq_user"></property>
    <property name="SerPwd" value="mq200519"></property>
  </object>

  <!--KEY -VALUE  数据库-->
  <object name="DB_KV" type="Saas.Pos.Drive.Common.Proxy.Middleware.RedisContainer,Saas.Pos.Drive">
    <property name="Config" ref="DB_Config"></property>
  </object>
  <object name="DB_Config" type="MiddlewareLibrary.NoSql.RedisConfig,MiddlewareLibrary">
    <property name="DbPassword" value="123"></property>
    <property name="RedisMaxReadPool" value="3"></property>
    <property name="RedisMaxWritePool" value="1"></property>
    <property name="RedisHosts" value="************:6379"></property>
  </object>

</objects>