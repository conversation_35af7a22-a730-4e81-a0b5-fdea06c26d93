﻿using ComponentApplicationServiceInterface.Context.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IPos : IDbFoodLabelService, IDbFoodService, IBillHandlerService, ITimeFrameService, IOrderMenuService, IDbRoomService, IDbOrderService, IDbFdUserService, IMemberInfoService, IPosDbFoodService, IUserBindingService, IEmpGiftRecordService, IGiftLimitConfigInfoService, IConsumeBillService, IFdCashOrderService, IWxPayInfoService, INewFoodService
    {

        /// <summary>
        /// 卡券派发销售相关操作
        /// </summary>
        [OperationContract]
        ResponseContext<string> Test(string context);

        [OperationContract]
        ResponseContext<string> GetToken(string context);
    }
}
