﻿using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.CouponManage
{
    public interface ICouponManageUse
    {
        /// <summary>
        /// 校验优惠卷返回金额
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        UseCouponDataModel VerifyCouponData(UseCouponDataContext context);

        /// <summary>
        /// 使用优惠卷
        /// </summary>
        /// <returns></returns>
        ReturnInt UseCouponData(UseCouponDataExContext context);
    }
}
