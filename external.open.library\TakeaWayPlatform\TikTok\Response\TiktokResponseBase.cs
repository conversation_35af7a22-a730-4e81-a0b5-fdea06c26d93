﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.Tiktok.Response
{
    public class TiktokResponseBase<T>
    {
        public T data { get; set; }

        public string message { get; set; }

        public Extra extra { get; set; }

        public BaseRespModel BaseResp { get; set; }
    }

    public class Extra
    {
        public int error_code { get; set; }

        public string description { get; set; }

        public int sub_error_code { get; set; }

        public string sub_description { get; set; }

        public string logid { get; set; }

        public long now { get; set; }
    }

    public class BaseRespModel
    {
        public string StatusMessage { get; set; }

        public int StatusCode { get; set; }
    }
}
