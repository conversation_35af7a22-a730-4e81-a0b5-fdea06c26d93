﻿using Saas.Pos.Common;
using Saas.Pos.Common.Rms;
using Saas.Pos.Model.Rms.Model;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Extend
{
    public static class BookCacheInfoExtend
    {
        public static BookCaCheModel GetFirst(this List<BookCaCheModel> books, int shopId, string timeNo, string comeDate, string tel, string bookNo, string rtNo, string endKey, int number, int ctNo)
        {
            return books.FirstOrDefault(w => w.ShopId == shopId && timeNo == w.BeginTimeNo && w.ComeDate == comeDate && w.CustTel == tel && w.BookNo == bookNo
            && w.RtNo == rtNo && w.EndTimeNo == endKey && w.Numbers == number && w.CtNo == ctNo);
        }

        public static BookCaCheModel GetFirst(this List<BookCaCheModel> books, int shopId, string bookIKey)
        {
            return books.FirstOrDefault(w => w.ShopId == shopId && w.IKey == bookIKey);
        }

        /// <summary>
        /// 获取指定时间段的时间间隔
        /// </summary>
        /// <returns></returns>
        public static List<TimeLinesModel> GetIntervalTime(DateTime date, int begHour, int endHour, string startHourTime, string endHourTime)
        {
            int count = 8;
            var nowDate = DateTime.Now;
            DateTime? startHourDate = null;
            DateTime? endHourDate = null;
            if (!string.IsNullOrEmpty(startHourTime) && !string.IsNullOrEmpty(endHourTime))
            {
                startHourDate = BookingHelper.ConvertTimeDate(date, startHourTime);
                endHourDate = BookingHelper.ConvertTimeDate(date, endHourTime);
                if (endHourDate < startHourDate)
                    endHourDate = endHourDate.Value.AddDays(1);
            }

            var timeLines = new List<TimeLinesModel>();
            var startDate = BookingHelper.ConvertTimeDate(date, begHour.ToString());
            var endDate = BookingHelper.ConvertTimeDate(date, endHour.ToString());
            if (startDate.Date > nowDate.Date || (startDate.Date == nowDate.Date && startDate > nowDate))
            {
                count = 7;
                bool stop = startHourDate.HasValue && startHourDate <= startDate && startDate <= endHourDate;
                timeLines.Add(new TimeLinesModel() { Time = startDate.ToString("HH:mm"), Stop = stop });
            }

            //需要一个随之增加的时间
            var currentDate = startDate;
            //模除三十不等于0说明不是整点
            if (startDate.Minute % 30 != 0)
            {
                currentDate = startDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                if (currentDate.Minute > GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval)
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    if (currentDate.Minute - GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval <= 10)
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:00:00"));
                    else
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:30:00"));
                }
                else
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    if (currentDate.Minute - GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval <= 10)
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:00:00"));
                    else
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:30:00"));
                }
            }
            else
                currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);

            for (int i = 0; i < count; i++)
            {
                //判断当前时间是否超过，超过就不返回了
                if (currentDate.Date <= nowDate.Date && currentDate < nowDate)
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    continue;
                }

                bool stop = startHourDate.HasValue && startHourDate <= currentDate && currentDate <= endHourDate;
                //正常情况，没有跨天
                if (begHour < endHour)
                {
                    if (currentDate >= endDate)
                        break;

                    timeLines.Add(new TimeLinesModel() { Time = currentDate.ToString("HH:mm"), Stop = stop });
                }
                else
                    //跨天，非正常情况，只循环7次
                    timeLines.Add(new TimeLinesModel() { Time = currentDate.ToString("HH:mm"), Stop = stop });

                currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
            }

            return timeLines;
        }

        public static List<string> GetIntervalTime(DateTime date, int begHour, int endHour)
        {
            int count = 8;
            var nowDate = DateTime.Now;

            var timeLines = new List<string>();
            var startDate = BookingHelper.ConvertTimeDate(date, begHour.ToString());
            var endDate = BookingHelper.ConvertTimeDate(date, endHour.ToString());
            if (startDate.Date > nowDate.Date || (startDate.Date == nowDate.Date && startDate > nowDate))
            {
                count = 7;
                timeLines.Add(startDate.ToString("HH:mm"));
            }

            //需要一个随之增加的时间
            var currentDate = startDate;
            //模除三十不等于0说明不是整点
            if (startDate.Minute % 30 != 0)
            {
                currentDate = startDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                if (currentDate.Minute > GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval)
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    if (currentDate.Minute - GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval <= 10)
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:00:00"));
                    else
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:30:00"));
                }
                else
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    if (currentDate.Minute - GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval <= 10)
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:00:00"));
                    else
                        currentDate = DateTime.Parse(currentDate.ToString("yyyy-MM-dd HH:30:00"));
                }
            }
            else
                currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);

            for (int i = 0; i < count; i++)
            {
                //判断当前时间是否超过，超过就不返回了
                if (currentDate.Date <= nowDate.Date && currentDate < nowDate)
                {
                    currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
                    continue;
                }

                //正常情况，没有跨天
                if (begHour < endHour)
                {
                    if (currentDate >= endDate)
                        break;

                    timeLines.Add(currentDate.ToString("HH:mm"));
                }
                else
                    //跨天，非正常情况，只循环7次
                    timeLines.Add(currentDate.ToString("HH:mm"));

                currentDate = currentDate.AddMinutes(GlobalConfig.Global.BookConfig.BookingConfig.TimeInterval);
            }

            return timeLines;
        }
    }
}
