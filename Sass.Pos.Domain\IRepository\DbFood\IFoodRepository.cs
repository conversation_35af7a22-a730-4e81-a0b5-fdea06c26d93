﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IFoodRepository : IRepositoryBase<Food>
    {
        List<StoreReportModel> GetStoreReport(GetStoreReportContext context);

        List<GetStoreFtTypeReportModel> GetDetailReport(GetStoreFtTypeReportContext context);

        List<HeadCountModel> GetHeadCountReport(GetStoreReportContext context);

        List<WaitCreateModel> GetWaitCreate();

        GetFoodModel GetFood(string fdNo);

        List<GetFoodDataModel> GetFoodData(GetFoodDataContext context);

        List<GetFdTypeDataModel> GetFdTypeData(GetFdTypeDataContext context);

        List<GetUserScenceFdNoModel> GetUserSceFdNoData(GetSceneRoleConfigModel context);
    }
}
