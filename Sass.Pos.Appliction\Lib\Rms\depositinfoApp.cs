﻿using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class depositinfoApp : AppBase<depositinfo>
    {
        public List<GetDepositListModel> GetDepositList(GetDepositListContext context) 
        {
            return Repository.depositinfo.GetDepositList(context);
        }
    }
}
