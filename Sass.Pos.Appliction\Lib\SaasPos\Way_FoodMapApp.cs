﻿using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Way_FoodMapApp : AppBase<Way_FoodMap>
    {
        /// <summary>
        /// 根据指定的平台门店和第三方商品ID，查出绑定的天王下单明细
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetFoodMapListModel> GetFoodList(GetFoodContext context)
        {
            return Repository.Way_FoodMap.IQueryable(w => context.ThirdFdNo.Contains(w.ThirdFdNo) && w.StoreId == context.StoreId && w.Platform == context.Platform).Select(x => new GetFoodMapListModel()
            {
                FdNo = x.FdNo,
                Qty = x.Qty,
                ThirdFdNo = x.ThirdFdNo,
                RedirectUrl = x.RedirectUrl,
                IsGiftMember = x.IsGiftMember,
                MemberCardLevel = x.MemberCardLevel,
                NSetKeys = x.GrouponKeys,
            }).ToList();
        }
    }
}
