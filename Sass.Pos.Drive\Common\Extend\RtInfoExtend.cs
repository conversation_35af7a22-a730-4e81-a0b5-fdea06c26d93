﻿using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Extend
{
    public static class RtInfoExtend
    {
        public static rtinfo GetFirst(this List<rtinfo> rtinfos, int shopId, string rtNo)
        {
            var data = rtinfos.FirstOrDefault(w => w.ShopId == shopId && w.RtNo == rtNo);
            return data;
        }
    }
}
