﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.Tiktok.Request
{
    public class TiktokReverseConsumeRequest
    {
        /// <summary>
        /// 代表券码一次核销的唯一标识
        /// </summary>
        public string verify_id { get; set; }
        /// <summary>
        /// 代表一张券码的标识
        /// </summary>
        public string certificate_id { get; set; }
    }
}
