# KTV营业数据查询完整解决方案

## 数据库架构确认

### 数据库配置信息
1. **rms2019数据库**
   - 服务器：*************
   - 数据库：dbfood (注意：连接名为rms2019但实际指向dbfood)
   - 用户：sa / Musicbox@***
   - 包含表：opencacheinfo（开台信息）

2. **operatedata数据库**
   - 服务器：***********
   - 数据库：operatedata
   - 用户：sa / Musicbox***
   - 包含表：rmcloseinfo（结账信息）

### 架构分析
- 两个关键表分布在不同的物理服务器上
- 需要跨服务器查询和数据关联
- 关联字段：InvNo（发票号）

## 方案一：存储过程 + 链接服务器方案

### 1.1 配置链接服务器

```sql
-- 在operatedata数据库服务器(***********)上执行
-- 创建到rms2019服务器的链接
EXEC sp_addlinkedserver 
    @server = 'RMS2019_LINK',
    @srvproduct = 'SQL Server',
    @provider = 'SQLNCLI',
    @datasrc = '*************'

-- 配置链接服务器登录映射
EXEC sp_addlinkedsrvlogin 
    @rmtsrvname = 'RMS2019_LINK',
    @useself = 'false',
    @locallogin = NULL,
    @rmtuser = 'sa',
    @rmtpassword = 'Musicbox@***'

-- 测试链接
SELECT * FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo WHERE 1=0
```

### 1.2 主存储过程实现

```sql
-- 在operatedata数据库中创建主存储过程
USE operatedata
GO

CREATE PROCEDURE [dbo].[sp_GetKtvBusinessData]
    @QueryDate DATE = NULL,
    @ShopId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 默认查询昨天数据
    IF @QueryDate IS NULL
        SET @QueryDate = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE)
    
    DECLARE @StartTime DATETIME = @QueryDate
    DECLARE @EndTime DATETIME = DATEADD(DAY, 1, @QueryDate)
    
    BEGIN TRY
        -- 临时表存储结果
        CREATE TABLE #BusinessData (
            InvNo NVARCHAR(50),
            ShopId INT,
            RmNo NVARCHAR(20),
            OpenTime DATETIME,
            CloseTime DATETIME,
            CustName NVARCHAR(100),
            Numbers INT,
            CashAmount DECIMAL(18,2),
            VesaAmount DECIMAL(18,2),
            WXPayAmount DECIMAL(18,2),
            TotalAmount DECIMAL(18,2),
            PaymentMethod NVARCHAR(100),
            IsDirect BIT,
            BusinessHours DECIMAL(5,2),
            ChannelType NVARCHAR(50)
        )
        
        -- 获取开台和结账关联数据
        INSERT INTO #BusinessData (
            InvNo, ShopId, RmNo, OpenTime, CustName, Numbers,
            CashAmount, VesaAmount, WXPayAmount, TotalAmount,
            PaymentMethod, IsDirect, BusinessHours, ChannelType
        )
        SELECT 
            o.Invno,
            o.ShopId,
            o.RmNo,
            o.BookDateTime,
            o.CustName,
            o.Numbers,
            ISNULL(c.Cash, 0) as CashAmount,
            ISNULL(c.Vesa, 0) as VesaAmount,
            ISNULL(c.WXPay, 0) as WXPayAmount,
            ISNULL(c.Cash, 0) + ISNULL(c.Vesa, 0) + ISNULL(c.WXPay, 0) as TotalAmount,
            CASE 
                WHEN c.WXPay > 0 THEN '微信支付'
                WHEN c.Vesa > 0 THEN '会员卡'
                WHEN c.Cash > 0 THEN '现金'
                ELSE '未支付'
            END as PaymentMethod,
            CASE WHEN c.InvNo IS NOT NULL THEN 1 ELSE 0 END as IsDirect,
            0 as BusinessHours, -- 后续计算
            CASE 
                WHEN c.WXPay > 0 THEN '线上'
                WHEN c.Vesa > 0 THEN '会员'
                WHEN c.Cash > 0 THEN '现金'
                ELSE '其他'
            END as ChannelType
        FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo o
        LEFT JOIN dbo.rmcloseinfo c ON o.Invno = c.InvNo
        WHERE o.BookDateTime >= @StartTime 
            AND o.BookDateTime < @EndTime
            AND o.Invno IS NOT NULL 
            AND o.Invno != ''
            AND (@ShopId IS NULL OR o.ShopId = @ShopId)
        
        -- 返回主要业务数据
        SELECT 
            '业务数据' as DataType,
            InvNo,
            ShopId,
            RmNo,
            OpenTime,
            CustName,
            Numbers,
            CashAmount,
            VesaAmount,
            WXPayAmount,
            TotalAmount,
            PaymentMethod,
            IsDirect,
            ChannelType
        FROM #BusinessData
        ORDER BY OpenTime
        
        -- 返回直落统计
        SELECT 
            '直落统计' as DataType,
            COUNT(*) as TotalOrders,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectOrders,
            SUM(CASE WHEN IsDirect = 0 THEN 1 ELSE 0 END) as NonDirectOrders,
            CAST(SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as DirectRate,
            SUM(CASE WHEN IsDirect = 1 THEN TotalAmount ELSE 0 END) as DirectAmount,
            SUM(CASE WHEN IsDirect = 0 THEN TotalAmount ELSE 0 END) as NonDirectAmount
        FROM #BusinessData
        
        -- 返回渠道统计
        SELECT 
            '渠道统计' as DataType,
            ChannelType,
            COUNT(*) as OrderCount,
            SUM(TotalAmount) as TotalAmount,
            AVG(TotalAmount) as AvgAmount,
            CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM #BusinessData) AS DECIMAL(5,2)) as Percentage
        FROM #BusinessData
        GROUP BY ChannelType
        ORDER BY TotalAmount DESC
        
        -- 返回时段统计
        SELECT 
            '时段统计' as DataType,
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END as TimeSlot,
            COUNT(*) as OrderCount,
            SUM(TotalAmount) as TotalAmount,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectCount
        FROM #BusinessData
        WHERE OpenTime IS NOT NULL
        GROUP BY 
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END
        ORDER BY TotalAmount DESC
        
        DROP TABLE #BusinessData
        
    END TRY
    BEGIN CATCH
        IF OBJECT_ID('tempdb..#BusinessData') IS NOT NULL
            DROP TABLE #BusinessData
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH
END
GO
```

### 1.3 辅助存储过程

```sql
-- 创建数据验证存储过程
CREATE PROCEDURE [dbo].[sp_ValidateKtvData]
    @QueryDate DATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 检查链接服务器连接
    BEGIN TRY
        DECLARE @TestCount INT
        SELECT @TestCount = COUNT(*) FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo WHERE 1=0
        PRINT '链接服务器连接正常'
    END TRY
    BEGIN CATCH
        PRINT '链接服务器连接失败: ' + ERROR_MESSAGE()
        RETURN -1
    END CATCH
    
    -- 数据质量检查
    SELECT 
        '数据质量报告' as ReportType,
        (SELECT COUNT(*) FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo 
         WHERE CAST(BookDateTime AS DATE) = @QueryDate AND Invno IS NOT NULL) as OpenRecords,
        (SELECT COUNT(*) FROM dbo.rmcloseinfo) as CloseRecords,
        (SELECT COUNT(*) FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo o
         INNER JOIN dbo.rmcloseinfo c ON o.Invno = c.InvNo
         WHERE CAST(o.BookDateTime AS DATE) = @QueryDate) as MatchedRecords
END
GO
```

### 1.4 C#调用代码

```csharp
// 添加operatedata数据库连接配置
public class OperateDataContext
{
    private readonly string _connectionString = 
        "Data Source=***********;Initial Catalog=operatedata;User ID=sa;Password=Musicbox***;";
    
    public List<KtvBusinessDataResult> GetKtvBusinessData(DateTime queryDate, int? shopId = null)
    {
        var results = new List<KtvBusinessDataResult>();
        
        using (var connection = new SqlConnection(_connectionString))
        {
            using (var command = new SqlCommand("sp_GetKtvBusinessData", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.Parameters.AddWithValue("@QueryDate", queryDate.Date);
                if (shopId.HasValue)
                    command.Parameters.AddWithValue("@ShopId", shopId.Value);
                
                connection.Open();
                using (var reader = command.ExecuteReader())
                {
                    // 读取业务数据
                    while (reader.Read())
                    {
                        if (reader["DataType"].ToString() == "业务数据")
                        {
                            results.Add(new KtvBusinessDataResult
                            {
                                InvNo = reader["InvNo"].ToString(),
                                ShopId = Convert.ToInt32(reader["ShopId"]),
                                RmNo = reader["RmNo"].ToString(),
                                OpenTime = Convert.ToDateTime(reader["OpenTime"]),
                                CustName = reader["CustName"].ToString(),
                                Numbers = Convert.ToInt32(reader["Numbers"]),
                                TotalAmount = Convert.ToDecimal(reader["TotalAmount"]),
                                PaymentMethod = reader["PaymentMethod"].ToString(),
                                IsDirect = Convert.ToBoolean(reader["IsDirect"]),
                                ChannelType = reader["ChannelType"].ToString()
                            });
                        }
                    }
                    
                    // 可以继续读取其他结果集（统计数据）
                    reader.NextResult();
                    // 处理直落统计...
                    
                    reader.NextResult();
                    // 处理渠道统计...
                }
            }
        }
        
        return results;
    }
}
```

## 方案一优缺点分析

### 优点
1. **性能优异**：数据库层面处理，减少网络传输
2. **逻辑集中**：所有业务逻辑在存储过程中
3. **事务一致性**：可以保证数据一致性
4. **复用性强**：存储过程可被多个应用调用

### 缺点
1. **配置复杂**：需要配置链接服务器
2. **维护困难**：跨服务器依赖，故障排查复杂
3. **安全风险**：链接服务器需要存储密码
4. **扩展性差**：难以适应架构变化

### 适用场景
- 数据量大，对性能要求高
- 业务逻辑相对稳定
- 有专业DBA维护
- 对实时性要求高的场景

## 方案二：微服务 + 消息队列方案

### 2.1 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Message Queue  │    │  Data Service   │
│                 │    │    (Redis)      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Business Logic  │    │ Data Aggregator │    │ Cache Manager   │
│    Service      │    │    Service      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  RMS2019 DB     │    │ OperateData DB  │    │   Result DB     │
│ (*************) │    │ (***********)   │    │  (统一存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 数据库连接配置

```xml
<!-- 在Web.config或App.config中添加 -->
<connectionStrings>
  <!-- 现有rms2019连接 -->
  <add name="rms2019Entities"
       connectionString="Data Source=*************;Initial Catalog=dbfood;User ID=sa;Password=Musicbox@***;MultipleActiveResultSets=True;"
       providerName="System.Data.SqlClient" />

  <!-- 新增operatedata连接 -->
  <add name="operatedataEntities"
       connectionString="Data Source=***********;Initial Catalog=operatedata;User ID=sa;Password=Musicbox***;MultipleActiveResultSets=True;"
       providerName="System.Data.SqlClient" />

  <!-- 结果缓存数据库 -->
  <add name="resultCacheEntities"
       connectionString="Data Source=************;Initial Catalog=KtvBusinessCache;User ID=sa;Password=***;MultipleActiveResultSets=True;"
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 2.3 数据访问层实现

```csharp
// RMS2019数据访问
public class Rms2019DataService
{
    private readonly string _connectionString =
        ConfigurationManager.ConnectionStrings["rms2019Entities"].ConnectionString;

    public async Task<List<OpenCacheData>> GetOpenCacheDataAsync(DateTime date, int? shopId = null)
    {
        var sql = @"
            SELECT
                Invno,
                ShopId,
                RmNo,
                BookDateTime,
                CustName,
                Numbers,
                CtName,
                OrderUserName
            FROM opencacheinfo
            WHERE CAST(BookDateTime AS DATE) = @Date
                AND Invno IS NOT NULL
                AND Invno != ''
                AND (@ShopId IS NULL OR ShopId = @ShopId)
            ORDER BY BookDateTime";

        using (var connection = new SqlConnection(_connectionString))
        {
            return (await connection.QueryAsync<OpenCacheData>(sql, new { Date = date.Date, ShopId = shopId })).ToList();
        }
    }
}

// OperateData数据访问
public class OperateDataService
{
    private readonly string _connectionString =
        ConfigurationManager.ConnectionStrings["operatedataEntities"].ConnectionString;

    public async Task<List<RmCloseData>> GetCloseDataAsync(List<string> invNos)
    {
        if (!invNos.Any()) return new List<RmCloseData>();

        var sql = @"
            SELECT
                InvNo,
                Cash,
                Cash_Targ,
                Vesa,
                VesaName,
                VesaNo,
                Vesa_Targ,
                WXPay,
                OpenId,
                GZ,
                GZName
            FROM rmcloseinfo
            WHERE InvNo IN @InvNos";

        using (var connection = new SqlConnection(_connectionString))
        {
            return (await connection.QueryAsync<RmCloseData>(sql, new { InvNos = invNos })).ToList();
        }
    }
}
```

### 2.4 业务逻辑服务

```csharp
public class KtvBusinessAnalysisService
{
    private readonly Rms2019DataService _rmsService;
    private readonly OperateDataService _operateService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<KtvBusinessAnalysisService> _logger;

    public KtvBusinessAnalysisService(
        Rms2019DataService rmsService,
        OperateDataService operateService,
        IMemoryCache cache,
        ILogger<KtvBusinessAnalysisService> logger)
    {
        _rmsService = rmsService;
        _operateService = operateService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<KtvBusinessReport> GetBusinessReportAsync(DateTime date, int? shopId = null)
    {
        var cacheKey = $"ktv_business_{date:yyyyMMdd}_{shopId}";

        // 检查缓存
        if (_cache.TryGetValue(cacheKey, out KtvBusinessReport cachedReport))
        {
            return cachedReport;
        }

        try
        {
            // 并行获取数据
            var openDataTask = _rmsService.GetOpenCacheDataAsync(date, shopId);
            var openData = await openDataTask;

            var invNos = openData.Where(x => !string.IsNullOrEmpty(x.Invno))
                                .Select(x => x.Invno).ToList();

            var closeData = await _operateService.GetCloseDataAsync(invNos);

            // 数据关联和分析
            var businessData = ProcessBusinessData(openData, closeData);

            // 生成报告
            var report = GenerateBusinessReport(businessData, date);

            // 缓存结果（缓存1小时）
            _cache.Set(cacheKey, report, TimeSpan.FromHours(1));

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取KTV营业数据失败: {Date}, {ShopId}", date, shopId);
            throw;
        }
    }

    private List<KtvBusinessDataModel> ProcessBusinessData(
        List<OpenCacheData> openData,
        List<RmCloseData> closeData)
    {
        var closeDict = closeData.ToDictionary(x => x.InvNo);

        return openData.Select(open => new KtvBusinessDataModel
        {
            InvNo = open.Invno,
            ShopId = open.ShopId,
            RmNo = open.RmNo,
            OpenTime = open.BookDateTime,
            CustName = open.CustName,
            Numbers = open.Numbers,
            CashAmount = closeDict.ContainsKey(open.Invno) ? closeDict[open.Invno].Cash : 0,
            VesaAmount = closeDict.ContainsKey(open.Invno) ? closeDict[open.Invno].Vesa : 0,
            WXPayAmount = closeDict.ContainsKey(open.Invno) ? closeDict[open.Invno].WXPay : 0,
            TotalAmount = closeDict.ContainsKey(open.Invno) ?
                         CalculateTotalAmount(closeDict[open.Invno]) : 0,
            IsDirect = closeDict.ContainsKey(open.Invno),
            PaymentMethod = GetPaymentMethod(closeDict.GetValueOrDefault(open.Invno)),
            ChannelType = GetChannelType(closeDict.GetValueOrDefault(open.Invno))
        }).ToList();
    }

    private KtvBusinessReport GenerateBusinessReport(List<KtvBusinessDataModel> businessData, DateTime date)
    {
        return new KtvBusinessReport
        {
            ReportDate = date,
            BusinessData = businessData,
            DirectAnalysis = AnalyzeDirectFall(businessData),
            ChannelAnalysis = AnalyzeChannels(businessData),
            TimeSlotAnalysis = AnalyzeTimeSlots(businessData),
            Summary = GenerateSummary(businessData)
        };
    }

    private DirectFallAnalysis AnalyzeDirectFall(List<KtvBusinessDataModel> data)
    {
        var totalOrders = data.Count;
        var directOrders = data.Count(x => x.IsDirect);

        return new DirectFallAnalysis
        {
            TotalOrders = totalOrders,
            DirectOrders = directOrders,
            NonDirectOrders = totalOrders - directOrders,
            DirectRate = totalOrders > 0 ? (decimal)directOrders / totalOrders * 100 : 0,
            DirectAmount = data.Where(x => x.IsDirect).Sum(x => x.TotalAmount),
            NonDirectAmount = data.Where(x => !x.IsDirect).Sum(x => x.TotalAmount)
        };
    }

    private List<ChannelAnalysis> AnalyzeChannels(List<KtvBusinessDataModel> data)
    {
        return data.GroupBy(x => x.ChannelType)
                  .Select(g => new ChannelAnalysis
                  {
                      ChannelType = g.Key,
                      OrderCount = g.Count(),
                      TotalAmount = g.Sum(x => x.TotalAmount),
                      AvgAmount = g.Average(x => x.TotalAmount),
                      Percentage = data.Count > 0 ? (decimal)g.Count() / data.Count * 100 : 0
                  })
                  .OrderByDescending(x => x.TotalAmount)
                  .ToList();
    }

    private List<TimeSlotAnalysis> AnalyzeTimeSlots(List<KtvBusinessDataModel> data)
    {
        return data.Where(x => x.OpenTime.HasValue)
                  .GroupBy(x => GetTimeSlot(x.OpenTime.Value))
                  .Select(g => new TimeSlotAnalysis
                  {
                      TimeSlot = g.Key,
                      OrderCount = g.Count(),
                      TotalAmount = g.Sum(x => x.TotalAmount),
                      DirectCount = g.Count(x => x.IsDirect)
                  })
                  .OrderByDescending(x => x.TotalAmount)
                  .ToList();
    }

    private string GetTimeSlot(DateTime time)
    {
        var hour = time.Hour;
        return hour switch
        {
            >= 10 and <= 13 => "上午(10-13)",
            >= 14 and <= 17 => "下午(14-17)",
            >= 18 and <= 21 => "晚上(18-21)",
            >= 22 or <= 2 => "深夜(22-02)",
            _ => "其他时段"
        };
    }

    private decimal CalculateTotalAmount(RmCloseData closeData)
    {
        return (closeData?.Cash ?? 0) + (closeData?.Vesa ?? 0) + (closeData?.WXPay ?? 0);
    }

    private string GetPaymentMethod(RmCloseData closeData)
    {
        if (closeData == null) return "未支付";

        if (closeData.WXPay > 0) return "微信支付";
        if (closeData.Vesa > 0) return "会员卡";
        if (closeData.Cash > 0) return "现金";
        return "未支付";
    }

    private string GetChannelType(RmCloseData closeData)
    {
        if (closeData == null) return "其他";

        if (closeData.WXPay > 0) return "线上";
        if (closeData.Vesa > 0) return "会员";
        if (closeData.Cash > 0) return "现金";
        return "其他";
    }
}
```

### 2.5 API控制器

```csharp
[ApiController]
[Route("api/[controller]")]
public class KtvBusinessController : ControllerBase
{
    private readonly KtvBusinessAnalysisService _businessService;
    private readonly ILogger<KtvBusinessController> _logger;

    public KtvBusinessController(
        KtvBusinessAnalysisService businessService,
        ILogger<KtvBusinessController> logger)
    {
        _businessService = businessService;
        _logger = logger;
    }

    [HttpGet("daily-report")]
    public async Task<ActionResult<KtvBusinessReport>> GetDailyReport(
        [FromQuery] DateTime? date = null,
        [FromQuery] int? shopId = null)
    {
        try
        {
            var queryDate = date ?? DateTime.Today.AddDays(-1);
            var report = await _businessService.GetBusinessReportAsync(queryDate, shopId);
            return Ok(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日报失败");
            return StatusCode(500, "服务器内部错误");
        }
    }

    [HttpGet("direct-analysis")]
    public async Task<ActionResult<DirectFallAnalysis>> GetDirectAnalysis(
        [FromQuery] DateTime? date = null,
        [FromQuery] int? shopId = null)
    {
        try
        {
            var queryDate = date ?? DateTime.Today.AddDays(-1);
            var report = await _businessService.GetBusinessReportAsync(queryDate, shopId);
            return Ok(report.DirectAnalysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取直落分析失败");
            return StatusCode(500, "服务器内部错误");
        }
    }

    [HttpGet("channel-analysis")]
    public async Task<ActionResult<List<ChannelAnalysis>>> GetChannelAnalysis(
        [FromQuery] DateTime? date = null,
        [FromQuery] int? shopId = null)
    {
        try
        {
            var queryDate = date ?? DateTime.Today.AddDays(-1);
            var report = await _businessService.GetBusinessReportAsync(queryDate, shopId);
            return Ok(report.ChannelAnalysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取渠道分析失败");
            return StatusCode(500, "服务器内部错误");
        }
    }
}
```

### 2.6 数据模型定义

```csharp
// 开台数据模型
public class OpenCacheData
{
    public string Invno { get; set; }
    public int ShopId { get; set; }
    public string RmNo { get; set; }
    public DateTime? BookDateTime { get; set; }
    public string CustName { get; set; }
    public int Numbers { get; set; }
    public string CtName { get; set; }
    public string OrderUserName { get; set; }
}

// 结账数据模型
public class RmCloseData
{
    public string InvNo { get; set; }
    public decimal Cash { get; set; }
    public decimal Cash_Targ { get; set; }
    public decimal Vesa { get; set; }
    public string VesaName { get; set; }
    public string VesaNo { get; set; }
    public decimal Vesa_Targ { get; set; }
    public decimal WXPay { get; set; }
    public string OpenId { get; set; }
    public decimal GZ { get; set; }
    public string GZName { get; set; }
}

// 业务数据模型
public class KtvBusinessDataModel
{
    public string InvNo { get; set; }
    public int ShopId { get; set; }
    public string RmNo { get; set; }
    public DateTime? OpenTime { get; set; }
    public string CustName { get; set; }
    public int Numbers { get; set; }
    public decimal CashAmount { get; set; }
    public decimal VesaAmount { get; set; }
    public decimal WXPayAmount { get; set; }
    public decimal TotalAmount { get; set; }
    public bool IsDirect { get; set; }
    public string PaymentMethod { get; set; }
    public string ChannelType { get; set; }
}

// 业务报告模型
public class KtvBusinessReport
{
    public DateTime ReportDate { get; set; }
    public List<KtvBusinessDataModel> BusinessData { get; set; }
    public DirectFallAnalysis DirectAnalysis { get; set; }
    public List<ChannelAnalysis> ChannelAnalysis { get; set; }
    public List<TimeSlotAnalysis> TimeSlotAnalysis { get; set; }
    public BusinessSummary Summary { get; set; }
}

// 直落分析模型
public class DirectFallAnalysis
{
    public int TotalOrders { get; set; }
    public int DirectOrders { get; set; }
    public int NonDirectOrders { get; set; }
    public decimal DirectRate { get; set; }
    public decimal DirectAmount { get; set; }
    public decimal NonDirectAmount { get; set; }
}

// 渠道分析模型
public class ChannelAnalysis
{
    public string ChannelType { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal AvgAmount { get; set; }
    public decimal Percentage { get; set; }
}

// 时段分析模型
public class TimeSlotAnalysis
{
    public string TimeSlot { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalAmount { get; set; }
    public int DirectCount { get; set; }
}

// 业务摘要模型
public class BusinessSummary
{
    public int TotalOrders { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AvgOrderAmount { get; set; }
    public int TotalCustomers { get; set; }
    public decimal DirectFallRate { get; set; }
}
```

### 2.7 依赖注入配置

```csharp
// Startup.cs 或 Program.cs
public void ConfigureServices(IServiceCollection services)
{
    // 注册数据服务
    services.AddScoped<Rms2019DataService>();
    services.AddScoped<OperateDataService>();
    services.AddScoped<KtvBusinessAnalysisService>();

    // 注册缓存
    services.AddMemoryCache();

    // 注册HTTP客户端（如果需要）
    services.AddHttpClient();

    // 注册日志
    services.AddLogging();

    // 注册控制器
    services.AddControllers();
}
```

## 方案二优缺点分析

### 优点
1. **架构清晰**：微服务架构，职责分离
2. **易于维护**：代码结构清晰，便于调试
3. **扩展性强**：可以独立扩展各个服务
4. **容错性好**：单个服务故障不影响整体
5. **技术灵活**：可以使用不同技术栈

### 缺点
1. **复杂度高**：需要管理多个服务
2. **网络开销**：多次网络调用
3. **一致性难保证**：分布式事务复杂
4. **运维成本高**：需要监控多个服务

### 适用场景
- 大型分布式系统
- 需要高可用性和扩展性
- 团队技术能力较强
- 对系统灵活性要求高

## 方案对比总结

| 对比项目 | 方案一(存储过程+链接服务器) | 方案二(微服务+消息队列) |
|---------|---------------------------|----------------------|
| 实现复杂度 | 中等 | 高 |
| 性能 | 优秀 | 良好 |
| 维护成本 | 中等 | 高 |
| 扩展性 | 差 | 优秀 |
| 容错性 | 差 | 优秀 |
| 开发效率 | 高 | 中等 |
| 运维成本 | 低 | 高 |

## 推荐方案

**对于当前需求，推荐使用方案一（存储过程+链接服务器）**，理由：

1. **快速实现**：可以快速满足业务需求
2. **性能优异**：数据库层面处理，性能最佳
3. **维护简单**：相对简单的架构，便于维护
4. **成本较低**：不需要大幅改造现有架构

**长期规划建议采用方案二**，当业务复杂度增加或需要更高的可扩展性时，可以逐步迁移到微服务架构。
```
