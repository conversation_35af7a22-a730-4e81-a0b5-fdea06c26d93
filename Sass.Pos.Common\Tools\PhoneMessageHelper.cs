﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Common.Tools
{
    public class PhoneMessageHelper
    {
        /// <summary>
        /// 会员扣费短信合成
        /// </summary>
        /// <returns></returns>
        public static string MemberDeducty(MemberDeductyModel context)
        {
            var AdvStr = "\r\n会员特权！您可用会员卡返还账户内金额，兑换抵扣任意房型的房费。";
            StringBuilder message = new StringBuilder();
            string AfterFour = context.MemberCardNo.Substring(context.MemberCardNo.Length - 4);//会员卡后四位 

            if (context.ReturnUseTot > 0 && context.RechargeUseTot > 0)
            {
                message.Append("，使用充值帐户支付" + context.RechargeUseTot + "元、帐户余额" + context.RechargeTot + "元");
                message.Append("。同时使用房费帐户支付" + context.RechargeUseTot + "元、房费帐户余额" + context.ReturnTot + "元");
            }
            else if (context.ReturnUseTot > 0)
            {
                message.Append("，使用房费帐户支付" + context.ReturnUseTot + "元、房费帐户余额" + context.ReturnTot + "元");
            }
            else if (context.RechargeUseTot > 0)
            {
                message.Append("，使用充值帐户支付" + context.RechargeUseTot + "元、帐户余额" + context.RechargeTot + "元");
            }

            var msgStr = string.Format("亲爱的{0}！您末4位{1}的{2}本日{3}于我司{4}消费{5}元{6}。{7}{8}", context.MemberName, AfterFour, context.MemberCardTypeName, DateTime.Now.ToString("HH时mm分"), context.ShopName, context.Tot, message.ToString(), "", AdvStr);

            return msgStr;
        }
    }

    public class MemberDeductyModel
    {
        /// <summary>
        /// 会员卡号
        /// </summary>
        public string MemberCardNo { get; set; }
        /// <summary>
        /// 会员名称
        /// </summary>
        public string MemberName { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string ShopName { get; set; }
        /// <summary>
        /// 会员卡等级
        /// </summary>
        public string MemberCardTypeName { get; set; }
        /// <summary>
        /// 会员卡积分余额
        /// </summary>
        public int IntergalTot { get; set; }
        /// <summary>
        /// 会员卡充值账户余额
        /// </summary>
        public int RechargeTot { get; set; }
        /// <summary>
        /// 会员卡房费账户余额
        /// </summary>
        public int ReturnTot { get; set; }
        /// <summary>
        /// 账单总额
        /// </summary>
        public int Tot { get; set; }
        /// <summary>
        /// 本次消费积分
        /// </summary>
        public int IntergalValue { get; set; }
        /// <summary>
        /// 房费返还比例
        /// </summary>
        public int ReturnRule { get; set; }
        /// <summary>
        /// 房费返还金额
        /// </summary>
        public int ReturnValue { get; set; }
        /// <summary>
        /// 房费使用金额
        /// </summary>
        public int ReturnUseTot { get; set; }
        /// <summary>
        /// 充值使用金额
        /// </summary>
        public int RechargeUseTot { get; set; }
    }
}
