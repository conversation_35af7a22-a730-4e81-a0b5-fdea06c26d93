﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Response
{
    public class GetAccesstokenResponse
    {
        /// <summary>
        /// 返回code
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 提示信息
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 即为session过期时间为30天一校验
        /// </summary>
        public string access_token { get; set; }
        /// <summary>
        /// 过期时间,秒，如10000秒之后过期
        /// </summary>
        public int expires_in { get; set; }
        /// <summary>
        /// 剩余刷新次数
        /// </summary>
        public int remain_refresh_count { get; set; }
        /// <summary>
        /// 返回bearer
        /// </summary>
        public string tokenType { get; set; }
        /// <summary>
        /// session的权限范围，对应模块名称
        /// </summary>
        public string scope { get; set; }
        /// <summary>
        /// 客户Id，注：bid不是open_shop_uuid
        /// </summary>
        public string bid { get; set; }
        /// <summary>
        /// 即为refresh_session，授权可刷新次数用完后，将不再返回新的refresh_session
        /// </summary>
        public string refresh_token { get; set; }
    }
}
