﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.Coupons.Context;
using Saas.Pos.Model.Coupons.Model;
using Saas.Pos.Model.GrouponBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IRepository.GrouponBase
{
    public partial interface INDistributeSetGrouponRepository : IRepositoryBase<NDistributeSetGroupon>
    {
        List<GetCustomCouponDataModel> GetCustomCoupon(GetCustomCouponDataContext context);
    }
}
