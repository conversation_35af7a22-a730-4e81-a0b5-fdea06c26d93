﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Rms.NumberPool
{
    public abstract class NumberPoolServiceBase
    {
        public NumberPoolServiceBase(DateTime bookDate)
        {
            BookDate = bookDate;
        }

        /// <summary>
        /// 预约日期
        /// </summary>
        protected DateTime BookDate { get; set; }

        public abstract string GenerateNumber();

        /// <summary>
        /// 检查数字是否符合要求，是否可以加入
        /// </summary>
        /// <param name="existNumbers"></param>
        /// <param name="number"></param>
        /// <returns></returns>
        protected virtual bool Inspect(List<int> existNumbers, int number)
        {
            if (existNumbers == null || existNumbers.Count <= 0)
                return true;

            if (existNumbers.Any(w => w == number))
                return false;
            else
                return true;
        }
    }
}
