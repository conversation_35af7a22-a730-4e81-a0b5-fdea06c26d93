﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IFdCashOrderRepository : IRepositoryBase<FdCashOrder>
    {
        List<GetFdCashOrderInfoModel> GetCashOrderInfoByFdNo(GetFdCashOrderInfoContext context);

        List<GetFdCashOrderAndFdInfoModel> GetFdCashOrderData(GetFdCashOrderDataContext context);
    }
}
