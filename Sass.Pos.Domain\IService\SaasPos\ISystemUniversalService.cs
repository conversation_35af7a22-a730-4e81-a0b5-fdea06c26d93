﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface ISystemUniversalService
    {
        [OperationContract]
        ResponseContext<bool> QueueSend(QueueSendContext context);


        ResponseContext<object> GetKeysValue(GetValueContext context);

        ResponseContext<bool> SetKeysValue(SetValueContext context);

        [OperationContract]
        ResponseContext<ReturnInt> ChangeErrorStatus(ChangeErrorStatusContext context);

        [OperationContract]
        Sys_FileDownLoad InsertDownLoad(ExportExcelExContext context);

        [OperationContract]
        int EditDownLoad(long filesize, string path, Sys_FileDownLoad fileData);

        [OperationContract]
        ResponseContext<bool> RefreshBasicData(RefreshBasicDataContext context);
    }
}
