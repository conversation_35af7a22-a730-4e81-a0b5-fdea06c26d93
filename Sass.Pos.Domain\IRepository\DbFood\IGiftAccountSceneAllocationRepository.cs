﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IGiftAccountSceneAllocationRepository :IRepositoryBase<GiftAccountSceneAllocation>
    {
        GiftAccountSceneInfo GetAccountScene(GetAccountSceneContext context);

        AccountSceneInfo GetAccountSceneInfo(GetAccountSceneContext context);
    }
}
