﻿using Saas.Pos.Drive.Common;
using Saas.Pos.Drive.Lib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    public class WineStorkManageSubDriveBase<T> : SubDriveBase<T> where T : class
    {
        public WineStorkManageSubDriveBase(T holderBase, AppSession app) : base(holderBase, app)
        {
        }
    }
}
