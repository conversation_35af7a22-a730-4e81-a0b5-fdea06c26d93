﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface ICouponManageService
    {
        /// <summary>
        /// 用户查询最优优惠卷信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<GetCouponExDataModel> GetUserMostCouponData(GetCouponCamDataContext context);

        /// <summary>
        /// 用户查询优惠卷信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<List<GetCouponExDataModel>> GetUserCouponData(GetCouponDataContext context);

        /// <summary>
        /// 优惠卷派发
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnInt> CouponDataDistribute(CouponDataDistribute context);

        [OperationContract]
        ResponseContext<ReturnInt> CouponDataDistributeEx(CouponDataDistributeEx context);

        [OperationContract]
        ResponseContext<EventsDistributeModel> EventsDistribute(EventsDistributeContext context);

        /// <summary>
        /// 管理平台查询核销记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<RespPaginationModel<GetUseRecordDataModel>> GetUseRecordData(GetUseRecordDataContext context);

        /// <summary>
        /// 管理平台查询优惠卷信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<RespPaginationModel<GetManageCouponDataModel>> GetManageCouponData(GetManageCouponDataContext context);

        /// <summary>
        /// 管理平台查询优惠卷规则信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<RespPaginationModel<GetCouponRuleModel>> GetCouponRuleData(GetCouponRuleContext context);

        /// <summary>
        /// 查询优惠卷活动信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<CamCouponDataModel> GetCamCouponData(CamCouponDataContext context);

        /// <summary>
        /// 根据活动规则派发优惠卷
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<CamCouponDisByRuleModel> CamCouponDisByRule(CamCouponDataExContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetDistributeRecordDataModel>> GetDistributeRecordData(GetDistributeRecordDataContext context);

        [OperationContract]
        ResponseContext<List<GetMiddleDataModel>> GetMiddleData(GetMiddleDataContext context);

        [OperationContract]
        ResponseContext<ReturnBool> DeleteCouponData(DeleteCouponDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<Activity_AttendRecord>> GetActivityAttendList(GetActivityAttendListContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetActivityDataModel>> GetActivityData(GetActivityDataContext context);
    }
}
