﻿
using ComponentApplicationServiceInterface.Web;
using Saas.Pos.Model.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
 public partial class Activity_AttendRecordApp : AppBase<Activity_AttendRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Activity_AttendRecord> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Activity_AttendRecord;
        }
   
        
 
 }
  

 public partial class Commission_EmpRecordApp : AppBase<Commission_EmpRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Commission_EmpRecord> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Commission_EmpRecord;
        }
   
        
 
 }
  

 public partial class Commission_PermitInfoApp : AppBase<Commission_PermitInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Commission_PermitInfo> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Commission_PermitInfo;
        }
   
        
 
 }
  

 public partial class Commission_PermitItemApp : AppBase<Commission_PermitItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Commission_PermitItem> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Commission_PermitItem;
        }
   
        
 
 }
  

 public partial class Commission_PermitSchemesConfigApp : AppBase<Commission_PermitSchemesConfig> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Commission_PermitSchemesConfig> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Commission_PermitSchemesConfig;
        }
   
        
 
 }
  

 public partial class Commission_SchemesInfoApp : AppBase<Commission_SchemesInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Commission_SchemesInfo> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Commission_SchemesInfo;
        }
   
        
 
 }
  

 public partial class Coupon_And_ProApp : AppBase<Coupon_And_Pro> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_And_Pro> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_And_Pro;
        }
   
        
 
 }
  

 public partial class Coupon_CampaignApp : AppBase<Coupon_Campaign> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Campaign> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Campaign;
        }
   
        
 
 }
  

 public partial class Coupon_Campaign_ClaimApp : AppBase<Coupon_Campaign_Claim> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Campaign_Claim> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Campaign_Claim;
        }
   
        
 
 }
  

 public partial class Coupon_Cancel_RecordApp : AppBase<Coupon_Cancel_Record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Cancel_Record> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Cancel_Record;
        }
   
        
 
 }
  

 public partial class Coupon_Claim_RecordApp : AppBase<Coupon_Claim_Record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Claim_Record> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Claim_Record;
        }
   
        
 
 }
  

 public partial class Coupon_InfoApp : AppBase<Coupon_Info> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Info> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Info;
        }
   
        
 
 }
  

 public partial class Coupon_MiddleApp : AppBase<Coupon_Middle> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Middle> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Middle;
        }
   
        
 
 }
  

 public partial class Coupon_RuleApp : AppBase<Coupon_Rule> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Rule> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Rule;
        }
   
        
 
 }
  

 public partial class Coupon_Use_RecordApp : AppBase<Coupon_Use_Record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Coupon_Use_Record> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Coupon_Use_Record;
        }
   
        
 
 }
  

 public partial class Energy_Metering_CategoryApp : AppBase<Energy_Metering_Category> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Energy_Metering_Category> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Energy_Metering_Category;
        }
   
        
 
 }
  

 public partial class Energy_Metering_CategoryItemApp : AppBase<Energy_Metering_CategoryItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Energy_Metering_CategoryItem> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Energy_Metering_CategoryItem;
        }
   
        
 
 }
  

 public partial class Energy_Metering_RecordApp : AppBase<Energy_Metering_Record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Energy_Metering_Record> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Energy_Metering_Record;
        }
   
        
 
 }
  

 public partial class Energy_Metering_RecordItemApp : AppBase<Energy_Metering_RecordItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Energy_Metering_RecordItem> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Energy_Metering_RecordItem;
        }
   
        
 
 }
  

 public partial class Order_AmountRecordApp : AppBase<Order_AmountRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Order_AmountRecord> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Order_AmountRecord;
        }
   
        
 
 }
  

 public partial class Order_ItemApp : AppBase<Order_Item> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Order_Item> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Order_Item;
        }
   
        
 
 }
  

 public partial class Order_RefundApp : AppBase<Order_Refund> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Order_Refund> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Order_Refund;
        }
   
        
 
 }
  

 public partial class Order_RelevanceApp : AppBase<Order_Relevance> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Order_Relevance> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Order_Relevance;
        }
   
        
 
 }
  

 public partial class Payment_DataApp : AppBase<Payment_Data> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Payment_Data> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Payment_Data;
        }
   
        
 
 }
  

 public partial class Pos_Storage_FdTypeApp : AppBase<Pos_Storage_FdType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos_Storage_FdType> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Pos_Storage_FdType;
        }
   
        
 
 }
  

 public partial class Pos_Storage_FoodApp : AppBase<Pos_Storage_Food> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Pos_Storage_Food> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Pos_Storage_Food;
        }
   
        
 
 }
  

 public partial class ShopApp : AppBase<Shop> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop;
        }
   
        
 
 }
  

 public partial class Shop_Basic_ServiceApp : AppBase<Shop_Basic_Service> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_Basic_Service> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_Basic_Service;
        }
   
        
 
 }
  

 public partial class Shop_BookCache_BlackListApp : AppBase<Shop_BookCache_BlackList> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BookCache_BlackList> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BookCache_BlackList;
        }
   
        
 
 }
  

 public partial class Shop_BookCacheInfoApp : AppBase<Shop_BookCacheInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BookCacheInfo> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BookCacheInfo;
        }
   
        
 
 }
  

 public partial class Shop_BookCaheOpenInfoApp : AppBase<Shop_BookCaheOpenInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BookCaheOpenInfo> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BookCaheOpenInfo;
        }
   
        
 
 }
  

 public partial class Shop_BookGoodApp : AppBase<Shop_BookGood> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BookGood> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BookGood;
        }
   
        
 
 }
  

 public partial class Shop_BookPermitApp : AppBase<Shop_BookPermit> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BookPermit> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BookPermit;
        }
   
        
 
 }
  

 public partial class Shop_BookRtInfoApp : AppBase<Shop_BookRtInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BookRtInfo> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BookRtInfo;
        }
   
        
 
 }
  

 public partial class Shop_BTime_NoticeApp : AppBase<Shop_BTime_Notice> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BTime_Notice> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BTime_Notice;
        }
   
        
 
 }
  

 public partial class Shop_BusinessTimeApp : AppBase<Shop_BusinessTime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_BusinessTime> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_BusinessTime;
        }
   
        
 
 }
  

 public partial class Shop_GoodsApp : AppBase<Shop_Goods> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_Goods> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_Goods;
        }
   
        
 
 }
  

 public partial class Shop_GoodsImageApp : AppBase<Shop_GoodsImage> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_GoodsImage> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_GoodsImage;
        }
   
        
 
 }
  

 public partial class Shop_GoodsPayMethodApp : AppBase<Shop_GoodsPayMethod> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_GoodsPayMethod> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_GoodsPayMethod;
        }
   
        
 
 }
  

 public partial class Shop_GoodsSkuApp : AppBase<Shop_GoodsSku> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_GoodsSku> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_GoodsSku;
        }
   
        
 
 }
  

 public partial class Shop_ModeLinkApp : AppBase<Shop_ModeLink> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_ModeLink> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_ModeLink;
        }
   
        
 
 }
  

 public partial class Shop_Time_ModeApp : AppBase<Shop_Time_Mode> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_Time_Mode> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_Time_Mode;
        }
   
        
 
 }
  

 public partial class Shop_Time_ModeItemApp : AppBase<Shop_Time_ModeItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_Time_ModeItem> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_Time_ModeItem;
        }
   
        
 
 }
  

 public partial class Shop_TimeRtInfoDepositApp : AppBase<Shop_TimeRtInfoDeposit> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Shop_TimeRtInfoDeposit> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Shop_TimeRtInfoDeposit;
        }
   
        
 
 }
  

 public partial class Sys_CallBackErrorApp : AppBase<Sys_CallBackError> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Sys_CallBackError> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Sys_CallBackError;
        }
   
        
 
 }
  

 public partial class Sys_FileDownLoadApp : AppBase<Sys_FileDownLoad> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Sys_FileDownLoad> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Sys_FileDownLoad;
        }
   
        
 
 }
  

 public partial class User_CouponApp : AppBase<User_Coupon> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<User_Coupon> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.User_Coupon;
        }
   
        
 
 }
  

 public partial class User_Coupon_EventsApp : AppBase<User_Coupon_Events> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<User_Coupon_Events> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.User_Coupon_Events;
        }
   
        
 
 }
  

 public partial class User_OrderApp : AppBase<User_Order> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<User_Order> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.User_Order;
        }
   
        
 
 }
  

 public partial class User_Order_ReturnApp : AppBase<User_Order_Return> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<User_Order_Return> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.User_Order_Return;
        }
   
        
 
 }
  

 public partial class Way_FoodMapApp : AppBase<Way_FoodMap> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Way_FoodMap> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Way_FoodMap;
        }
   
        
 
 }
  

 public partial class Way_StoreMapApp : AppBase<Way_StoreMap> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Way_StoreMap> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Way_StoreMap;
        }
   
        
 
 }
  

 public partial class Way_Verify_FoodItemApp : AppBase<Way_Verify_FoodItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Way_Verify_FoodItem> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Way_Verify_FoodItem;
        }
   
        
 
 }
  

 public partial class Way_Verify_LinkApp : AppBase<Way_Verify_Link> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Way_Verify_Link> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Way_Verify_Link;
        }
   
        
 
 }
  

 public partial class Way_VerifyItemApp : AppBase<Way_VerifyItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Way_VerifyItem> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Way_VerifyItem;
        }
   
        
 
 }
  

 public partial class Way_VerifyRecordApp : AppBase<Way_VerifyRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Way_VerifyRecord> SetRepository(RepositoryFactory.T4.SaasPos.RepositorySession Session)
        {
            return Session.Way_VerifyRecord;
        }
   
        
 
 }
  

}
