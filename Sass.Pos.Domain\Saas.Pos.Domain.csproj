﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{901140E8-4C47-4B7A-9F14-1D2CD5335042}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Saas.Pos.Domain</RootNamespace>
    <AssemblyName>Saas.Pos.Domain</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentApplicationServiceInterface">
      <HintPath>..\Sass.Pos.Model\Packages\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="ComponentCore">
      <HintPath>..\Sass.Pos.Model\Packages\ComponentCore.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="IDrive\CouponManage\IActivityAttend.cs" />
    <Compile Include="IDrive\CouponManage\ICouponManageCampaign.cs" />
    <Compile Include="IDrive\CouponManage\ICouponManageCancel.cs" />
    <Compile Include="IDrive\CouponManage\ICouponManageDistribute.cs" />
    <Compile Include="IDrive\CouponManage\ICouponManageUse.cs" />
    <Compile Include="IDrive\DbFoodOrderMenu\IDbFoodOrderMenuSub.cs" />
    <Compile Include="IDrive\DbFood\Bill\IBillPrepaymentRecordDrive.cs" />
    <Compile Include="IDrive\DbFood\GiftOrderManage\IEmpGiftRecord.cs" />
    <Compile Include="IDrive\DbFood\GiftOrderManage\IGiftLimitConfigInfo.cs" />
    <Compile Include="IDrive\DbFood\GiftOrderManage\IGiftOrderAccount.cs" />
    <Compile Include="IDrive\DbFood\GiftOrderManage\IGiftOrderRole.cs" />
    <Compile Include="IDrive\DbFood\GiftOrderManage\IGiftOrderScene.cs" />
    <Compile Include="IDrive\DbFood\NewFoodManage\INewFdGiveDrive.cs" />
    <Compile Include="IDrive\DbFood\NewFoodManage\INewFdTypeDrive.cs" />
    <Compile Include="IDrive\MemBerManage\IMemBerCardManage.cs" />
    <Compile Include="IDrive\DbFood\Bill\IBillDetailsService.cs" />
    <Compile Include="IDrive\DbFood\Bill\IBillPaymentService.cs" />
    <Compile Include="IDrive\MemBerManage\IMemBerDeductMoney.cs" />
    <Compile Include="IDrive\MemBerManage\IMemBerDeductMoneyRule.cs" />
    <Compile Include="IDrive\MemBerManage\IMemBerRiskControl.cs" />
    <Compile Include="IDrive\MemberOpenUpManage\IMemberTopUp.cs" />
    <Compile Include="IDrive\OrderManage\ICommissionEmpRecord.cs" />
    <Compile Include="IDrive\OrderManage\ICommissionPermitSchemesConfig.cs" />
    <Compile Include="IDrive\OrderManage\ICommissionSchemes.cs" />
    <Compile Include="IDrive\OrderManage\IOrderManageReturn.cs" />
    <Compile Include="IDrive\OrderManage\IOrderShopMakeDayData.cs" />
    <Compile Include="IDrive\PosFoodManage\IPosFtTypeManage.cs" />
    <Compile Include="IDrive\ShopGood\IShopBookGoodRtInfo.cs" />
    <Compile Include="IDrive\ShopGood\IShopTimeRtInfoDeposit.cs" />
    <Compile Include="IDrive\StoreManage\IStoreManageBTimeNotice.cs" />
    <Compile Include="IDrive\OrderManage\IOrderManageCoupon.cs" />
    <Compile Include="IDrive\OrderManage\IOrderManageItem.cs" />
    <Compile Include="IDrive\OrderManage\IOrderManagePayment.cs" />
    <Compile Include="IDrive\OrderManage\IOrderManageRefund.cs" />
    <Compile Include="IDrive\ShopGood\IShopBookGoodPermit.cs" />
    <Compile Include="IDrive\StoreManage\IStoreManageBasicService.cs" />
    <Compile Include="IDrive\StoreManage\IStoreManageBusiness.cs" />
    <Compile Include="IDrive\ShopGood\IShopGoodsImage.cs" />
    <Compile Include="IDrive\ShopGood\IShopGoodSku.cs" />
    <Compile Include="IDrive\ShopGood\IShopGoodsPayMethod.cs" />
    <Compile Include="IDrive\ShopGood\IShopModeLink.cs" />
    <Compile Include="IDrive\WineStockManage\IDrCheckManage.cs" />
    <Compile Include="IDrive\WineStockManage\IFetchWineUser.cs" />
    <Compile Include="IDrive\WineStockManage\ISaveWineUser.cs" />
    <Compile Include="IDrive\WineStockManage\IWineData.cs" />
    <Compile Include="IDrive\WineStockManage\IWineStockReport.cs" />
    <Compile Include="IDrive\WineStockManage\IWineStockUser.cs" />
    <Compile Include="IRepository\Bar\IWineStockRepository.cs" />
    <Compile Include="IRepository\DbFood\IEmpGift_RecordRepository.cs" />
    <Compile Include="IRepository\DbFood\IFdCashBakRepository.cs" />
    <Compile Include="IRepository\DbFood\IFdCashOrderRepository.cs" />
    <Compile Include="IRepository\DbFood\IFdCashRepository.cs" />
    <Compile Include="IRepository\DbFood\IFdInvRepository.cs" />
    <Compile Include="IRepository\DbFood\IFdUserRepository.cs" />
    <Compile Include="IRepository\DbFood\IFoodLabelRepository.cs" />
    <Compile Include="IRepository\DbFood\IFoodRepository.cs" />
    <Compile Include="IRepository\DbFood\IGiftAccountRepository.cs" />
    <Compile Include="IRepository\DbFood\IGiftAccountSceneAllocationRepository.cs" />
    <Compile Include="IRepository\DbFood\IGiftRoleRepository.cs" />
    <Compile Include="IRepository\DbFood\IInv_TimeSectionRepository.cs" />
    <Compile Include="IRepository\DbFood\ILimit_ConfigInfoRepository.cs" />
    <Compile Include="IRepository\DbFood\IMobileFtTypeRepository.cs" />
    <Compile Include="IRepository\DbFood\IRoomRepository.cs" />
    <Compile Include="IRepository\DbFood\ISceneRole_ConfigRepository.cs" />
    <Compile Include="IRepository\DbFood\IUserInfo_BindingRepository.cs" />
    <Compile Include="IRepository\DbFood\IwxPayInfoRepository.cs" />
    <Compile Include="IRepository\DbFood\T4Repository.cs">
      <DependentUpon>T4Repository.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="IRepository\GrouponBase\INDistributeSetGrouponRepository.cs" />
    <Compile Include="IRepository\GrouponBase\INGrouponCodeInfoRepository.cs" />
    <Compile Include="IRepository\GrouponBase\T4Repository.cs">
      <DependentUpon>T4Repository.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="IRepository\MIMS\IMemberInfoRepository.cs" />
    <Compile Include="IRepository\MIMS\T4Repository.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>T4Repository.tt</DependentUpon>
    </Compile>
    <Compile Include="IRepository\Rms\Ibirthday_gift_recordRepository.cs" />
    <Compile Include="IRepository\Rms\IbookcacheinfoRepository.cs" />
    <Compile Include="IRepository\Rms\IdepositinfoRepository.cs" />
    <Compile Include="IRepository\Rms\IopencacheinfoRepository.cs" />
    <Compile Include="IRepository\Rms\IprintrecordRepository.cs" />
    <Compile Include="IRepository\Rms\IshoporderinfoRepository.cs" />
    <Compile Include="IRepository\Rms\IshoptimeinfoRepository.cs" />
    <Compile Include="IRepository\Rms\T4Repository.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>T4Repository.tt</DependentUpon>
    </Compile>
    <Compile Include="IRepository\SaasPos\IActivity_AttendRecordRepository.cs" />
    <Compile Include="IRepository\SaasPos\ICommission_EmpRecordRepository.cs" />
    <Compile Include="IRepository\SaasPos\ICommission_SchemesInfoRepository.cs" />
    <Compile Include="IRepository\SaasPos\ICoupon_InfoRepository.cs" />
    <Compile Include="IRepository\SaasPos\IEnergy_Metering_RecordRepository.cs" />
    <Compile Include="IRepository\SaasPos\IOrder_RelevanceRepository.cs" />
    <Compile Include="IRepository\SaasPos\IPos_Storage_FoodRepositoy.cs" />
    <Compile Include="IRepository\SaasPos\IShop_BookCacheInfoRepository.cs" />
    <Compile Include="IRepository\SaasPos\IShop_BookGoodRepository.cs" />
    <Compile Include="IRepository\SaasPos\IShop_TimeRtInfoDepositRepository.cs" />
    <Compile Include="IRepository\SaasPos\IUser_OrderRepository.cs" />
    <Compile Include="IRepository\SaasPos\T4Repository.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>T4Repository.tt</DependentUpon>
    </Compile>
    <Compile Include="IRepository\SaasPos\IWay_VerifyItemRepository.cs" />
    <Compile Include="IRepository\SongBase\ISongScanRepository.cs" />
    <Compile Include="IRepository\SongBase\T4Repository.cs">
      <DependentUpon>T4Repository.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="IService\BI\IMemberService.cs" />
    <Compile Include="IService\GrouponBase\IGrouponInfoService.cs" />
    <Compile Include="IService\IRmsService.cs" />
    <Compile Include="IService\MIMS\IBookingNewAttDayService.cs" />
    <Compile Include="IService\Pos\IBillHandlerService.cs" />
    <Compile Include="IService\IInternalCollaborationService.cs" />
    <Compile Include="IService\Pos\IConsumeBillService.cs" />
    <Compile Include="IService\Pos\IDbFdUserService.cs" />
    <Compile Include="IService\Pos\IDbFoodLabelService.cs" />
    <Compile Include="IService\Pos\IDbFoodService.cs" />
    <Compile Include="IService\Pos\IDbOrderService.cs" />
    <Compile Include="IService\Pos\IDbRoomService.cs" />
    <Compile Include="IService\Pos\IEmpGiftRecordService.cs" />
    <Compile Include="IService\Pos\IFdCashOrderService.cs" />
    <Compile Include="IService\Pos\IGiftLimitConfigInfoService.cs" />
    <Compile Include="IService\Pos\IMemberInfoService.cs" />
    <Compile Include="IService\Pos\INewFoodService.cs" />
    <Compile Include="IService\Pos\IOrderMenuService.cs" />
    <Compile Include="IService\Pos\IPosDbFoodService.cs" />
    <Compile Include="IService\Pos\ITimeFrameService.cs" />
    <Compile Include="IService\Pos\IUserBindingService.cs" />
    <Compile Include="IService\Pos\IWxPayInfoService.cs" />
    <Compile Include="IService\Rms\IRmsBookingService.cs" />
    <Compile Include="IService\Rms\IRmsOpeningService.cs" />
    <Compile Include="IService\Rms\IRmsCustomerService.cs" />
    <Compile Include="IService\Rms\IRmsReportService.cs" />
    <Compile Include="IService\SaasPos\ICouponManageService.cs" />
    <Compile Include="IService\SaasPos\IMemBerManageService.cs" />
    <Compile Include="IService\SaasPos\IOpenPlatformService.cs" />
    <Compile Include="IService\Pos\IPos.cs" />
    <Compile Include="IService\SaasPos\IOrderManageService.cs" />
    <Compile Include="IService\SaasPos\IPosFoodManageService.cs" />
    <Compile Include="IService\SaasPos\ISaasPosReport.cs" />
    <Compile Include="IService\ISaasPos.cs" />
    <Compile Include="IService\SaasPos\IShopBookCacheInfoService.cs" />
    <Compile Include="IService\SaasPos\IShopBookGoodService.cs" />
    <Compile Include="IService\SaasPos\IShopGoodService.cs" />
    <Compile Include="IService\SaasPos\ISongScanBillService.cs" />
    <Compile Include="IService\SaasPos\IStoreManageService.cs" />
    <Compile Include="IService\IVip.cs" />
    <Compile Include="IService\SaasPos\ISystemUniversalService.cs" />
    <Compile Include="IService\SaasPos\IUserActivateManageService.cs" />
    <Compile Include="IService\SaasPos\IWineStockService.cs" />
    <Compile Include="IService\WeChat\IWeChatService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="ISys\File\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="IRepository\DbFood\T4Repository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Repository.cs</LastGenOutput>
    </Content>
    <Content Include="IRepository\GrouponBase\T4Repository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Repository.cs</LastGenOutput>
    </Content>
    <Content Include="IRepository\MIMS\T4Repository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Repository.cs</LastGenOutput>
    </Content>
    <Content Include="IRepository\Rms\T4Repository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Repository.cs</LastGenOutput>
    </Content>
    <Content Include="IRepository\SaasPos\T4Repository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Repository.cs</LastGenOutput>
    </Content>
    <Content Include="IRepository\SongBase\T4Repository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Repository.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\external.open.library\external.open.library.csproj">
      <Project>{83EE90BB-211A-4CAA-86A8-9AA0FF223D92}</Project>
      <Name>external.open.library</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sass.Pos.Model\Saas.Pos.Model.csproj">
      <Project>{d3e4e488-5933-4b09-88d9-a5adbf6389ce}</Project>
      <Name>Saas.Pos.Model</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>