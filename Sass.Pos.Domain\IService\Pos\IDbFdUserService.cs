﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IDbFdUserService
    {
        [OperationContract]
        ResponseContext<AuthenUserModel> AuthenUser(AuthenUserContext context);

        [OperationContract]
        ResponseContext<AuthenUserModel> AuthenUserByStore(AuthenUserContext context);
    }
}
