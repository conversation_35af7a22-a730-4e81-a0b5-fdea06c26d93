﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IFoodLabelRepository : IRepositoryBase<FoodLabel>
    {
        int GetMaxCode();

        List<GetCheckOutModel> GetCheckOutData(GetCheckOutDataContext context);

        List<CheckOutFoodInfoModel> GetCheckOutFoodData(GetCheckOutFoodDataContext context);

        List<GetOrderMenuData> GetOrderMenuData(GetOrderMenuDataContext context);

        List<FdDataDetail> GetOrderMenuDataDetail(GetOrderMenuDataDetailContext context);

        List<GetFdTypeLinkDataModel> GetFdTypeLinkData(GetFdTypeLinkDataContext context);

        List<GetFoodLabelDataModel> GetFoodLabelData(GetFoodLabelDataContext context);
    }
}
