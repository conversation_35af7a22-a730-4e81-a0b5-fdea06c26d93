﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.General;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.DbFood.NewFoodManage
{
    public interface INewFdGiveDrive
    {
        ResponseContext<RespPaginationModel<GetNewFdGiveDataModel>> GetNewFdGiveDataByStore(GetNewFdGiveDataContext context);

        ResponseContext<EditNewFdGiveDataModel> EditNewFdGiveDataByStore(EditNewFdGiveDataContext context);

        ResponseContext<RespPaginationModel<GetNewFdGiveDataModel>> GetNewFdGiveData(GetNewFdGiveDataContext context);

        ResponseContext<EditNewFdGiveDataModel> EditNewFdGiveData(EditNewFdGiveDataContext context);

        ResponseContext<List<KeyValueModel>> GetSelectFdGiveDataByStore(GetSelectFdGiveDataContext context);

        ResponseContext<List<KeyValueModel>> GetSelectFdGiveData(GetSelectFdGiveDataContext context);
    }
}
