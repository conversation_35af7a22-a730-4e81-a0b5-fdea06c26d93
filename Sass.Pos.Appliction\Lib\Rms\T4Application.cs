﻿
using ComponentApplicationServiceInterface.Web;
using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
 public partial class api_messageApp : AppBase<api_message> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<api_message> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.api_message;
        }
   
        
 
 }
  

 public partial class areainfoApp : AppBase<areainfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<areainfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.areainfo;
        }
   
        
 
 }
  

 public partial class behapartinfoApp : AppBase<behapartinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<behapartinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.behapartinfo;
        }
   
        
 
 }
  

 public partial class BehatypeinfoApp : AppBase<Behatypeinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Behatypeinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.Behatypeinfo;
        }
   
        
 
 }
  

 public partial class billstatusApp : AppBase<billstatus> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<billstatus> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.billstatus;
        }
   
        
 
 }
  

 public partial class birthday_gift_recordApp : AppBase<birthday_gift_record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<birthday_gift_record> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.birthday_gift_record;
        }
   
        
 
 }
  

 public partial class birthday_gift_record_detailedApp : AppBase<birthday_gift_record_detailed> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<birthday_gift_record_detailed> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.birthday_gift_record_detailed;
        }
   
        
 
 }
  

 public partial class birthday_virtual_orderApp : AppBase<birthday_virtual_order> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<birthday_virtual_order> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.birthday_virtual_order;
        }
   
        
 
 }
  

 public partial class bookcacheinfoApp : AppBase<bookcacheinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<bookcacheinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.bookcacheinfo;
        }
   
        
 
 }
  

 public partial class bookcacheinfo_preorderApp : AppBase<bookcacheinfo_preorder> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<bookcacheinfo_preorder> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.bookcacheinfo_preorder;
        }
   
        
 
 }
  

 public partial class bookhistoryApp : AppBase<bookhistory> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<bookhistory> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.bookhistory;
        }
   
        
 
 }
  

 public partial class bookhistory_bakApp : AppBase<bookhistory_bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<bookhistory_bak> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.bookhistory_bak;
        }
   
        
 
 }
  

 public partial class bookhistory_bak202312081App : AppBase<bookhistory_bak202312081> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<bookhistory_bak202312081> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.bookhistory_bak202312081;
        }
   
        
 
 }
  

 public partial class bookmessageApp : AppBase<bookmessage> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<bookmessage> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.bookmessage;
        }
   
        
 
 }
  

 public partial class contypeinfoApp : AppBase<contypeinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<contypeinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.contypeinfo;
        }
   
        
 
 }
  

 public partial class contypetimeinfoApp : AppBase<contypetimeinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<contypetimeinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.contypetimeinfo;
        }
   
        
 
 }
  

 public partial class custbehainfoApp : AppBase<custbehainfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<custbehainfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.custbehainfo;
        }
   
        
 
 }
  

 public partial class custdemandApp : AppBase<custdemand> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<custdemand> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.custdemand;
        }
   
        
 
 }
  

 public partial class custdemandtitleApp : AppBase<custdemandtitle> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<custdemandtitle> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.custdemandtitle;
        }
   
        
 
 }
  

 public partial class CustInfoApp : AppBase<CustInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CustInfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.CustInfo;
        }
   
        
 
 }
  

 public partial class deposit_foodApp : AppBase<deposit_food> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<deposit_food> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.deposit_food;
        }
   
        
 
 }
  

 public partial class depositinfoApp : AppBase<depositinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<depositinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.depositinfo;
        }
   
        
 
 }
  

 public partial class exchangewaitApp : AppBase<exchangewait> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<exchangewait> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.exchangewait;
        }
   
        
 
 }
  

 public partial class heartbeatApp : AppBase<heartbeat> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<heartbeat> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.heartbeat;
        }
   
        
 
 }
  

 public partial class luserinfoApp : AppBase<luserinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<luserinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.luserinfo;
        }
   
        
 
 }
  

 public partial class msmsApp : AppBase<msms> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<msms> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.msms;
        }
   
        
 
 }
  

 public partial class MSpeer_conflictdetectionconfigrequest1App : AppBase<MSpeer_conflictdetectionconfigrequest1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MSpeer_conflictdetectionconfigrequest1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.MSpeer_conflictdetectionconfigrequest1;
        }
   
        
 
 }
  

 public partial class MSpeer_conflictdetectionconfigresponse1App : AppBase<MSpeer_conflictdetectionconfigresponse1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MSpeer_conflictdetectionconfigresponse1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.MSpeer_conflictdetectionconfigresponse1;
        }
   
        
 
 }
  

 public partial class MSpeer_lsns1App : AppBase<MSpeer_lsns1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MSpeer_lsns1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.MSpeer_lsns1;
        }
   
        
 
 }
  

 public partial class MSpeer_originatorid_history1App : AppBase<MSpeer_originatorid_history1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MSpeer_originatorid_history1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.MSpeer_originatorid_history1;
        }
   
        
 
 }
  

 public partial class MSpeer_request1App : AppBase<MSpeer_request1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MSpeer_request1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.MSpeer_request1;
        }
   
        
 
 }
  

 public partial class MSpeer_response1App : AppBase<MSpeer_response1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MSpeer_response1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.MSpeer_response1;
        }
   
        
 
 }
  

 public partial class mvodmsgApp : AppBase<mvodmsg> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<mvodmsg> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.mvodmsg;
        }
   
        
 
 }
  

 public partial class mvodroomApp : AppBase<mvodroom> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<mvodroom> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.mvodroom;
        }
   
        
 
 }
  

 public partial class NumberManageApp : AppBase<NumberManage> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NumberManage> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.NumberManage;
        }
   
        
 
 }
  

 public partial class opencacheinfoApp : AppBase<opencacheinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<opencacheinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.opencacheinfo;
        }
   
        
 
 }
  

 public partial class openhistoryApp : AppBase<openhistory> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<openhistory> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.openhistory;
        }
   
        
 
 }
  

 public partial class openhistory_bakApp : AppBase<openhistory_bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<openhistory_bak> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.openhistory_bak;
        }
   
        
 
 }
  

 public partial class operationApp : AppBase<operation> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<operation> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.operation;
        }
   
        
 
 }
  

 public partial class orderuserApp : AppBase<orderuser> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<orderuser> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.orderuser;
        }
   
        
 
 }
  

 public partial class pricemodelApp : AppBase<pricemodel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<pricemodel> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.pricemodel;
        }
   
        
 
 }
  

 public partial class printattributeApp : AppBase<printattribute> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<printattribute> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.printattribute;
        }
   
        
 
 }
  

 public partial class printrecordApp : AppBase<printrecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<printrecord> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.printrecord;
        }
   
        
 
 }
  

 public partial class rmexchangeApp : AppBase<rmexchange> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<rmexchange> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.rmexchange;
        }
   
        
 
 }
  

 public partial class rminfoApp : AppBase<rminfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<rminfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.rminfo;
        }
   
        
 
 }
  

 public partial class rminfoChangedApp : AppBase<rminfoChanged> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<rminfoChanged> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.rminfoChanged;
        }
   
        
 
 }
  

 public partial class RmOperationApp : AppBase<RmOperation> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmOperation> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.RmOperation;
        }
   
        
 
 }
  

 public partial class RmOperation_HistoryApp : AppBase<RmOperation_History> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmOperation_History> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.RmOperation_History;
        }
   
        
 
 }
  

 public partial class rmsmemberinfoApp : AppBase<rmsmemberinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<rmsmemberinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.rmsmemberinfo;
        }
   
        
 
 }
  

 public partial class rtinfoApp : AppBase<rtinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<rtinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.rtinfo;
        }
   
        
 
 }
  

 public partial class rtpriceApp : AppBase<rtprice> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<rtprice> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.rtprice;
        }
   
        
 
 }
  

 public partial class shopbookusedInfoApp : AppBase<shopbookusedInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<shopbookusedInfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.shopbookusedInfo;
        }
   
        
 
 }
  

 public partial class shopinfoApp : AppBase<shopinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<shopinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.shopinfo;
        }
   
        
 
 }
  

 public partial class shoporderinfoApp : AppBase<shoporderinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<shoporderinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.shoporderinfo;
        }
   
        
 
 }
  

 public partial class shoporderitemApp : AppBase<shoporderitem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<shoporderitem> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.shoporderitem;
        }
   
        
 
 }
  

 public partial class shoptimeinfoApp : AppBase<shoptimeinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<shoptimeinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.shoptimeinfo;
        }
   
        
 
 }
  

 public partial class sysarticlecolumns1App : AppBase<sysarticlecolumns1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<sysarticlecolumns1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.sysarticlecolumns1;
        }
   
        
 
 }
  

 public partial class sysarticles1App : AppBase<sysarticles1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<sysarticles1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.sysarticles1;
        }
   
        
 
 }
  

 public partial class sysarticleupdates1App : AppBase<sysarticleupdates1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<sysarticleupdates1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.sysarticleupdates1;
        }
   
        
 
 }
  

 public partial class syspublications1App : AppBase<syspublications1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<syspublications1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.syspublications1;
        }
   
        
 
 }
  

 public partial class sysschemaarticles1App : AppBase<sysschemaarticles1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<sysschemaarticles1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.sysschemaarticles1;
        }
   
        
 
 }
  

 public partial class syssubscriptions1App : AppBase<syssubscriptions1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<syssubscriptions1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.syssubscriptions1;
        }
   
        
 
 }
  

 public partial class systranschemas1App : AppBase<systranschemas1> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<systranschemas1> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.systranschemas1;
        }
   
        
 
 }
  

 public partial class timeinfoApp : AppBase<timeinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<timeinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.timeinfo;
        }
   
        
 
 }
  

 public partial class triggerRecordApp : AppBase<triggerRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<triggerRecord> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.triggerRecord;
        }
   
        
 
 }
  

 public partial class userinfoApp : AppBase<userinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<userinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.userinfo;
        }
   
        
 
 }
  

 public partial class workbookoutApp : AppBase<workbookout> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<workbookout> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.workbookout;
        }
   
        
 
 }
  

 public partial class workconfigApp : AppBase<workconfig> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<workconfig> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.workconfig;
        }
   
        
 
 }
  

 public partial class worknotcontypeApp : AppBase<worknotcontype> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<worknotcontype> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.worknotcontype;
        }
   
        
 
 }
  

 public partial class worknotrtinfoApp : AppBase<worknotrtinfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<worknotrtinfo> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.worknotrtinfo;
        }
   
        
 
 }
  

 public partial class worknotshopApp : AppBase<worknotshop> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<worknotshop> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.worknotshop;
        }
   
        
 
 }
  

 public partial class worknotshoptimeApp : AppBase<worknotshoptime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<worknotshoptime> SetRepository(RepositoryFactory.T4.Rms.RepositorySession Session)
        {
            return Session.worknotshoptime;
        }
   
        
 
 }
  

}
