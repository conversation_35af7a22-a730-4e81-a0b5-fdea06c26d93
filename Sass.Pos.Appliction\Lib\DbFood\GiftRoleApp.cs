﻿using Saas.Pos.Application.Lib.DbFood;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class GiftRoleApp : AppBase<GiftRole>
    {
        /// <summary>
        /// 查询角色信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetGiftRoleDataModel> GetGiftRoleData(GetGiftRoleDataContext context)
        {
            return Repository.GiftRole.GetGiftRoleData(context);
        }

        /// <summary>
        /// 查询场景角色绑定信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetSceneRoleBindDataModel> GetSceneRoleBindData(GetSceneRoleBindDataContext context)
        {
            return Repository.GiftRole.GetSceneRoleBindData(context);
        }

        /// <summary>
        /// 根据场景角色绑定ID查询下拉框信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetSceneRoleBindDataModel> GetSceneRoleBindByIdData(GetSceneRoleBindByIdDataContext context)
        {
            return Repository.GiftRole.GetSceneRoleBindByIdData(context);
        }
    }
}
