﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Request
{
    public class GetAccecctokenRequest
    {
        public string app_key { get; set; }

        public string app_secret { get; set; }
        public string grant_type { get; set; }
        public string auth_code { get; set; }
        public string redirect_url { get; set; }
    }


    public class RefreshAccecctokenRequest
    {
        public string app_key { get; set; }

        public string app_secret { get; set; }
        public string grant_type { get; set; }

        public string refresh_token { get; set; }
    }
}
