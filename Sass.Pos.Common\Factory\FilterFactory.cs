﻿using Castle.DynamicProxy;
using Saas.Pos.Common.Attributes.Filter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Factory
{
    /// <summary>
    /// 拦截器工厂类
    /// </summary>
    public class FilterFactory
    {
        static Dictionary<string, object> ClassProxy = new Dictionary<string, object>();

        /// <summary>
        /// 创建通用拦截器代理
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static T CreateClassProxy<T>(string type = null) where T : class
        {
            var classType = typeof(T);
            if (ClassProxy.ContainsKey(classType.Name))
                return (T)ClassProxy[classType.Name];
            else
            {
                ProxyGenerator generator = new ProxyGenerator();
                IInterceptor interceptor = null;
                if (type == null)
                {
                    interceptor = new GeneralInterceptor();
                }

                var instance = generator.CreateClassProxy<T>(interceptor);
                ClassProxy.Add(classType.Name, instance);
                return instance;
            }
        }
        /// <summary>
        /// 创建通用拦截器代理
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static T CreateGeneralClassProxy<T>() where T : class
        {
            var type = typeof(T);
            if (ClassProxy.ContainsKey(type.Name))
                return (T)ClassProxy[type.Name];
            else
            {
                ProxyGenerator generator = new ProxyGenerator();
                var instance = generator.CreateClassProxy<T>(new GeneralInterceptor());
                ClassProxy.Add(type.Name, instance);
                return instance;
            }
        }
    }
}
