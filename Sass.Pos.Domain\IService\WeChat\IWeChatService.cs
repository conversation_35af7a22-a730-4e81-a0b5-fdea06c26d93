﻿using Saas.Pos.Model.WeChat.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IWeChatService
    {
        [OperationContract]
        vxRedisData CheckOpenIdFollow(string openid);

        [OperationContract]
        int CheckSendMessage();
    }
}
