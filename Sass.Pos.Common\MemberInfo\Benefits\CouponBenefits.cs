﻿using Saas.Pos.Common.MemberInfo.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MemberInfo.Benefits
{
    public class CouponBenefits : BenefitsBase<ConponBenefit>
    {
        public CouponBenefits(BenefitConfigContext<ConponBenefit> _Coupons) : base(_Coupons)
        {
        }

        public override bool Check(GetMemberConfigContext context)
        {
            throw new NotImplementedException();
        }
    }
}
