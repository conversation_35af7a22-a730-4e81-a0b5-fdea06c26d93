﻿using external.open.library.TakeaWayPlatform.MeiTuan.Request;
using external.open.library.TakeaWayPlatform.MeiTuan.Response;
using external.open.library.TakeaWayPlatform.Model;
using Saas.Pos.Common.Log;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography;
using System.Text;

namespace external.open.library.TakeaWayPlatform.MeiTuan
{
    public class DeliveryMeituan : LocalLifeBase
    {
        private static GetAccesstokenResponse accesstokenResponse = null;
        private static readonly string SavePath = AppDomain.CurrentDomain.BaseDirectory + "\\meituan_accesstoken.txt";

        private static readonly string Appkey = "18ef0f76ec6517d7";
        private static readonly string Secret = "76daefac77366ed89ad79e4c26191a20526abea1";
        private static readonly string BaseUrl = "https://openapi.dianping.com/";

        public DeliveryMeituan()
        {
            //首次初始化 换取token
            if (!File.Exists(SavePath))
                throw new WayPlatformException("未获得商家授权，无法初始化Token信息，请先授权！");
            else
                accesstokenResponse = File.ReadAllText(SavePath).ToObject<GetAccesstokenResponse>();
        }

        public override List<ConsumeResponseModel> Consume(ConsumeModel consume)
        {
            string url = "router/tuangou/receipt/consume";
            ConsumeRequest request = new ConsumeRequest()
            {
                app_key = Appkey,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                session = accesstokenResponse.access_token,
                requestid = Guid.NewGuid().ToString(),
                receipt_code = consume.ReceiptCode,
                count = consume.Count,
                open_shop_uuid = consume.OpenShopId,
                app_shop_account = consume.ShopAccount,
                app_shop_accountname = consume.ShopName,
            };

            var response = new MeituanResponseBase<List<ConsumeResponse>>();
            Retry(() =>
            {
                //未发生异常该执行的逻辑
                //参数签名
                request.sign = GenerateSign(request);
                response = SendRequest<ConsumeRequest, MeituanResponseBase<List<ConsumeResponse>>>(BaseUrl + url, request);


                if (response.code != 200)
                    throw new WayPlatformException(response.msg);
                if (response.data.Count <= 0)
                    throw new WayPlatformException("核验失败，未返回相关数据！");
            }, () =>
            {
                //发生指定异常应该执行的逻辑
                RefreshAccestoken();
                request.session = accesstokenResponse.access_token;
            }, new List<string>() { "session已过期" }, 2);

            //获取用户卡券平均实际支付金额
            var customPayTypes = new List<long>() { 10, 23, 25, 26, 29 };
            //商家优惠
            var businessTypes = new List<long>() { 8, 17, 18, 22, 24, 28 };
            var payAmount = response.data.FirstOrDefault().payment_detail.Where(w => customPayTypes.Contains(w.amount_type)).Sum(w => w.amount);
            //商家优惠金额
            var businessAmount = response.data.FirstOrDefault().payment_detail.Where(w => businessTypes.Contains(w.amount_type)).Sum(w => w.amount);
            //平台优惠金额   排除用户实付与商家优惠，剩下的就是平台优惠
            var platformAmount = response.data.FirstOrDefault().payment_detail.Where(w => !customPayTypes.Contains(w.amount_type) && !businessTypes.Contains(w.amount_type)).Sum(w => w.amount);

            var result = response.data.Select(w => new ConsumeResponseModel()
            {
                OrderId = w.order_id,
                ReceiptCode = w.receipt_code,
                EncryptedCode = w.receipt_code,
                OpenShopId = w.open_shop_uuid,
                DealGroupId = w.dealgroup_id.HasValue ? w.dealgroup_id.Value.ToString() : string.Empty,
                ProductItemId = w.dealgroup_id.HasValue ? w.dealgroup_id.Value.ToString() : string.Empty,
                CertificateId = string.Empty,
                VerifyId = w.deal_id.HasValue ? w.deal_id.Value.ToString() : string.Empty,
                Result = 0,
                Count = 1,
                Title = w.deal_title,
                MerchantAmount = (decimal)w.deal_price,
                OriginalAmount = (decimal)w.deal_marketprice,
                BusDis = businessAmount,
                PlatformDis = platformAmount,
                PayAmount = payAmount,
            }).ToList();

            return result;
        }

        public override QueryCouponResponse QueryCoupon(QueryCouponModel query)
        {
            string url = "router/tuangou/receipt/prepare";

            var request = new MeituanQueryCouponRequest()
            {
                app_key = Appkey,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                session = accesstokenResponse.access_token,
                receipt_code = query.Code,
                open_shop_uuid = query.OpenShopId,
            };

            var response = new MeituanResponseBase<MeituanQueryCouponResponse>();
            Retry(() =>
            {
                request.sign = GenerateSign(request);

                response = SendRequest<MeituanQueryCouponRequest, MeituanResponseBase<MeituanQueryCouponResponse>>(BaseUrl + url, request);
                if (response.code != 200)
                    throw new WayPlatformException(response.msg);

            }, () =>
            {
                RefreshAccestoken();
                request.session = accesstokenResponse.access_token;
            }, new List<string>() { "session已过期" }, 2);

            return new QueryCouponResponse()
            {
                Status = response.data.count > 0 ? 1 : 2,
                Count = response.data.count,
                TotalCount = response.data.tgTimesCardFlag ? int.Parse(response.data.purchaseToConsumeRatio) : response.data.count,
                Code = response.data.receipt_code,
                EndTime = ConvertToDate(response.data.receiptEndDate),
                ThirdFdNos = new List<string>() { response.data.dealgroup_id.ToString() }
            };
        }

        public override ReverseConsumeResponseModel ReverseConsume(ReverseConsumeModel reverseConsume)
        {
            string url = "router/tuangou/receipt/reverseconsume";
            var request = new ReverseConsumeRequest()
            {
                app_key = Appkey,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                session = accesstokenResponse.access_token,
                receipt_code = reverseConsume.receipt_code,
                app_deal_id = reverseConsume.app_deal_id,
                app_shop_account = reverseConsume.ShopAccount,
                app_shop_accountname = reverseConsume.ShopName,
                open_shop_uuid = reverseConsume.OpenShopId,
            };

            var response = new MeituanResponseBase<List<ReverseConsumeResponse>>();

            Retry(() =>
            {
                request.sign = GenerateSign(request);

                response = SendRequest<ReverseConsumeRequest, MeituanResponseBase<List<ReverseConsumeResponse>>>(BaseUrl + url, request);
                if (response.code != 200)
                    throw new WayPlatformException(response.msg);
                if (response.data.Count <= 0)
                    throw new WayPlatformException("撤销核验失败，未返回相关数据！");

            }, () =>
            {
                RefreshAccestoken();
                request.session = accesstokenResponse.access_token;
            }, new List<string>() { "session已过期" }, 2);

            var reverse = response.data.FirstOrDefault();
            return new ReverseConsumeResponseModel()
            {
                ReceiptCode = reverse.receipt_code,
                DealGroupId = reverse.dealgroup_id.ToString(),
                DealId = reverse.deal_id.ToString(),
                OpenShopId = reverse.open_shop_uuid
            };
        }

        public override List<QueryProductResponse> QueryProduct(QueryProductModel model)
        {
            bool hasMore = true;
            string url = "tuangou/deal/queryshopdeal";

            var request = new QueryProductRequest()
            {
                app_key = Appkey,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                session = accesstokenResponse.access_token,
                open_shop_uuid = model.ShopId,
                source = 2,
                offset = 1,
                limit = 100
            };
            var respList = new List<MeituanQueryProductResponse>();

            while (hasMore)
            {
                Retry(() =>
                {
                    request.sign = GenerateSign(request);
                    var response = SendRequest<QueryProductRequest, MeituanResponseBase<List<MeituanQueryProductResponse>>>(BaseUrl + url, request);
                    if (response.code != 200)
                        throw new WayPlatformException(response.msg);

                    if (response.data.Count < 100)
                        hasMore = false;

                    respList.AddRange(response.data);
                    //查询完当前数据之后需要将页码加1
                    request.offset++;
                }, () =>
                {
                    RefreshAccestoken();
                    //刷新Token之后重新给request赋值
                    request.session = accesstokenResponse.access_token;
                }, new List<string>() { "session已过期" }, 2);
            }

            var list = respList.Select(w => new QueryProductResponse()
            {
                ProductId = w.dealgroup_id.ToString(),
                ProductName = w.title,
                ProductType = w.deal_type,
                OriginAmount = (decimal)w.marketprice,
                ActualAmount = (decimal)w.price,
                Status = w.dealgroup_status
            }).ToList();

            return list;
        }

        public override PrepareCouponModel Prepare(QueryCouponModel query)
        {
            if (query.Code.Contains("http"))
                throw new Exception("卡券号错误！");

            string url = "router/tuangou/receipt/prepare";
            var request = new MeituanQueryCouponRequest()
            {
                app_key = Appkey,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                session = accesstokenResponse.access_token,
                receipt_code = query.Code,
                open_shop_uuid = query.OpenShopId,
            };

            var response = new MeituanResponseBase<MeituanQueryCouponResponse>();
            Retry(() =>
            {
                request.sign = GenerateSign(request);

                response = SendRequest<MeituanQueryCouponRequest, MeituanResponseBase<MeituanQueryCouponResponse>>(BaseUrl + url, request);
                if (response.code != 200)
                    throw new WayPlatformException(response.msg);

            }, () =>
            {
                RefreshAccestoken();
                request.session = accesstokenResponse.access_token;
            }, new List<string>() { "session已过期" }, 2);

            //获取用户卡券平均实际支付金额
            var customPayTypes = new List<long>() { 10, 23, 25, 26, 29 };
            var payAmount = response.data.payment_detail.Where(w => customPayTypes.Contains(w.amount_type)).Sum(w => w.amount);
            //这里能取到卡券真实数量，这里可以平分，核销那边取不到，所以在核销外面对该平台单独平分
            payAmount = Math.Round(payAmount / response.data.count, 2);

            var result = new PrepareCouponModel()
            {
                OrderId = string.Empty,
                VerifyToken = string.Empty,
                certificates = new List<Certificate>()
                {
                    new Certificate(){
                        Code = response.data.receipt_code,
                        ProductId = response.data.dealgroup_id.ToString(),
                        EncryptedCode = string.Empty,
                        StartTime = DateTime.Now.AddMinutes(-1),
                        EndTime = ConvertToDate(response.data.receiptEndDate),
                        Count = response.data.count,
                        OriginalAmount = (decimal)response.data.deal_marketprice,
                        MerchantAmount = (decimal)response.data.deal_price,
                        PayAmount = payAmount,
                        Title = response.data.deal_title
                    }
                }
            };

            return result;
        }

        /// <summary>
        /// 获取token
        /// </summary>
        /// <returns></returns>
        public GetAccesstokenResponse GetAccestoken(string auth_code)
        {
            string url = "router/oauth/token";

            var postData = new GetAccecctokenRequest()
            {
                app_key = Appkey,
                app_secret = Secret,
                grant_type = "authorization_code",
                auth_code = auth_code,
                redirect_url = "http://www.zhiruichuang.top/Common/GetAuthCode",//与授权auth_code的时候的回调地址一模一样就可以了
            };

            var response = SendRequest<GetAccecctokenRequest, GetAccesstokenResponse>(BaseUrl + url, postData);
            if (response.code != 200)
                throw new WayPlatformException(response.msg);

            return response;
        }

        /// <summary>
        /// 刷新token
        /// </summary>
        /// <returns></returns>
        private void RefreshAccestoken()
        {
            string url = "router/oauth/token";

            var postData = new RefreshAccecctokenRequest()
            {
                app_key = Appkey,
                app_secret = Secret,
                grant_type = "refresh_token",
                refresh_token = accesstokenResponse.refresh_token
            };

            var response = SendRequest<RefreshAccecctokenRequest, GetAccesstokenResponse>(BaseUrl + url, postData);

            if (response.code != 200)
                throw new WayPlatformException(response.msg);

            //刷新相关重要数据
            accesstokenResponse.expires_in = response.expires_in;
            accesstokenResponse.access_token = response.access_token;
            accesstokenResponse.refresh_token = response.refresh_token;
            accesstokenResponse.remain_refresh_count = response.remain_refresh_count;

            File.WriteAllText(SavePath, accesstokenResponse.ToJson());
        }

        /// <summary>
        /// 公共Http请求发送地址
        /// </summary>
        /// <typeparam name="T">返回的具体类型</typeparam>
        /// <param name="url">请求地址</param>
        /// <param name="postData">请求参数</param>
        /// <param name="method">请求方式</param>
        /// <returns></returns>
        private OutputT SendRequest<InputT, OutputT>(string url, InputT postData, string method = "POST")
        {
            if (postData == null)
                throw new WayPlatformException("请求参数不能为空！");

            var guid = Guid.NewGuid().ToString();
            //本地日志记录核销
            LogHelper.Info("唯一记录：" + guid + "，请求地址：" + url + "，请求记录：" + postData.ToJson());
            StringBuilder builder = new StringBuilder();
            var type = typeof(InputT);
            var properties = type.GetProperties();
            builder.Append("?");
            foreach (var item in properties)
            {
                if (item.GetValue(postData, null) != null)
                {
                    builder.Append(Uri.EscapeDataString(item.Name).Replace("%20", "+") + "=" + Uri.EscapeDataString(item.GetValue(postData, null).ToString()).Replace("%20", "+"));
                    builder.Append("&");
                }
            }
            builder = builder.Remove(builder.Length - 1, 1);
            url += builder.ToString();

            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidation);
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)192 | (SecurityProtocolType)768 | (SecurityProtocolType)3072 | SecurityProtocolType.Tls;
            HttpWebRequest client = (HttpWebRequest)WebRequest.Create(url);
            try
            {
                client.Method = method;
                if (method == "POST")
                    client.ContentType = "application/x-www-form-urlencoded";

                WebResponse response = client.GetResponse();
                using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                {
                    string responseBody = reader.ReadToEnd();
                    if (string.IsNullOrEmpty(responseBody))
                        throw new Exception("未获取到响应信息！");

                    LogHelper.Info("唯一记录：" + guid + "，返回记录：" + responseBody);
                    var data = responseBody.ToObject<OutputT>();
                    return data;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("请求出现异常！" + ex.Message);
            }
        }

        private static string GenerateSign<T>(T t)
        {
            var type = typeof(T);
            var properties = type.GetProperties();
            var result = new Dictionary<string, string>();
            foreach (var item in properties)
            {
                //包括公共参数和业务参数，但除去sign参数和值为空的参数
                if (item.Name != "sign" && item.GetValue(t, null) != null)
                {
                    result.Add(item.Name, item.GetValue(t, null).ToString());
                }
            }

            return ModelToDic(result);
        }

        private static string ModelToDic(Dictionary<string, string> param)
        {
            List<string> array = new List<string>();
            foreach (var item in param)
            {
                array.Add(item.Key);
            }
            array = array.OrderBy(str => string.Concat(str.Select(w => ((int)w).ToString("D3")))).ToList();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append(Secret);
            foreach (var key in array)
            {
                stringBuilder.Append(key).Append(param[key]);
            }
            stringBuilder.Append(Secret);
            return GenMd5(stringBuilder.ToString());
        }

        private static string GenMd5(string info)
        {
            byte[] byteUserPwd = Encoding.UTF8.GetBytes(info);

            MD5CryptoServiceProvider myMd5 = new MD5CryptoServiceProvider();

            byte[] byteMd5UserPwd = myMd5.ComputeHash(byteUserPwd);

            return ByteArrayToHex(byteMd5UserPwd);
        }

        private static string ByteArrayToHex(byte[] bytes)
        {
            string returnStr = "";
            if (bytes != null)
            {
                for (int i = 0; i < bytes.Length; i++)
                {
                    returnStr += bytes[i].ToString("x2");
                }
            }
            return returnStr;
        }

        private static DateTime ConvertToDate(long timeStamp)
        {
            DateTimeOffset dateTimeOffset = new DateTimeOffset(1970, 1, 1, 0, 0, 0, TimeSpan.Zero).AddMilliseconds(timeStamp);
            return dateTimeOffset.LocalDateTime;
        }

        public override QueryOrderResponse QueryOrder(QueryOrderModel model)
        {
            var url = "router/order/query/info";
            GetOrderShareInfoRequest request = new GetOrderShareInfoRequest()
            {
                app_key = Appkey,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                session = accesstokenResponse.access_token,
                order_id = model.OrderId,
                type = "[1,2]"
            };

            var response = new MeituanResponseBase<GetOrderShareInfoResponse>();
            Retry(() =>
            {
                request.sign = GenerateSign(request);
                response = SendRequest<GetOrderShareInfoRequest, MeituanResponseBase<GetOrderShareInfoResponse>>(BaseUrl + url, request);
            }, () =>
            {
                //发生指定异常应该执行的逻辑
                RefreshAccestoken();
                request.session = accesstokenResponse.access_token;
            }, new List<string>() { "session已过期" }, 2);

            var data = new QueryOrderResponse()
            {
                OrderId = response.data.order.order_id.ToString(),
                Receipt = response.data.receipt_list.Select(w =>
                {
                    int status = 0;
                    if (w.status == 4 || w.status == 6)
                        status = 3;
                    else
                        status = w.status;

                    return new ReceiptInfo()
                    {
                        ReceiptCode = w.receipt_code,
                        Status = status,
                        PayAmount = 0
                    };
                }).ToList()
            };

            return data;
        }
    }
}
