﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.General;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IMemBerManageService
    {
        [OperationContract]
        ResponseContext<MemberInfoData> GetMemBerData(GetMemBerDataContext context);

        [OperationContract]
        ResponseContext<GetMemBerDetailDataModel> GetMemBerDetailData(GetMemBerDetailDataContext context);

        [OperationContract]
        ResponseContext<RepDeductMoneyModel> DeductMoney(MemBerDeductMoneyContext context);

        [OperationContract]
        ResponseContext<RepDeductMoneyModel> LabourRelieveMemBerRisk(LabourRelieveMemBerRiskContext context);

        [OperationContract]
        ResponseContext<GetMemBerRuleDataModel> GetMemBerRuleData(GetMemBerRuleDataContext context);

        [OperationContract]
        ResponseContext<Model.MIMS.Model.MemberTopUpEditModel> MemberTopUpEdit(Model.MIMS.Context.MemberTopUpEditContext context);

        [OperationContract]
        ResponseContext<List<GetMemberCardRecordDataModel>> GetMemberCardRecordData(GetMemberCardRecordDataContext context);

        [OperationContract]
        ResponseContext<ExcelMemberCardRecordDataModel> ExcelMemberCardRecordData(ExcelMemberCardRecordDataContext context);

        [OperationContract]
        ResponseContext<List<KeyValueModel>> GetMemberLevelList();
    }
}
