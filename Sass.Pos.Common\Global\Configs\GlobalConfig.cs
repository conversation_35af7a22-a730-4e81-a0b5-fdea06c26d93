﻿using Saas.Pos.Common.Global.Configs.Bar;
using Saas.Pos.Common.Global.Configs.Comm;
using Saas.Pos.Common.Global.Configs.DbFood;
using Saas.Pos.Common.Global.Configs.Rms;
using Saas.Pos.Common.Global.Configs.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common
{
    /// <summary>
    ///  全局配置资源
    /// </summary>
    public class GlobalConfig
    {
        private static Dictionary<string, Tuple<string, string>> BtnCollection = new Dictionary<string, Tuple<string, string>>();
        private static Dictionary<string, Tuple<string, string>> CashCollection = new Dictionary<string, Tuple<string, string>>();
        GlobalConfig()
        {
            BtnCollection.Add("A", new Tuple<string, string>("结账", "#8482C6"));
            BtnCollection.Add("B", new Tuple<string, string>("坏房", "#010101"));
            BtnCollection.Add("C", new Tuple<string, string>("续单", "#008200"));
            BtnCollection.Add("D", new Tuple<string, string>("清洁", "#848284"));
            BtnCollection.Add("E", new Tuple<string, string>("可用", "#FFFFFF"));
            BtnCollection.Add("U", new Tuple<string, string>("占用", "#FF8284"));
            BtnCollection.Add("UnKnown", new Tuple<string, string>("未知", "#C8C8C8"));
            CashCollection.Add("N", new Tuple<string, string>("常规下单", "#000000"));
            CashCollection.Add("Z", new Tuple<string, string>("赠送", "#FF0000"));
            CashCollection.Add("X", new Tuple<string, string>("销单", "#778899"));
            BookConfig = new BookConfig();
            ShopConfig = new ShopConfig();
            OrderConfig = new OrderConfig();
            RedisKey = new RedisKeyConfig();
            ShopBookConfig = new ShopBookConfig();
            OrderExConfig = new OrderManageConfig();
            WineStorkManageConfig = new WineStorkManageConfig();
            RoomConfig = new RoomConfig();
            Comm = new CommConfig();
        }

        static GlobalConfig _Global;
        public static GlobalConfig Global
        {
            get
            {
                if (_Global == null)
                    _Global = new GlobalConfig();
                return _Global;
            }
        }

        #region --------全局属性--------
        /// <summary>
        /// 订单模块全局资源
        /// </summary>
        public OrderManageConfig OrderExConfig { get; private set; }
        /// <summary>
        /// 酒吧存酒模块全局资源
        /// </summary>
        public WineStorkManageConfig WineStorkManageConfig { get; private set; }
        /// <summary>
        /// 预约模块全局资源
        /// </summary>
        public BookConfig BookConfig { get; private set; }
        /// <summary>
        /// 天王下单模块全局资源
        /// </summary>
        public OrderConfig OrderConfig { get; private set; }

        /// <summary>
        /// 天王房间模块全局资源
        /// </summary>
        public RoomConfig RoomConfig { get; private set; }

        /// <summary>
        /// SaasPos模块全局资源
        /// </summary>
        public ShopBookConfig ShopBookConfig { get; set; }

        /// <summary>
        /// RedisKey全局资源
        /// </summary>
        public RedisKeyConfig RedisKey { get; set; }

        /// <summary>
        /// 门店全局资源
        /// </summary>
        public ShopConfig ShopConfig { get; set; }

        /// <summary>
        /// 全局公共资源
        /// </summary>
        public CommConfig Comm { get; private set; }

        #endregion

        #region --------全局方法--------

        /// <summary>
        /// 获取房态并且获取按钮颜色
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public Tuple<string, string> GetBtnColor(string key)
        {
            if (BtnCollection.ContainsKey(key))
                return BtnCollection[key];
            else
                return BtnCollection["UnKnown"];
        }

        /// <summary>
        /// 获取下单类型并且获取颜色
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public Tuple<string, string> GetCashColor(string key)
        {
            if (CashCollection.ContainsKey(key))
                return CashCollection[key];
            else
                return BtnCollection["UnKnown"];
        }

        #endregion
    }
}
