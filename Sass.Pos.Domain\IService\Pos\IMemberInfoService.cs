﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IMemberInfoService
    {
        [OperationContract]
        ResponseContext<ReturnInt> AddDbMemberChechOutData(AddMemberChechOutInfoContext context);

        [OperationContract]
        ResponseContext<ReturnInt> AddDbMemberChechOutDataByStore(AddMemberChechOutInfoContext context);
    }
}
