﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface ICommission_SchemesInfoRepository : IRepositoryBase<Commission_SchemesInfo>
    {
        List<Commission_SchemesInfo> GetSchemesList(GetBestSchemesContext context);

        List<GetCommissionSchemesModel> GetSchemesPageList(GetSchemesListContext context);

        List<GetPermitSchemesConfigsModel> GetPermitSchemesConfigs(GetPermitSchemesConfigsContext context);

        List<GetPermitItemModel> GetPermitItem(GetPermitItemContext context);

        List<GetPermitDataModel> GetPermitData(GetPermitDataContext context);
    }
}
