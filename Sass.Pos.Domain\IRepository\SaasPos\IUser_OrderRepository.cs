﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IUser_OrderRepository : IRepositoryBase<User_Order>
    {
        List<GetSkuDescriptionModel> GetUserOrderGoodName(int orderId);

        GetOrderDetailModel GetOrderData(int orderid);

        int GetUserCouData(string OpenId, string CamId);

        GetCoupon_CampaignModelEx GetUserCouDataEx(string OpenId, string CamId);

        int GetIsUseCouCount(string openid);

        int GetIsUseCouCountByMiddleId(string openid, int MiddleId);

        GetUserOrderDataModel GetUserOrderData(GetUserOrderDataContext context);

        List<GetOrderMakeByDay> GetShopMakeData(GetShopMakeDataContext context);

        GetOrderCountModel GetOrderCount(string openid);


        GetCouCountModel GetCouCount(string openid);

        List<GetMakeFileDataModel> GetMakeFileData(GetMakeFileDataContext context);

        GetBookCaheOpenDataModel GetBookCaheOpenData(int orderId);

        /// <summary>
        /// 查询在线预定订单明细
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        List<GetShopBookCacheOrderDetailModel> GetShopBookCacheOrderDetail(GetOrderDetailDataExContext context);

        List<GetOrderDataStatisModel> GetOrderDataStatis(GetOrderDataStatisContext context);

        List<ExportOrderDataStatisModel> ExportOrderDataStatis(GetOrderDataStatisContext context);

        GetOrderDataInfoModel GetOrder(int orderId);
    }
}
