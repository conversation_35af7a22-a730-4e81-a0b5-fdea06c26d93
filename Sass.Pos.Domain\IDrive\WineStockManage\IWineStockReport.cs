﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.WineStockManage
{
    public interface IWineStockReport
    {
        /// <summary>
        /// 存酒报表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<WineStorkReportModel>> GetWineStorkReportData(WineStorkReportContext context);
    }
}
