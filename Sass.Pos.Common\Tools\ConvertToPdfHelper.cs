﻿using SelectPdf;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    public class ConvertToPdfHelper
    {
        static string ResourcePath = ConfigurationManager.AppSettings["BillPdf"].ToString();
        /// <summary>
        /// Pdfb保存文件夹
        /// </summary>
        private static string PdfPath = ResourcePath;
        /// <summary>
        /// Pdf历史记录保存地址
        /// </summary>
        private static string PdfHisPath = ResourcePath + "\\HisPdf";

        /// <summary>
        /// 将Html转换成Pdf文件
        /// </summary>
        /// <param name="htmlStr">Html地址</param>
        /// <param name="pdfName">pdf名称</param>
        /// <returns></returns>
        public static string ConvertUrlToPdf(string url, string pdfName)
        {
            var pdfSavePath = PdfPath + "\\" + pdfName + ".pdf";
            if (pdfSavePath.FileExists())
            {
                //创建文件夹
                PdfHisPath.CreateFolder();
                //如果已存在，就将Pdf移动到历史记录表中，并且改换名字
                var pdfHisSavePath = PdfHisPath + "\\" + pdfName + "-" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".pdf";
                pdfSavePath.MoveFile(pdfHisSavePath);
            }

            System.Diagnostics.Process.Start("net", $@"use {ResourcePath} Musicbox@123 /user:administrator");
            HtmlToPdf converter = new HtmlToPdf();
            PdfDocument doc = converter.ConvertUrl(url);
            doc.Save(pdfSavePath);
            doc.Close();

            return pdfSavePath;
        }
    }
}
