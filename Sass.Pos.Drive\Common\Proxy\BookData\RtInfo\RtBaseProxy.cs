﻿using Saas.Pos.Common;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    /// <summary>
    /// 房型代理基类
    /// </summary>
    public abstract class RtBaseProxy : BookDateBaseProxy
    {
        public List<GetBookRtTypeModel> GetBookRtType(DateTime date, string timeNo)
        {
            var query = AppSingle.App.Storage.DbDat.RtList.AsEnumerable();
            if (Proxy.Context.ShopId > 0)
                query = query.Where(w => w.ShopId == Proxy.Context.ShopId);

            var rtInfo = AppSingle.App.Storage.DbDat.RtList.Where(w => w.ShopId == Proxy.Context.ShopId).OrderBy(x => x.SortNumber).ToList();
            var roomQuery = AppSingle.App.Storage.BookingDat.RoomDataStorage.Where(w => w.ShopId == Proxy.Context.ShopId).AsEnumerable();

            //获取当前时段的客户预约记录
            //var bookCacheList = AppSingle.App.Storage.BookingDat.StoreDynamicStorage.Data.BookCacheList.Where(w => w.ComeDate == date.ToString("yyyyMMdd") && w.ShopId == Proxy.Context.ShopId && w.BeginTimeNo == timeNo).ToList();
            var bookCacheList = AppSingle.App.Storage.BookingDat.BookCacheList.Where(w => w.ComeDate == date.ToString("yyyyMMdd") && w.ShopId == Proxy.Context.ShopId).ToList();

            //计算超订房型数据
            var resultData = BookingBaseProxy.GetWorkOutList(roomQuery.ToList(), 1, date);
            //计算预订之后的数据
            resultData = BookingBaseProxy.CalculateRoomNumber(resultData, bookCacheList);
            var resultRtData = resultData.FirstOrDefault().Book.FirstOrDefault(w => w.SId == Proxy.Context.ShopId).RtInfo.Select(w => new
            {
                RtNo = w.RtNo,
                Stop = w.cb,
                RmQty = w.RmQty,
            }).ToList();

            var data = (from li in resultRtData
                        join info in rtInfo on li.RtNo equals info.RtNo
                        join qu in query on li.RtNo equals qu.RtNo
                        join rm in roomQuery on li.RtNo equals rm.RtNo
                        select new GetBookRtTypeModel()
                        {
                            TolCount = rm.TotalCount,
                            ReQty = li.RmQty,
                            Stop = li.Stop,
                            Sid = qu.ShopId,
                            RbMax = qu.NumberMax,
                            RbMin = qu.NumberMin,
                            Id = li.RtNo,
                            Name = info.RtName
                        }).OrderBy(w => w.RbMax).ToList();

            return data;
        }

        /// <summary>
        /// 获取门店超限预订数
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns></returns>
        public List<WorkBookOut> GetBookOutList(DateTime startTime, DateTime endTime)
        {
            var query = AppSingle.App.Storage.BookingDat.WorkBookOutList.AsEnumerable();
            if (Proxy.Context.ShopId > 0)
                query = query.Where(x => x.ShopId == Proxy.Context.ShopId);

            var bookOutList = query.Where(w => w.WorkTimeStart <= startTime && w.WorkTimeEnd >= endTime).ToList();
            return bookOutList;
        }

        public abstract object GetShopRtType(DateTime date, string timeNo);
    }
}
