﻿using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Order_RelevanceApp : AppBase<Order_Relevance>
    {
        public List<GetOrderPaymentInfoModel> GetPayInfo(List<int> orderId)
        {
            return Repository.Order_Relevance.GetPayInfo(orderId);
        }
    }
}
