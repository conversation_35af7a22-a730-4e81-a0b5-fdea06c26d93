﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.Collaboration.Context;
using Saas.Pos.Model.Collaboration.Model;
using Saas.Pos.Model.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IEnergy_Metering_RecordRepository : IRepositoryBase<Energy_Metering_Record>
    {
        List<GetRecordItemListModel> GetRecordItemList(GetRecordItemListContext context);
    }
}
