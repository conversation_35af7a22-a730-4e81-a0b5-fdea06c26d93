﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IWineStockService
    {
        [OperationContract]
        ResponseContext<List<WineStorkReportModel>> GetWineStorkReportData(WineStorkReportContext context);

        [OperationContract]
        ResponseContext<List<UserCustWineData>> GetUserDrinksData(UserCustDataContext context);

        [OperationContract]
        ResponseContext<GetFdTypeDatasModel> GetFdTypeData(GetFdTypeDataContext context);

        [OperationContract]
        ResponseContext<List<GetFdDataModel>> GetFdData(GetFdDataContext context);

        [OperationContract]
        ResponseContext<GetWineDataByBarIdModel> GetWineDataByBarId(GetWineDataByBarIdContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetWineMainDataOnVueModel>> GetWineMainDataOnVue(GetUserWineDataOnVueContext context);

        [OperationContract]
        ResponseContext<List<GetWineDetailDataOnVueModel>> GetWineDetailDataOnVue(GetWineDetailDataOnVueContext context);

        [OperationContract]
        ResponseContext<WineStorkAuthModel> WineStorkAuth(WineStorkAuthContext context);

        [OperationContract]
        ResponseContext<FetchWineUserExModel> FetchWineUser(FetchWineUserContext context);

        [OperationContract]
        ResponseContext<FetchWineUserOrderModel> FetchWineUserOrder(FetchWineUserOrderContext context);

        [OperationContract]
        ResponseContext<SaveWineUserExModel> SaveWineUser(SaveWineUserContext context);

        [OperationContract]
        ResponseContext<SaveWineUserExModel> ContinueSaveWineUser(ContinueSaveWineUserContext context);

        [OperationContract]
        ResponseContext<SaveDrCheckDataModel> SaveDrCheckData(SaveDrCheckDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetDrCheckDataModel>> GetDrCheckData(GetDrCheckDataContext context);

        [OperationContract]
        ResponseContext<List<GetDrCheckDetailDataModel>> GetDrCheckDetailData(GetDrCheckDetailDataContext context);

        [OperationContract]
        ResponseContext<GetWineDataModel> GetWineData(GetWineDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetfdListEx>> GetFdList(GetfdListContext context);

        [OperationContract]
        ResponseContext<EditFdDataModel> InsertFdData(List<EditFdDataContext> context);

        [OperationContract]
        ResponseContext<EditFdDataModel> EditFdData(EditFdDataContext context);

        [OperationContract]
        ResponseContext<DeleteFdDataModel> DeleteFdData(DeleteFdDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetfdTypeList>> GetFtList(GetftListContext context);

        [OperationContract]
        ResponseContext<List<GetfdTypeList>> GetfdTypeLists(GetfdTypeListsContext context);

        [OperationContract]
        ResponseContext<EditFtDataModel> InsertFtData(List<EditFtDataContext> context);

        [OperationContract]
        ResponseContext<EditFtDataModel> EditFtData(EditFtDataContext context);

        [OperationContract]
        ResponseContext<DeleteFtDataModel> DeleteFtData(DeleteFtDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetUnitList>> GetUnitList(GetUnitListContext context);

        [OperationContract]
        ResponseContext<EditUnitDataModel> EditUnitData(EditUnitDataContext context);

        [OperationContract]
        ResponseContext<DeleteUnitModel> DeleteUnit(DeleteUnitContext context);
    }
}
