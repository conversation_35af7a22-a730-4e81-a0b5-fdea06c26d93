﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IMobileFtTypeRepository : IRepositoryBase<MobileFtType>
    {
        List<GetOrderMenuData> GetOrderMenuData();

        List<FdDataDetail> GetOrderMenuDataDetail(GetOrderMenuDataDetailContext context);

        List<FdData> GetOrderMenuDataByFdName(GetDataByFdNameContext context);

        List<AddItemContext> GetAddItemData(GetAddItemContext context);

        List<PackageFdInfo> GetPackageItem(List<string> packageFdNo);
    }
}
