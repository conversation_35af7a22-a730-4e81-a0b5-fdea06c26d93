﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Domain.IDrive;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IDbFoodService
    {
        [OperationContract]
        ResponseContext<List<GetStoreReportModel>> GetStoreReport(GetStoreReportContext context);

        [OperationContract]
        ResponseContext<List<GetStoreFtTypeReportModel>> GetTypeDetailReport(GetStoreFtTypeReportContext context);

        [OperationContract]
        ResponseContext<List<GetHeadCountModel>> GetHeadCountReport(GetStoreReportContext context);

        [OperationContract]
        ResponseContext<List<WaitCreateModel>> GetWaitCreate(WaitCreateContext context);

        [OperationContract]
        ResponseContext<List<GetFoodInfoModel>> GetFoodInfo(GetFoodInfoContext context);

        [OperationContract]
        ResponseContext<List<GetShopFoodInfoModel>> GetFoodListByStore(GetFoodListContext context);

        [OperationContract]
        ResponseContext<List<GetShopFoodInfoModel>> GetFoodList(GetFoodListContext context);
    }
}
