﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.Model
{
    public class QueryOrderResponse
    {
        public string OrderId { get; set; }

        public bool IsTimeCard { get; set; }

        public List<ReceiptInfo> Receipt { get; set; }
    }

    public class ReceiptInfo 
    {
        public string ReceiptCode { get; set; }

        /// <summary>
        /// 1：待使用，2：已使用；3：已退款
        /// </summary>
        public int Status { get; set; }

        public int PayAmount { get; set; }
    }
}
