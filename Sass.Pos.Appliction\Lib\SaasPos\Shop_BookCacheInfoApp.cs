﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Shop_BookCacheInfoApp : AppBase<Shop_BookCacheInfo>
    {
        public List<ShopBookCacheInfoModel> GetUserBookData(GetShopBookCacheInfoContext context)
        {
            return Repository.Shop_BookCacheInfo.GetUserBookData(context);
        }

        public List<Shop_BookCacheInfo> GetExpireData(DateTime date)
        {
            return Repository.Shop_BookCacheInfo.GetExpireData(date);
        }

        public List<GetConsumeListModel> GetConsumeList(GetConsumePageingContext context)
        {
            return Repository.Shop_BookCacheInfo.GetConsumeList(context);
        }
    }
}
