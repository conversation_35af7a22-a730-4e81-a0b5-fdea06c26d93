﻿using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class shoporderinfoApp : AppBase<shoporderinfo>
    {
        public GetRoomOrderItemModel GetOrderItem(GetRoomOrderItemContext context)
        {
            return Repository.shoporderinfo.GetOrderItem(context);
        }
    }
}
    