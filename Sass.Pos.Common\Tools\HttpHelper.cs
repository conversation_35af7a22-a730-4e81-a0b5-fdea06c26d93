﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    public class HttpHelper
    {
        /// <summary>
        /// 获取重定向地址
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string GetEncrypCode(string url)
        {
            string encrypCode = string.Empty;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.AllowAutoRedirect = false; // 设置不自动重定向
            try
            {
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    if ((int)response.StatusCode >= 301 && (int)response.StatusCode <= 399)
                        encrypCode = response.Headers["Location"];
                    else
                        throw new Exception("获取重定向地址失败！");
                }

                return encrypCode;
            }
            catch (Exception ex)
            {
                Log.LogHelper.Info(url + "查询卡券重定向地址失败：" + ex.Message);
                throw new Exception("未找到卡券信息！");
            }
        }

        /// <summary>
        /// 通用http请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="method"></param>
        /// <returns></returns>
        public static string Request(string url, string method = "GET")
        {
            HttpWebRequest client = (HttpWebRequest)WebRequest.Create(url);
            client.Method = method;
            try
            {
                using (WebResponse response = client.GetResponse())
                {
                    using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                    {
                        var responseBody = reader.ReadToEnd();

                        return responseBody;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ExMessage("Http请求失败！" + ex.Message);
            }
        }
    }
}
