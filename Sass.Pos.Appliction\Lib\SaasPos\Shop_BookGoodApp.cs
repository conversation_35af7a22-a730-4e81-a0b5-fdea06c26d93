﻿using Saas.Pos.Model.Enum;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Shop_BookGoodApp : AppBase<Shop_BookGood>
    {
        public List<GetBookSkuModel> GetSkuData(GetBookSkuDataContext context)
        {
            return Repository.Shop_BookGood.GetSkuData(context);
        }

        public List<GetOrderBookSkuModel> GetOrderSkuData(List<GetOrderBookSkuContext> contexts)
        {
            return Repository.Shop_BookGood.GetOrderSkuData(contexts);
        }

        public List<GetOrderBookSkuModel> GetSkuData(GetSkuListContext context)
        {
            return Repository.Shop_BookGood.GetSkuList(context);
        }

        public GetOrderDetailsModel GetOrderDetails(QueryOrderPlaceContext context)
        {
            return Repository.Shop_BookGood.GetOrderDetails(context);
        }


        public List<GetNightBookModel> GetNightBook(GetNightBookContext context)
        {
            return Repository.Shop_BookGood.GetNightBook(context);
        }

        public GetShopBookGoodModel GetBookGoodList(GetShopBookGoodListContextEx context)
        {
            return Repository.Shop_BookGood.GetBookGoodList(context);
        }

        public List<GetShopBookGoodListModel> GetSameBookGood(GetBookSkuDataContextEx context)
        {
            return Repository.Shop_BookGood.GetSameBookGood(context);

        }

        public List<GetOrderDetailsModel> GetOrderDetailList(List<int> orderIds)
        {
            return Repository.Shop_BookGood.GetOrderDetailList(orderIds);
        }

        public List<GetSkuDataModel> GetSkuData(GetSkuDataContext context)
        {
            return Repository.Shop_BookGood.GetSkuData(context);
        }

        public List<Shop_GoodsPayMethodExModel> GetShopSkuPayMethodList(GetShopPayMethodsListContext context)
        {
            return Repository.Shop_BookGood.GetShopSkuPayMethodList(context);
        }

        public List<GetModeLinkDataModel> GetModeLinkData(GetModeLinkDataContext context)
        {
            var data = Repository.Shop_BookGood.GetModeLinkData(context);

            data = data.Select(i => { i.PlatformName = Enum.GetName(typeof(GoodModeNameEnum), i.PlatformId); return i; }).ToList();

            return data;
        }

        public List<GetBookGoodDataModel> GetBookGoodData(GetBookGoodDataContext context)
        {
            return Repository.Shop_BookGood.GetBookGoodData(context);
        }

        public List<GetPermitDataExModel> GetPermitDataEx(GetPermitDataExContext context)
        {
            var data = Repository.Shop_BookGood.GetPermitDataEx(context);

            data = data.Select(i => { i.PlatformName = Enum.GetName(typeof(GoodModeNameEnum), i.PlatformId); i.ChargingName = Enum.GetName(typeof(ShopPermitChargingEnum), i.Charging); return i; }).ToList();

            return data;
        }

        public List<GetRtInfoDataModel> GetRtInfoData(GetRtInfoDataContext context)
        {
            return Repository.Shop_BookGood.GetRtInfoData(context);
        }
    }
}
