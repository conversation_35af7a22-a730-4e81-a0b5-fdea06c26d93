﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Rms.NumberPool
{
    /// <summary>
    /// 无序生成预约号实现
    /// </summary>
    public class NumberPoolDisOrderService : NumberPoolServiceBase
    {
        /// <summary>
        /// 当日已生成的号码
        /// </summary>
        private static List<KeyValuePair<string, DateTime>> PublishedNumber = new List<KeyValuePair<string, DateTime>>();
        private SystemConfigModel config;
        public NumberPoolDisOrderService(List<KeyValuePair<string, DateTime>> _PublishedNumber, SystemConfigModel _config, DateTime bookDate) : base(bookDate)
        {
            config = _config;
            PublishedNumber = _PublishedNumber;
        }

        public NumberPoolDisOrderService(DateTime bookDate) : base(bookDate)
        {
        }

        public override string GenerateNumber()
        {
            var publishedNumber = PublishedNumber.Where(w => w.Value.Date == BookDate.Date).Select(w => w.Key).ToList();
            var existNumbers = new List<int>();
            if (!string.IsNullOrEmpty(config.existNumbers))
                existNumbers = config.existNumbers.Split(',').Select(x => Convert.ToInt32(x)).ToList();

            Random random = new Random();
            StringBuilder builder = new StringBuilder();
            while (true)
            {
                //一直循环，知道字符串的长度等于需要的长度再结束循环
                while (builder.Length < config.Length)
                {
                    int number = random.Next(0, 9);
                    if (Inspect(existNumbers, number))
                        builder.Append(number);
                }

                if (!string.IsNullOrEmpty(config.Initial))
                    builder.Insert(0, config.Initial);

                if (publishedNumber.FirstOrDefault(w => w == builder.ToString()) != null)
                    builder.Clear();
                else
                    break;
            }

            string returnStr = builder.ToString();
            return returnStr;
        }
    }
}
