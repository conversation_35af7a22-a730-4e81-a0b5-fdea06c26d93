﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.MemBerManage
{
    public interface IMemBerRiskControl
    {
        /// <summary>
        /// 解除风控并检查风控
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        void RelieveMemBerRisk(RelieveMemBerRiskControlContext context);

        /// <summary>
        /// 检测风控
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        bool MemBerRiskControl(MemBerRiskControlContext context);

        /// <summary>
        /// 人工解除风控
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<RepDeductMoneyModel> LabourRelieveMemBerRisk(LabourRelieveMemBerRiskContext context);
    }
}
