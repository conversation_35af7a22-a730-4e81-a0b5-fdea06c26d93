﻿
using ComponentApplicationServiceInterface.Web;
using Saas.Pos.Model.SongBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.SongBase
{
 public partial class Hotsong_tjApp : AppBase<Hotsong_tj> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Hotsong_tj> SetRepository(RepositoryFactory.T4.SongBase.RepositorySession Session)
        {
            return Session.Hotsong_tj;
        }
   
        
 
 }
  

 public partial class SongCollectionInfoApp : AppBase<SongCollectionInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SongCollectionInfo> SetRepository(RepositoryFactory.T4.SongBase.RepositorySession Session)
        {
            return Session.SongCollectionInfo;
        }
   
        
 
 }
  

 public partial class SongPutApp : AppBase<SongPut> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SongPut> SetRepository(RepositoryFactory.T4.SongBase.RepositorySession Session)
        {
            return Session.SongPut;
        }
   
        
 
 }
  

 public partial class SongScanApp : AppBase<SongScan> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SongScan> SetRepository(RepositoryFactory.T4.SongBase.RepositorySession Session)
        {
            return Session.SongScan;
        }
   
        
 
 }
  

 public partial class SongScanBillApp : AppBase<SongScanBill> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SongScanBill> SetRepository(RepositoryFactory.T4.SongBase.RepositorySession Session)
        {
            return Session.SongScanBill;
        }
   
        
 
 }
  

 public partial class SongSizeApp : AppBase<SongSize> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SongSize> SetRepository(RepositoryFactory.T4.SongBase.RepositorySession Session)
        {
            return Session.SongSize;
        }
   
        
 
 }
  

}
