﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService
{
    [ServiceContract]
    public interface IShopBookGoodService
    {
        [OperationContract]
        ResponseContext<ReturnInt> SaveBookGood(SaveBookGoodContext context);

        [OperationContract]
        ResponseContext<ReturnInt> DeleteBookGood(DeleteBookGoodContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetBookGoodDataModel>> GetBookGoodData(GetBookGoodDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetPermitDataExModel>> GetPermitDataEx(GetPermitDataExContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetRtInfoDataModel>> GetRtInfoData(GetRtInfoDataContext context);

        [OperationContract]
        ResponseContext<DeleteRtInfoDataModel> DeleteRtInfoData(DeleteRtInfoDataContext context);

        [OperationContract]
        ResponseContext<SaveRtInfoDataModel> SaveRtInfoData(SaveRtInfoDataContext context);

        [OperationContract]
        ResponseContext<ReturnInt> SavePermit(SaveShopBookPermitContext context);

        [OperationContract]
        ResponseContext<DeletePermitDataModel> DeletePermitData(DeletePermitDataContext context);

        [OperationContract]
        ResponseContext<GetShopBookGoodModel> GetBookGoodList(GetShopBookGoodListContext context);

        [OperationContract]
        ResponseContext<GetBookPermitSkuModel> GetSkuList(GetBookSkuDataContext context);

        [OperationContract]
        ResponseContext<bool> QueryOrderPlace(QueryOrderPlaceContext context);

        [OperationContract]
        ResponseContext<List<GetNightBookModel>> GetNightBookList(GetNightBookContext context);

        [OperationContract]
        ResponseContext<List<GetShopBookGoodListModel>> GetSameBookGood(GetBookSkuDataContext context);

        [OperationContract]
        ResponseContext<GetBookSelItemsModel> GetBookSelItems(GetBookSelItemsContext context);

        [OperationContract]
        ResponseContext<GetShopTimeRtInfoDepositModel> GetDeposit(GetShopTimeRtInfoDepositContext context);

        [OperationContract]
        ResponseContext<ReturnInt> SaveDeposit(SaveShopTimeRtInfoDepositContext context);

        [OperationContract]
        ResponseContext<DeleteRtinfoDepositDataModel> DeleteRtinfoDepositData(DeleteRtinfoDepositDataContext context);

        [OperationContract]
        ResponseContext<ReturnInt> ChangeDepositInfoStatus(ChangeDepositInfoStatusContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetShopTimeRtInfoDepositListModel>> GetDepositList(GetDepositListContext context);

        [OperationContract]
        ResponseContext<Shop_TimeRtInfoDeposit> GetTimeRtDepositInfo(GetTimeRtDepositInfoContext context);
    }
}
