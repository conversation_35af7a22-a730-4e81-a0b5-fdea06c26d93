﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Request
{
    public class ConsumeRequest : MeituanRequestBase
    {
        /// <summary>
        /// 请求id，用于标识幂等性
        /// </summary>
        public string requestid { get; set; }
        /// <summary>
        /// 团购券码，必须未验证
        /// </summary>
        public string receipt_code { get; set; }
        /// <summary>
        /// 验券数量, 不可多于100个
        /// </summary>
        public int count { get; set; }
        /// <summary>
        /// 美团点评店铺id，必须是团购的适用门店
        /// </summary>
        public string open_shop_uuid { get; set; }
        /// <summary>
        /// 商家在自研系统或第三方服务商系统内登录的帐号，仅用于记录验券者的信息，该字段不参与任何验券校验逻辑
        /// </summary>
        public string app_shop_account { get; set; }
        /// <summary>
        /// 商家在自研系统或第三方服务商系统内登陆的用户名，仅用于记录验券者的信息，该字段不参与任何验券校验逻辑
        /// </summary>
        public string app_shop_accountname { get; set; }

        public string app_shop_id { get; set; }

        public string third_device_id { get; set; }
        public string third_venue_id { get; set; }
        public string third_device_location { get; set; }
        public string third_device_city { get; set; }
        public string third_device_type { get; set; }
        public string third_location_type { get; set; }
    }

    public class QueryModel : MeituanRequestBase
    {
        public QueryModel()
        {
            limit = 100;
        }

        public string open_shop_uuid { get; set; }

        //public string receipt_code { get; set; }

        public int page_no { get; set; }

        public int limit { get; set; }
    }
}
