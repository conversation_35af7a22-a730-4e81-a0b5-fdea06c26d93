﻿

using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.MIMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.MIMS
{
 public partial interface IActivityRepository : IRepositoryBase<Activity> {}
  

 public partial interface IADemoRepository : IRepositoryBase<ADemo> {}
  

 public partial interface ICheckoutInfoRepository : IRepositoryBase<CheckoutInfo> {}
  

 public partial interface IdtpropertiesRepository : IRepositoryBase<dtproperties> {}
  

 public partial interface IExGiftInfoRepository : IRepositoryBase<ExGiftInfo> {}
  

 public partial interface IFdUserWeChatRepository : IRepositoryBase<FdUserWeChat> {}
  

 public partial interface IFdUserWeChatJurisdictionRepository : IRepositoryBase<FdUserWeChatJurisdiction> {}
  

 public partial interface IGiftInfoRepository : IRepositoryBase<GiftInfo> {}
  

 public partial interface IIC_FdUserRepository : IRepositoryBase<IC_FdUser> {}
  

 public partial interface IIntegralInfoRepository : IRepositoryBase<IntegralInfo> {}
  

 public partial interface IManagerCardInfoRepository : IRepositoryBase<ManagerCardInfo> {}
  

 public partial interface IManagerInfoRepository : IRepositoryBase<ManagerInfo> {}
  

 public partial interface IMemberCardInfoRepository : IRepositoryBase<MemberCardInfo> {}
  

 public partial interface IMemberCardInfoJoinRepository : IRepositoryBase<MemberCardInfoJoin> {}
  

 public partial interface IMemberCardInfoRemoveRepository : IRepositoryBase<MemberCardInfoRemove> {}
  

 public partial interface IMemberCardOrderRecordRepository : IRepositoryBase<MemberCardOrderRecord> {}
  

 public partial interface IMemberCardPresentInfoRepository : IRepositoryBase<MemberCardPresentInfo> {}
  

 public partial interface IMemberCardTypeInfoRepository : IRepositoryBase<MemberCardTypeInfo> {}
  

 public partial interface IMemberEmpowerPayRepository : IRepositoryBase<MemberEmpowerPay> {}
  

 public partial interface IMemberGiftRecordRepository : IRepositoryBase<MemberGiftRecord> {}
  

 public partial interface IMemberInfoRepository : IRepositoryBase<MemberInfo> {}
  

 public partial interface IMemberInfo_copy1Repository : IRepositoryBase<MemberInfo_copy1> {}
  

 public partial interface IMemberInfoExtendRepository : IRepositoryBase<MemberInfoExtend> {}
  

 public partial interface IMemberInfoRemoveRepository : IRepositoryBase<MemberInfoRemove> {}
  

 public partial interface IMemberOpenUpRuleRepository : IRepositoryBase<MemberOpenUpRule> {}
  

 public partial interface IMemBerRiskControlRepository : IRepositoryBase<MemBerRiskControl> {}
  

 public partial interface IMemBerRiskRuleRepository : IRepositoryBase<MemBerRiskRule> {}
  

 public partial interface IMemBerRuleRepository : IRepositoryBase<MemBerRule> {}
  

 public partial interface IMemBerSccountRecordRepository : IRepositoryBase<MemBerSccountRecord> {}
  

 public partial interface IMemberSystemEditionRepository : IRepositoryBase<MemberSystemEdition> {}
  

 public partial interface IMimsRoomCommissionRepository : IRepositoryBase<MimsRoomCommission> {}
  

 public partial interface IPointInfoRepository : IRepositoryBase<PointInfo> {}
  

 public partial interface IPointInfoTempRepository : IRepositoryBase<PointInfoTemp> {}
  

 public partial interface IRechargeInfoRepository : IRepositoryBase<RechargeInfo> {}
  

 public partial interface IRechargePurviewInfoRepository : IRepositoryBase<RechargePurviewInfo> {}
  

 public partial interface IRegMemberCardInfoRepository : IRepositoryBase<RegMemberCardInfo> {}
  

 public partial interface IReturnAnnualClosingRepository : IRepositoryBase<ReturnAnnualClosing> {}
  

 public partial interface IReturnInfoRepository : IRepositoryBase<ReturnInfo> {}
  

 public partial interface IScoreDelRecordRepository : IRepositoryBase<ScoreDelRecord> {}
  

 public partial interface IShopInfoRepository : IRepositoryBase<ShopInfo> {}
  

 public partial interface IsysdiagramsRepository : IRepositoryBase<sysdiagrams> {}
  

 public partial interface ITh_RoomCommissionAllotRepository : IRepositoryBase<Th_RoomCommissionAllot> {}
  

 public partial interface IUserCardInfoRepository : IRepositoryBase<UserCardInfo> {}
  

 public partial interface Ivip_report_singlecou_projectRepository : IRepositoryBase<vip_report_singlecou_project> {}
  

 public partial interface Ivip_report_singlecou_project_itemRepository : IRepositoryBase<vip_report_singlecou_project_item> {}
  

 public partial interface Ivip_report_singlecou_project_item_record_dayRepository : IRepositoryBase<vip_report_singlecou_project_item_record_day> {}
  

}
