﻿using MiddlewareLibrary.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MiddlewareProxy.RabbitMq
{
    public class RabbitMQ : MqBase
    {
        public List<string> QueueList = new List<string>()
        {
            "trade_order_queue"
        };
        MiddlewareLibrary.MQ.AMQP.RabbitMQ MQ = new MiddlewareLibrary.MQ.AMQP.RabbitMQ(Objects.IOC.DI<MQServerContext>("MQServer"));

        public RabbitMQ()
        {
            this.Client = MQ.Client;
            this.Server = MQ.Server;
            //MQ.Client.Consume(new BasicConsumer((content, args) =>
            //{
            //    Console.WriteLine(DateTime.Now.ToString("yyyyMMdd HH:mm:ss:sss") + "_" + content);
            //})
            //{
            //    Queue = "trade_order_queue",

            //});
        }

    }
}
