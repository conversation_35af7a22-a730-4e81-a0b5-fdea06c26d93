﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IBookingNewAttDayService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetBookingNewAttDayDataModel>> GetBookingNewAttDayData(GetBookingNewAttDayDataContext context);

        [OperationContract]
        ResponseContext<UserOrderReturnModel> ExportBookingNewAttDayData(ExportBookingNewAttDayDataContext context);
    }
}
