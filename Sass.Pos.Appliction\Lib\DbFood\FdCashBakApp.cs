﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class FdCashBakApp : AppBase<FdCashBak>
    {
        public List<GetFdCashModel> GetFdCashList(GetFdCashContext context) 
        {
            return Repository.FdCashBak.GetFdCashList(context);
        }
    }
}
