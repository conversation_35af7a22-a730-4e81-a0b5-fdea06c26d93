<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod">
            <summary>
            실행을 위한 TestMethod입니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestMethodName">
            <summary>
            테스트 메서드의 이름을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.TestClassName">
            <summary>
            테스트 클래스의 이름을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ReturnType">
            <summary>
            테스트 메서드의 반환 형식을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.ParameterTypes">
            <summary>
            테스트 메서드의 매개 변수를 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.MethodInfo">
            <summary>
             테스트 메서드에 대한 methodInfo를 가져옵니다.
            </summary>
            <remarks>
            This is just to retrieve additional information about the method.
            Do not directly invoke the method using MethodInfo. Use ITestMethod.Invoke instead.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.Invoke(System.Object[])">
            <summary>
            테스트 메서드를 호출합니다.
            </summary>
            <param name="arguments">
            테스트 메서드에 전달할 인수(예: 데이터 기반의 경우)
            </param>
            <returns>
            테스트 메서드 호출의 결과.
            </returns>
            <remarks>
            This call handles asynchronous test methods as well.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAllAttributes(System.Boolean)">
            <summary>
            테스트 메서드의 모든 특성을 가져옵니다.
            </summary>
            <param name="inherit">
            부모 클래스에 정의된 특성이 올바른지 여부입니다.
            </param>
            <returns>
            모든 특성.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod.GetAttributes``1(System.Boolean)">
            <summary>
            특정 형식의 특성을 가져옵니다.
            </summary>
            <typeparam name="AttributeType"> System.Attribute type. </typeparam>
            <param name="inherit">
            부모 클래스에 정의된 특성이 올바른지 여부입니다.
            </param>
            <returns>
            지정한 형식의 특성입니다.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Helper">
            <summary>
            도우미입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNull(System.Object,System.String,System.String)">
            <summary>
            검사 매개 변수가 Null이 아닙니다.
            </summary>
            <param name="param">
            매개 변수.
            </param>
            <param name="parameterName">
            매개 변수 이름.
            </param>
            <param name="message">
            메시지.
            </param>
            <exception cref="T:System.ArgumentNullException"> Throws argument null exception when parameter is null. </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Helper.CheckParameterNotNullOrEmpty(System.String,System.String,System.String)">
            <summary>
            검사 매개 변수가 Null이 아니거나 비어 있습니다.
            </summary>
            <param name="param">
            매개 변수.
            </param>
            <param name="parameterName">
            매개 변수 이름.
            </param>
            <param name="message">
            메시지.
            </param>
            <exception cref="T:System.ArgumentException"> Throws ArgumentException when parameter is null. </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod">
            <summary>
            데이터 기반 테스트에서 데이터 행에 액세스하는 방법에 대한 열거형입니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Sequential">
            <summary>
            행이 순차적인 순서로 반환됩니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random">
            <summary>
            행이 임의의 순서로 반환됩니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute">
            <summary>
            테스트 메서드에 대한 인라인 데이터를 정의하는 특성입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="data1"> 데이터 개체. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.#ctor(System.Object,System.Object[])">
            <summary>
            인수 배열을 사용하는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="data1"> 데이터 개체. </param>
            <param name="moreData"> 추가 데이터. </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.Data">
            <summary>
            테스트 메서드 호출을 위한 데이터를 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute.DisplayName">
            <summary>
            사용자 지정을 위한 테스트 결과에서 표시 이름을 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            <summary>
            어설션 불확실 예외입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 메시지. </param>
            <param name="ex"> 예외. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 메시지. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException">
            <summary>
            InternalTestFailureException 클래스. 테스트 사례에 대한 내부 실패를 나타내는 데 사용됩니다.
            </summary>
            <remarks>
            This class is only added to preserve source compatibility with the V1 framework.
            For all practical purposes either use AssertFailedException/AssertInconclusiveException.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 예외 메시지. </param>
            <param name="ex"> 예외. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 예외 메시지. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.InternalTestFailureException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute">
            <summary>
            지정된 형식의 예외를 예상하도록 지정하는 특성
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type)">
            <summary>
            예상 형식이 있는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="exceptionType">예상되는 예외의 형식</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.#ctor(System.Type,System.String)">
            <summary>
            테스트에서 예외를 throw하지 않을 때 포함할 메시지 및 예상 형식이 있는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute"/> 클래스의
            새 인스턴스를 초기화합니다.
            </summary>
            <param name="exceptionType">예상되는 예외의 형식</param>
            <param name="noExceptionMessage">
            예외를 throw하지 않아 테스트가 실패할 경우 테스트 결과에 포함할 메시지
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.ExceptionType">
            <summary>
            예상되는 예외의 형식을 나타내는 값을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.AllowDerivedTypes">
            <summary>
            예상 예외의 형식에서 파생된 형식이 예상대로 자격을 얻도록 허용할지 여부를 나타내는 값을 가져오거나
            설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.NoExceptionMessage">
            <summary>
            예외를 throw하지 않아 테스트에 실패하는 경우 테스트 결과에 포함할 메시지를 가져옵니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionAttribute.Verify(System.Exception)">
            <summary>
            단위 테스트에 의해 throw되는 예외의 형식이 예상되는지를 확인합니다.
            </summary>
            <param name="exception">단위 테스트에서 throw한 예외</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute">
            <summary>
            단위 테스트에서 예외를 예상하도록 지정하는 특성에 대한 기본 클래스
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor">
            <summary>
            기본 예외 없음 메시지가 있는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.#ctor(System.String)">
            <summary>
            예외 없음 메시지가 있는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="noExceptionMessage">
            예외를 throw하지 않아서 테스트가 실패할 경우 테스트 결과에 포함할
            메시지
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.NoExceptionMessage">
            <summary>
            예외를 throw하지 않아 테스트에 실패하는 경우 테스트 결과에 포함할 메시지를 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.SpecifiedNoExceptionMessage">
            <summary>
            예외를 throw하지 않아 테스트에 실패하는 경우 테스트 결과에 포함할 메시지를 가져옵니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.GetDefaultNoExceptionMessage(System.String)">
            <summary>
            기본 예외 없음 메시지를 가져옵니다.
            </summary>
            <param name="expectedExceptionAttributeTypeName">ExpectedException 특성 형식 이름</param>
            <returns>기본 예외 없음 메시지</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.Verify(System.Exception)">
            <summary>
            예외가 예상되는지 여부를 확인합니다. 메서드가 반환되면 예외가
            예상되는 것으로 이해됩니다. 메서드가 예외를 throw하면 예외가
            예상되지 않는 것으로 이해되고, throw된 예외의 메시지가
            테스트 결과에 포함됩니다. <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert"/> 클래스는 편의를 위해 사용될 수
            있습니다. <see cref="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive"/>이(가) 사용되는 경우 어설션에 실패하며,
            테스트 결과가 [결과 불충분]으로 설정됩니다.
            </summary>
            <param name="exception">단위 테스트에서 throw한 예외</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.ExpectedExceptionBaseAttribute.RethrowIfAssertException(System.Exception)">
            <summary>
            AssertFailedException 또는 AssertInconclusiveException인 경우 예외를 다시 throw합니다.
            </summary>
            <param name="exception">어설션 예외인 경우 예외를 다시 throw</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper">
            <summary>
            이 클래스는 제네릭 형식을 사용하는 형식에 대한 사용자의 유닛 테스트를 지원하도록 설계되었습니다.
            GenericParameterHelper는 몇 가지 공통된 제네릭 형식 제약 조건을 충족합니다.
            예:
            1. public 기본 생성자
            2. 공통 인터페이스 구현: IComparable, IEnumerable
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor">
            <summary>
            C# 제네릭의 '새로 입력할 수 있는' 제약 조건을 충족하는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 클래스의
            새 인스턴스를 초기화합니다.
            </summary>
            <remarks>
            This constructor initializes the Data property to a random value.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.#ctor(System.Int32)">
            <summary>
            데이터 속성을 사용자가 제공한 값으로 초기화하는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 클래스의
            새 인스턴스를 초기화합니다.
            </summary>
            <param name="data">임의의 정수 값</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Data">
            <summary>
            데이터를 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Equals(System.Object)">
            <summary>
            두 GenericParameterHelper 개체의 값을 비교합니다.
            </summary>
            <param name="obj">비교할 개체</param>
            <returns>개체의 값이 '이' GenericParameterHelper 개체와 동일한 경우에는 true이고,
            동일하지 않은 경우에는 false입니다.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetHashCode">
            <summary>
            이 개체의 해시 코드를 반환합니다.
            </summary>
            <returns>해시 코드입니다.</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.CompareTo(System.Object)">
            <summary>
            두 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/> 개체의 데이터를 비교합니다.
            </summary>
            <param name="obj">비교할 개체입니다.</param>
            <returns>
            이 인스턴스 및 값의 상대 값을 나타내는 부호 있는 숫자입니다.
            </returns>
            <exception cref="T:System.NotSupportedException">
            Thrown when the object passed in is not an instance of <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.GetEnumerator">
            <summary>
            길이가 데이터 속성에서 파생된 IEnumerator 개체를
            반환합니다.
            </summary>
            <returns>IEnumerator 개체</returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.GenericParameterHelper.Clone">
            <summary>
            현재 개체와 동일한 GenericParameterHelper 개체를
            반환합니다.
            </summary>
            <returns>복제된 개체입니다.</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger">
            <summary>
             사용자가 진단을 위해 단위 테스트에서 추적을 로그하거나 쓸 수 있습니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessageHandler">
            <summary>
            LogMessage용 처리기입니다.
            </summary>
            <param name="message">로깅할 메시지.</param>
        </member>
        <member name="E:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.OnLogMessage">
            <summary>
            수신할 이벤트입니다. 단위 테스트 기록기에서 메시지를 기록할 때 발생합니다.
            주로 어댑터에서 사용합니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Logging.Logger.LogMessage(System.String,System.Object[])">
            <summary>
            메시지를 로그하기 위해 테스트 작성자가 호출하는 API입니다.
            </summary>
            <param name="format">자리 표시자가 있는 문자열 형식.</param>
            <param name="args">자리 표시자에 대한 매개 변수.</param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute">
            <summary>
            TestCategory 특성 - 단위 테스트의 범주 지정에 사용됩니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute"/> 클래스의 새 인스턴스를 초기화하고 범주를 테스트에 적용합니다.
            </summary>
            <param name="testCategory">
            테스트 범주.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryAttribute.TestCategories">
            <summary>
            테스트에 적용된 테스트 범주를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute">
            <summary>
            "Category" 특성을 위한 기본 클래스
            </summary>
            <remarks>
            The reason for this attribute is to let the users create their own implementation of test categories.
            - test framework (discovery, etc) deals with TestCategoryBaseAttribute.
            - The reason that TestCategories property is a collection rather than a string,
              is to give more flexibility to the user. For instance the implementation may be based on enums for which the values can be OR'ed
              in which case it makes sense to have single attribute rather than multiple ones on the same test.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            범주를 테스트에 적용합니다. TestCategories에 의해 반환된 문자열은
            테스트 필터링을 위한 /category 명령과 함께 사용됩니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestCategoryBaseAttribute.TestCategories">
            <summary>
            테스트에 적용된 테스트 범주를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            <summary>
            AssertFailedException 클래스 - 테스트 사례에 대한 실패를 나타내는 데 사용됩니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 메시지. </param>
            <param name="ex"> 예외. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 메시지. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.Assert">
            <summary>
            단위 테스트 내에서 다양한 조건을 테스트하기 위한 도우미
            클래스의 컬렉션입니다. 테스트 중인 조건이 충족되지 않으면 예외가
            throw됩니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.That">
            <summary>
            Assert 기능의 singleton 인스턴스를 가져옵니다.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void IsOfType&lt;T&gt;(this Assert assert, object obj)"
            Users could then use a syntax similar to the default assertions which in this case is "Assert.That.IsOfType&lt;Dog&gt;(animal);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean)">
            <summary>
            지정된 조건이 true인지를 테스트하고 조건이 false이면 예외를
            throw합니다.
            </summary>
            <param name="condition">
            테스트가 참일 것으로 예상하는 조건.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            지정된 조건이 true인지를 테스트하고 조건이 false이면 예외를
            throw합니다.
            </summary>
            <param name="condition">
            테스트가 참일 것으로 예상하는 조건.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="condition"/>
            이(가) 거짓인 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsTrue(System.Boolean,System.String,System.Object[])">
            <summary>
            지정된 조건이 true인지를 테스트하고 조건이 false이면 예외를
            throw합니다.
            </summary>
            <param name="condition">
            테스트가 참일 것으로 예상하는 조건.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="condition"/>
            이(가) 거짓인 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is false.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean)">
            <summary>
            지정된 조건이 false인지를 테스트하고 조건이 true이면 예외를
            throw합니다.
            </summary>
            <param name="condition">
            테스트가 거짓일 것으로 예상하는 조건.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            지정된 조건이 false인지를 테스트하고 조건이 true이면 예외를
            throw합니다.
            </summary>
            <param name="condition">
            테스트가 거짓일 것으로 예상하는 조건.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="condition"/>
            이(가) 참인 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsFalse(System.Boolean,System.String,System.Object[])">
            <summary>
            지정된 조건이 false인지를 테스트하고 조건이 true이면 예외를
            throw합니다.
            </summary>
            <param name="condition">
            테스트가 거짓일 것으로 예상하는 조건.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="condition"/>
            이(가) 참인 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="condition"/> is true.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object)">
            <summary>
            지정된 개체가 Null인지를 테스트하고, Null이 아니면 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 null일 것으로 예상하는 개체.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String)">
            <summary>
            지정된 개체가 Null인지를 테스트하고, Null이 아니면 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 null일 것으로 예상하는 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) null이 아닌 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNull(System.Object,System.String,System.Object[])">
            <summary>
            지정된 개체가 Null인지를 테스트하고, Null이 아니면 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 null일 것으로 예상하는 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) null이 아닌 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object)">
            <summary>
            지정된 개체가 Null이 아닌지를 테스트하고, Null이면 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 null이 아닐 것으로 예상하는 개체.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String)">
            <summary>
            지정된 개체가 Null이 아닌지를 테스트하고, Null이면 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 null이 아닐 것으로 예상하는 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) null인 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotNull(System.Object,System.String,System.Object[])">
            <summary>
            지정된 개체가 Null이 아닌지를 테스트하고, Null이면 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 null이 아닐 것으로 예상하는 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) null인 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object)">
            <summary>
            지정된 두 개체가 동일한 개체를 참조하는지를 테스트하고, 두 입력이
            동일한 개체를 참조하지 않으면 예외를 throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 개체. 테스트가 예상하는 값입니다.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String)">
            <summary>
            지정된 두 개체가 동일한 개체를 참조하는지를 테스트하고, 두 입력이
            동일한 개체를 참조하지 않으면 예외를 throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 개체. 테스트가 예상하는 값입니다.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            지정된 두 개체가 동일한 개체를 참조하는지를 테스트하고, 두 입력이
            동일한 개체를 참조하지 않으면 예외를 throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 개체. 테스트가 예상하는 값입니다.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> does not refer to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object)">
            <summary>
            지정된 개체가 서로 다른 개체를 참조하는지를 테스트하고, 두 입력이
            동일한 개체를 참조하면 예외를 throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 개체. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String)">
            <summary>
            지정된 개체가 서로 다른 개체를 참조하는지를 테스트하고, 두 입력이
            동일한 개체를 참조하면 예외를 throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 개체. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 동일한 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotSame(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            지정된 개체가 서로 다른 개체를 참조하는지를 테스트하고, 두 입력이
            동일한 개체를 참조하면 예외를 throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 개체. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 동일한 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> refers to the same object
            as <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0)">
            <summary>
            지정된 값이 같은지를 테스트하고, 두 값이 같지 않으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            비교할 첫 번째 값. 테스트가 예상하는 값입니다.
            </param>
            <param name="actual">
            비교할 두 번째 값. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String)">
            <summary>
            지정된 값이 같은지를 테스트하고, 두 값이 같지 않으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            비교할 첫 번째 값. 테스트가 예상하는 값입니다.
            </param>
            <param name="actual">
            비교할 두 번째 값. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            지정된 값이 같은지를 테스트하고, 두 값이 같지 않으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="expected">
            비교할 첫 번째 값. 테스트가 예상하는 값입니다.
            </param>
            <param name="actual">
            비교할 두 번째 값. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            지정된 값이 다른지를 테스트하고, 두 값이 같으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            비교할 첫 번째 값. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 값. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String)">
            <summary>
            지정된 값이 다른지를 테스트하고, 두 값이 같으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            비교할 첫 번째 값. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 값. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual``1(``0,``0,System.String,System.Object[])">
            <summary>
            지정된 값이 다른지를 테스트하고, 두 값이 같으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <typeparam name="T">
            The type of values to compare.
            </typeparam>
            <param name="notExpected">
            비교할 첫 번째 값. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 값. 테스트 중인 코드에 의해 생성된 값입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object)">
            <summary>
            지정된 개체가 같은지를 테스트하고, 두 개체가 같지 않으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 개체. 테스트가 예상하는 개체입니다.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 개체입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String)">
            <summary>
            지정된 개체가 같은지를 테스트하고, 두 개체가 같지 않으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 개체. 테스트가 예상하는 개체입니다.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 개체입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            지정된 개체가 같은지를 테스트하고, 두 개체가 같지 않으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 개체. 테스트가 예상하는 개체입니다.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 개체입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object)">
            <summary>
            지정된 개체가 다른지를 테스트하고, 두 개체가 같으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 개체. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 개체입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String)">
            <summary>
            지정된 개체가 다른지를 테스트하고, 두 개체가 같으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 개체. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 개체입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Object,System.Object,System.String,System.Object[])">
            <summary>
            지정된 개체가 다른지를 테스트하고, 두 개체가 같으면
            예외를 throw합니다. 논리값이 같더라도 숫자 형식이 다르면
            같지 않은 것으로 취급됩니다. 42L은 42와 같지 않습니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 개체. 테스트가 다음과 일치하지 않을 것으로 예상하는
            값: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 개체. 테스트 중인 코드에 의해 생성된 개체입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single)">
            <summary>
            지정된 부동이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 부동. 테스트가 예상하는 부동입니다.
            </param>
            <param name="actual">
            비교할 두 번째 부동. 테스트 중인 코드에 의해 생성된 부동입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="expected"/>
            의 차이가 다음보다 큰 경우: <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            지정된 부동이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 부동. 테스트가 예상하는 부동입니다.
            </param>
            <param name="actual">
            비교할 두 번째 부동. 테스트 중인 코드에 의해 생성된 부동입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="expected"/>
            의 차이가 다음보다 큰 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            과(와)<paramref name="expected"/>의 차이가 다음보다 큰 경우: 
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            지정된 부동이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 부동. 테스트가 예상하는 부동입니다.
            </param>
            <param name="actual">
            비교할 두 번째 부동. 테스트 중인 코드에 의해 생성된 부동입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="expected"/>
            의 차이가 다음보다 큰 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            과(와)<paramref name="expected"/>의 차이가 다음보다 큰 경우: 
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single)">
            <summary>
            지정된 부동이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 부동. 테스트가 다음과 일치하지 않을 것으로 예상하는
            부동: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 부동. 테스트 중인 코드에 의해 생성된 부동입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="notExpected"/>
            의 차이가 다음을 넘지 않는 경우: <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String)">
            <summary>
            지정된 부동이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 부동. 테스트가 다음과 일치하지 않을 것으로 예상하는
            부동: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 부동. 테스트 중인 코드에 의해 생성된 부동입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="notExpected"/>
            의 차이가 다음을 넘지 않는 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/> 또는 그 차이가 다음 미만인 경우:
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Single,System.Single,System.Single,System.String,System.Object[])">
            <summary>
            지정된 부동이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 부동. 테스트가 다음과 일치하지 않을 것으로 예상하는
            부동: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 부동. 테스트 중인 코드에 의해 생성된 부동입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="notExpected"/>
            의 차이가 다음을 넘지 않는 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/> 또는 그 차이가 다음 미만인 경우:
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double)">
            <summary>
            지정된 double이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 double. 테스트가 예상하는 double입니다.
            </param>
            <param name="actual">
            비교할 두 번째 double. 테스트 중인 코드에 의해 생성된 double입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="expected"/>
            의 차이가 다음보다 큰 경우: <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            지정된 double이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 double. 테스트가 예상하는 double입니다.
            </param>
            <param name="actual">
            비교할 두 번째 double. 테스트 중인 코드에 의해 생성된 double입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="expected"/>
            의 차이가 다음보다 큰 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            과(와)<paramref name="expected"/>의 차이가 다음보다 큰 경우: 
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            지정된 double이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 double. 테스트가 예상하는 double입니다.
            </param>
            <param name="actual">
            비교할 두 번째 double. 테스트 중인 코드에 의해 생성된 double입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="expected"/>
            의 차이가 다음보다 큰 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            과(와)<paramref name="expected"/>의 차이가 다음보다 큰 경우: 
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double)">
            <summary>
            지정된 double이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 double. 테스트가 다음과 일치하지 않을 것으로 예상하는
            double: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 double. 테스트 중인 코드에 의해 생성된 double입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="notExpected"/>
            의 차이가 다음을 넘지 않는 경우: <paramref name="delta"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String)">
            <summary>
            지정된 double이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 double. 테스트가 다음과 일치하지 않을 것으로 예상하는
            double: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 double. 테스트 중인 코드에 의해 생성된 double입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="notExpected"/>
            의 차이가 다음을 넘지 않는 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/> 또는 그 차이가 다음 미만인 경우:
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.Double,System.Double,System.Double,System.String,System.Object[])">
            <summary>
            지정된 double이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 double. 테스트가 다음과 일치하지 않을 것으로 예상하는
            double: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 double. 테스트 중인 코드에 의해 생성된 double입니다.
            </param>
            <param name="delta">
            필요한 정확성. 다음과 같은 경우에만 예외가 throw됩니다.
            <paramref name="actual"/>과(와)<paramref name="notExpected"/>
            의 차이가 다음을 넘지 않는 경우: <paramref name="delta"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/> 또는 그 차이가 다음 미만인 경우:
            <paramref name="delta"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean)">
            <summary>
            지정된 문자열이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다. 비교에는 고정 문화권이 사용됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 문자열. 테스트가 예상하는 문자열입니다.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            지정된 문자열이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다. 비교에는 고정 문화권이 사용됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 문자열. 테스트가 예상하는 문자열입니다.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            지정된 문자열이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다. 비교에는 고정 문화권이 사용됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 문자열. 테스트가 예상하는 문자열입니다.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            지정된 문자열이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 문자열. 테스트가 예상하는 문자열입니다.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="culture">
            문화권 관련 비교 정보를 제공하는 CultureInfo 개체.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            지정된 문자열이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 문자열. 테스트가 예상하는 문자열입니다.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="culture">
            문화권 관련 비교 정보를 제공하는 CultureInfo 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            지정된 문자열이 같은지를 테스트하고, 같지 않으면 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 문자열. 테스트가 예상하는 문자열입니다.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="culture">
            문화권 관련 비교 정보를 제공하는 CultureInfo 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean)">
            <summary>
            지정된 문자열이 다른지를 테스트하고, 같으면 예외를
            throw합니다. 비교에는 고정 문화권이 사용됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 문자열. 테스트가 다음과 일치하지 않을 것으로 예상하는
            문자열: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String)">
            <summary>
            지정된 문자열이 다른지를 테스트하고, 같으면 예외를
            throw합니다. 비교에는 고정 문화권이 사용됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 문자열. 테스트가 다음과 일치하지 않을 것으로 예상하는
            문자열: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.String,System.Object[])">
            <summary>
            지정된 문자열이 다른지를 테스트하고, 같으면 예외를
            throw합니다. 비교에는 고정 문화권이 사용됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 문자열. 테스트가 다음과 일치하지 않을 것으로 예상하는
            문자열: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            지정된 문자열이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 문자열. 테스트가 다음과 일치하지 않을 것으로 예상하는
            문자열: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="culture">
            문화권 관련 비교 정보를 제공하는 CultureInfo 개체.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String)">
            <summary>
            지정된 문자열이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 문자열. 테스트가 다음과 일치하지 않을 것으로 예상하는
            문자열: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="culture">
            문화권 관련 비교 정보를 제공하는 CultureInfo 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.AreNotEqual(System.String,System.String,System.Boolean,System.Globalization.CultureInfo,System.String,System.Object[])">
            <summary>
            지정된 문자열이 다른지를 테스트하고, 같으면 예외를
            throw합니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 문자열. 테스트가 다음과 일치하지 않을 것으로 예상하는
            문자열: <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 문자열. 테스트 중인 코드에 의해 생성된 문자열입니다.
            </param>
            <param name="ignoreCase">
            대/소문자를 구분하거나 구분하지 않는 비교를 나타내는 부울(true는
            대/소문자를 구분하지 않는 비교를 나타냄).
            </param>
            <param name="culture">
            문화권 관련 비교 정보를 제공하는 CultureInfo 개체.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type)">
            <summary>
            지정된 개체가 예상 형식의 인스턴스인지를 테스트하고,
            예상 형식이 개체의 상속 계층 구조에 있지 않은 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 지정된 형식일 것으로 예상하는 개체.
            </param>
            <param name="expectedType">
            다음의 예상 형식: <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            지정된 개체가 예상 형식의 인스턴스인지를 테스트하고,
            예상 형식이 개체의 상속 계층 구조에 있지 않은 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 지정된 형식일 것으로 예상하는 개체.
            </param>
            <param name="expectedType">
            다음의 예상 형식: <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음의 인스턴스가 아닌 경우: <paramref name="expectedType"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            지정된 개체가 예상 형식의 인스턴스인지를 테스트하고,
            예상 형식이 개체의 상속 계층 구조에 있지 않은 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 지정된 형식일 것으로 예상하는 개체.
            </param>
            <param name="expectedType">
            다음의 예상 형식: <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음의 인스턴스가 아닌 경우: <paramref name="expectedType"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type)">
            <summary>
            지정된 개체가 잘못된 형식의 인스턴스가 아닌지를 테스트하고,
            지정된 형식이 개체의 상속 계층 구조에 있는 경우 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 지정된 형식이 아닐 것으로 예상하는 개체.
            </param>
            <param name="wrongType">
            형식: <paramref name="value"/>이(가) 아니어야 함.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String)">
            <summary>
            지정된 개체가 잘못된 형식의 인스턴스가 아닌지를 테스트하고,
            지정된 형식이 개체의 상속 계층 구조에 있는 경우 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 지정된 형식이 아닐 것으로 예상하는 개체.
            </param>
            <param name="wrongType">
            형식: <paramref name="value"/>이(가) 아니어야 함.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음의 인스턴스인 경우: <paramref name="wrongType"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.IsNotInstanceOfType(System.Object,System.Type,System.String,System.Object[])">
            <summary>
            지정된 개체가 잘못된 형식의 인스턴스가 아닌지를 테스트하고,
            지정된 형식이 개체의 상속 계층 구조에 있는 경우 예외를
            throw합니다.
            </summary>
            <param name="value">
            테스트가 지정된 형식이 아닐 것으로 예상하는 개체.
            </param>
            <param name="wrongType">
            형식: <paramref name="value"/>이(가) 아니어야 함.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음의 인스턴스인 경우: <paramref name="wrongType"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> is not null and
            <paramref name="wrongType"/> is in the inheritance hierarchy
            of <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail">
            <summary>
            AssertFailedException을 throw합니다.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String)">
            <summary>
            AssertFailedException을 throw합니다.
            </summary>
            <param name="message">
            예외에 포함할 메시지. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Fail(System.String,System.Object[])">
            <summary>
            AssertFailedException을 throw합니다.
            </summary>
            <param name="message">
            예외에 포함할 메시지. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive">
            <summary>
            AssertInconclusiveException을 throw합니다.
            </summary>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String)">
            <summary>
            AssertInconclusiveException을 throw합니다.
            </summary>
            <param name="message">
            예외에 포함할 메시지. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Inconclusive(System.String,System.Object[])">
            <summary>
            AssertInconclusiveException을 throw합니다.
            </summary>
            <param name="message">
            예외에 포함할 메시지. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertInconclusiveException">
            Always thrown.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.Equals(System.Object,System.Object)">
            <summary>
            참조 같음에 대해 두 형식의 인스턴스를 비교하는 데 정적 equals 오버로드가
            사용됩니다. 이 메서드는 같음에 대해 두 인스턴스를 비교하는 데 사용되지 <b>않습니다</b>.
            이 개체는 <b>항상</b> Assert.Fail과 함께 throw됩니다. 단위 테스트에서
            Assert.AreEqual 및 관련 오버로드를 사용하세요.
            </summary>
            <param name="objA"> 개체 A </param>
            <param name="objB"> 개체 B </param>
            <returns> 항상 False. </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action)">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우
            <code>
            AssertFailedException
            </code>
            을 throw합니다.
            </summary>
            <param name="action">
            테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            throw될 예외 형식입니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String)">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우
            <code>
            AssertFailedException
            </code>
            을 throw합니다.
            </summary>
            <param name="action">
            테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="action"/>
            이(가) 다음 형식의 예외를 throw하지 않는 경우:<typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            throw될 예외 형식입니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object})">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우
            <code>
            AssertFailedException
            </code>
            을 throw합니다.
            </summary>
            <param name="action">
            테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            throw될 예외 형식입니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String)">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우
            <code>
            AssertFailedException
            </code>
            을 throw합니다.
            </summary>
            <param name="action">
            테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="action"/>
            이(가) 다음 형식의 예외를 throw하지 않는 경우:<typeparamref name="T"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            throw될 예외 형식입니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Func{System.Object},System.String,System.Object[])">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우
            <code>
            AssertFailedException
            </code>
            을 throw합니다.
            </summary>
            <param name="action">
            테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="action"/>
            이(가) 다음 형식의 예외를 throw하지 않는 경우:<typeparamref name="T"/>.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throw exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            throw될 예외 형식입니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException``1(System.Action,System.String,System.Object[])">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우
            <code>
            AssertFailedException
            </code>
            을 throw합니다.
            </summary>
            <param name="action">
            테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="action"/>
            이(가) 다음 형식의 예외를 throw하지 않는 경우:<typeparamref name="T"/>.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            throw될 예외 형식입니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우
            <code>
            AssertFailedException
            </code>
            을 throw합니다.
            </summary>
            <param name="action">
            테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.
            </param>
            <typeparam name="T">
            Type of exception expected to be thrown.
            </typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            <see cref="T:System.Threading.Tasks.Task"/> 오류가 발생했습니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String)">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고 
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우 <code>AssertFailedException</code>을 throw합니다.
            </summary>
            <param name="action">테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.</param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="action"/>
            이(가) 다음 형식의 예외를 throw하지 않는 경우: <typeparamref name="T"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            <see cref="T:System.Threading.Tasks.Task"/> 오류가 발생했습니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ThrowsExceptionAsync``1(System.Func{System.Threading.Tasks.Task},System.String,System.Object[])">
            <summary>
            <paramref name="action"/> 대리자가 지정한 코드가 <typeparamref name="T"/> 형식의 정확한 특정 예외(파생된 형식이 아님)를 throw하는지 테스트하고 
            코드가 예외를 throw하지 않거나 <typeparamref name="T"/>이(가) 아닌 형식의 예외를 throw하는 경우 <code>AssertFailedException</code>을 throw합니다.
            </summary>
            <param name="action">테스트할 코드 및 예외를 throw할 것으로 예상되는 코드에 대한 대리자.</param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="action"/>
            이(가) 다음 형식의 예외를 throw하지 않는 경우: <typeparamref name="T"/>.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <typeparam name="T">Type of exception expected to be thrown.</typeparam>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="action"/> does not throws exception of type <typeparamref name="T"/>.
            </exception>
            <returns>
            <see cref="T:System.Threading.Tasks.Task"/> 오류가 발생했습니다.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNullChars(System.String)">
            <summary>
            Null 문자('\0')를 "\\0"으로 바꿉니다.
            </summary>
            <param name="input">
            검색할 문자열.
            </param>
            <returns>
            Null 문자가 "\\0"으로 교체된 변환된 문자열.
            </returns>
            <remarks>
            This is only public and still present to preserve compatibility with the V1 framework.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.HandleFail(System.String,System.String,System.Object[])">
            <summary>
            AssertionFailedException을 만들고 throw하는 도우미 함수
            </summary>
            <param name="assertionName">
            예외를 throw하는 어설션의 이름
            </param>
            <param name="message">
            어설션 실패에 대한 조건을 설명하는 메시지
            </param>
            <param name="parameters">
            매개 변수.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.CheckParameterNotNull(System.Object,System.String,System.String,System.String,System.Object[])">
            <summary>
            유효한 조건의 매개 변수를 확인합니다.
            </summary>
            <param name="param">
            매개 변수.
            </param>
            <param name="assertionName">
            어셜선 이름.
            </param>
            <param name="parameterName">
            매개 변수 이름
            </param>
            <param name="message">
            잘못된 매개 변수 예외에 대한 메시지
            </param>
            <param name="parameters">
            매개 변수.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.Assert.ReplaceNulls(System.Object)">
            <summary>
            개체를 문자열로 안전하게 변환하고, Null 값 및 Null 문자를 처리합니다.
            Null 값은 "(null)"로 변환됩니다. Null 문자는 "\\0"으로 변환됩니다.
            </summary>
            <param name="input">
            문자열로 변환될 개체.
            </param>
            <returns>
            변환된 문자열.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert">
            <summary>
            문자열 어셜션입니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.That">
            <summary>
            CollectionAssert 기능의 singleton 인스턴스를 가져옵니다.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void ContainsWords(this StringAssert cusomtAssert, string value, ICollection substrings)"
            Users could then use a syntax similar to the default assertions which in this case is "StringAssert.That.ContainsWords(value, substrings);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String)">
            <summary>
            지정된 문자열에 지정된 하위 문자열이 포함되었는지를 테스트하고,
            테스트 문자열 내에 해당 하위 문자열이 없으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음을 포함할 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음 이내에 발생할 것으로 예상되는 문자열 <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String)">
            <summary>
            지정된 문자열에 지정된 하위 문자열이 포함되었는지를 테스트하고,
            테스트 문자열 내에 해당 하위 문자열이 없으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음을 포함할 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음 이내에 발생할 것으로 예상되는 문자열 <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="substring"/>
            이(가) 다음에 없는 경우: <paramref name="value"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Contains(System.String,System.String,System.String,System.Object[])">
            <summary>
            지정된 문자열에 지정된 하위 문자열이 포함되었는지를 테스트하고,
            테스트 문자열 내에 해당 하위 문자열이 없으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음을 포함할 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음 이내에 발생할 것으로 예상되는 문자열 <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="substring"/>
            이(가) 다음에 없는 경우: <paramref name="value"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="substring"/> is not found in
            <paramref name="value"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String)">
            <summary>
            지정된 문자열이 지정된 하위 문자열로 시작되는지를 테스트하고,
            테스트 문자열이 해당 하위 문자열로 시작되지 않으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음으로 시작될 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음의 접두사일 것으로 예상되는 문자열: <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String)">
            <summary>
            지정된 문자열이 지정된 하위 문자열로 시작되는지를 테스트하고,
            테스트 문자열이 해당 하위 문자열로 시작되지 않으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음으로 시작될 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음의 접두사일 것으로 예상되는 문자열: <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음으로 시작되지 않는 경우: <paramref name="substring"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.StartsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            지정된 문자열이 지정된 하위 문자열로 시작되는지를 테스트하고,
            테스트 문자열이 해당 하위 문자열로 시작되지 않으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음으로 시작될 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음의 접두사일 것으로 예상되는 문자열: <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음으로 시작되지 않는 경우: <paramref name="substring"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not begin with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String)">
            <summary>
            지정된 문자열이 지정된 하위 문자열로 끝나는지를 테스트하고,
            테스트 문자열이 해당 하위 문자열로 끝나지 않으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음으로 끝날 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음의 접미사일 것으로 예상되는 문자열: <paramref name="value"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String)">
            <summary>
            지정된 문자열이 지정된 하위 문자열로 끝나는지를 테스트하고,
            테스트 문자열이 해당 하위 문자열로 끝나지 않으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음으로 끝날 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음의 접미사일 것으로 예상되는 문자열: <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음으로 끝나지 않는 경우: <paramref name="substring"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.EndsWith(System.String,System.String,System.String,System.Object[])">
            <summary>
            지정된 문자열이 지정된 하위 문자열로 끝나는지를 테스트하고,
            테스트 문자열이 해당 하위 문자열로 끝나지 않으면 예외를
            throw합니다.
            </summary>
            <param name="value">
            다음으로 끝날 것으로 예상되는 문자열: <paramref name="substring"/>.
            </param>
            <param name="substring">
            다음의 접미사일 것으로 예상되는 문자열: <paramref name="value"/>.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음으로 끝나지 않는 경우: <paramref name="substring"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not end with
            <paramref name="substring"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            지정된 문자열이 정규식과 일치하는지를 테스트하고, 문자열이
            식과 일치하지 않으면 예외를 throw합니다.
            </summary>
            <param name="value">
            다음과 일치할 것으로 예상되는 문자열: <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            <paramref name="value"/>과(와)
            일치할 것으로 예상되는 정규식
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            지정된 문자열이 정규식과 일치하는지를 테스트하고, 문자열이
            식과 일치하지 않으면 예외를 throw합니다.
            </summary>
            <param name="value">
            다음과 일치할 것으로 예상되는 문자열: <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            <paramref name="value"/>과(와)
            일치할 것으로 예상되는 정규식
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음과 일치하지 않는 경우: <paramref name="pattern"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.Matches(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            지정된 문자열이 정규식과 일치하는지를 테스트하고, 문자열이
            식과 일치하지 않으면 예외를 throw합니다.
            </summary>
            <param name="value">
            다음과 일치할 것으로 예상되는 문자열: <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            <paramref name="value"/>과(와)
            일치할 것으로 예상되는 정규식
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음과 일치하지 않는 경우: <paramref name="pattern"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> does not match
            <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex)">
            <summary>
            지정된 문자열이 정규식과 일치하지 않는지를 테스트하고, 문자열이
            식과 일치하면 예외를 throw합니다.
            </summary>
            <param name="value">
            다음과 일치하지 않을 것으로 예상되는 문자열: <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            <paramref name="value"/>과(와)
            일치하지 않을 것으로 예상되는 정규식.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            지정된 문자열이 정규식과 일치하지 않는지를 테스트하고, 문자열이
            식과 일치하면 예외를 throw합니다.
            </summary>
            <param name="value">
            다음과 일치하지 않을 것으로 예상되는 문자열: <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            <paramref name="value"/>과(와)
            일치하지 않을 것으로 예상되는 정규식.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음과 일치하는 경우: <paramref name="pattern"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.StringAssert.DoesNotMatch(System.String,System.Text.RegularExpressions.Regex,System.String,System.Object[])">
            <summary>
            지정된 문자열이 정규식과 일치하지 않는지를 테스트하고, 문자열이
            식과 일치하면 예외를 throw합니다.
            </summary>
            <param name="value">
            다음과 일치하지 않을 것으로 예상되는 문자열: <paramref name="pattern"/>.
            </param>
            <param name="pattern">
            <paramref name="value"/>과(와)
            일치하지 않을 것으로 예상되는 정규식.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="value"/>
            이(가) 다음과 일치하는 경우: <paramref name="pattern"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="value"/> matches <paramref name="pattern"/>.
            </exception>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert">
            <summary>
            단위 테스트 내에서 컬렉션과 연결된 다양한 조건을 테스트하기
            위한 도우미 클래스의 컬렉션. 테스트 중인 조건이 충족되지 않으면
            예외가 throw됩니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.That">
            <summary>
            CollectionAssert 기능의 singleton 인스턴스를 가져옵니다.
            </summary>
            <remarks>
            Users can use this to plug-in custom assertions through C# extension methods.
            For instance, the signature of a custom assertion provider could be "public static void AreEqualUnordered(this CollectionAssert cusomtAssert, ICollection expected, ICollection actual)"
            Users could then use a syntax similar to the default assertions which in this case is "CollectionAssert.That.AreEqualUnordered(list1, list2);"
            More documentation is at "https://github.com/Microsoft/testfx-docs".
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object)">
            <summary>
            지정된 컬렉션이 지정된 요소를 포함하는지를 테스트하고,
            컬렉션에 요소가 없으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            요소를 검색할 컬렉션.
            </param>
            <param name="element">
            컬렉션에 포함될 것으로 예상되는 요소.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            지정된 컬렉션이 지정된 요소를 포함하는지를 테스트하고,
            컬렉션에 요소가 없으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            요소를 검색할 컬렉션.
            </param>
            <param name="element">
            컬렉션에 포함될 것으로 예상되는 요소.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="element"/>
            이(가) 다음에 없는 경우: <paramref name="collection"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.Contains(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            지정된 컬렉션이 지정된 요소를 포함하는지를 테스트하고,
            컬렉션에 요소가 없으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            요소를 검색할 컬렉션.
            </param>
            <param name="element">
            컬렉션에 포함될 것으로 예상되는 요소.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="element"/>
            이(가) 다음에 없는 경우: <paramref name="collection"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is not found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object)">
            <summary>
            지정된 컬렉션이 지정된 요소를 포함하지 않는지를 테스트하고,
            컬렉션에 요소가 있으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            요소를 검색할 컬렉션.
            </param>
            <param name="element">
            컬렉션에 포함되지 않을 것으로 예상되는 요소.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String)">
            <summary>
            지정된 컬렉션이 지정된 요소를 포함하지 않는지를 테스트하고,
            컬렉션에 요소가 있으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            요소를 검색할 컬렉션.
            </param>
            <param name="element">
            컬렉션에 포함되지 않을 것으로 예상되는 요소.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="element"/>
            이(가) 다음에 포함된 경우: <paramref name="collection"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.DoesNotContain(System.Collections.ICollection,System.Object,System.String,System.Object[])">
            <summary>
            지정된 컬렉션이 지정된 요소를 포함하지 않는지를 테스트하고,
            컬렉션에 요소가 있으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            요소를 검색할 컬렉션.
            </param>
            <param name="element">
            컬렉션에 포함되지 않을 것으로 예상되는 요소.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="element"/>
            이(가) 다음에 포함된 경우: <paramref name="collection"/>. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="element"/> is found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection)">
            <summary>
            지정된 컬렉션의 모든 항목이 Null이 아닌지를 테스트하고,
            Null인 요소가 있으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            Null 요소를 검색할 컬렉션.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String)">
            <summary>
            지정된 컬렉션의 모든 항목이 Null이 아닌지를 테스트하고,
            Null인 요소가 있으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            Null 요소를 검색할 컬렉션.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="collection"/>
            이(가) null 요소를 포함하는 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreNotNull(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            지정된 컬렉션의 모든 항목이 Null이 아닌지를 테스트하고,
            Null인 요소가 있으면 예외를 throw합니다.
            </summary>
            <param name="collection">
            Null 요소를 검색할 컬렉션.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="collection"/>
            이(가) null 요소를 포함하는 경우. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a null element is found in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection)">
            <summary>
            지정된 컬렉션의 모든 항목이 고유한지 여부를 테스트하고,
            컬렉션에 두 개의 같은 요소가 있는 경우 예외를 throw합니다.
            </summary>
            <param name="collection">
            중복 요소를 검색할 컬렉션.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String)">
            <summary>
            지정된 컬렉션의 모든 항목이 고유한지 여부를 테스트하고,
            컬렉션에 두 개의 같은 요소가 있는 경우 예외를 throw합니다.
            </summary>
            <param name="collection">
            중복 요소를 검색할 컬렉션.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="collection"/>
            이(가) 하나 이상의 중복 요소를 포함하는 경우. 메시지는 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreUnique(System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            지정된 컬렉션의 모든 항목이 고유한지 여부를 테스트하고,
            컬렉션에 두 개의 같은 요소가 있는 경우 예외를 throw합니다.
            </summary>
            <param name="collection">
            중복 요소를 검색할 컬렉션.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="collection"/>
            이(가) 하나 이상의 중복 요소를 포함하는 경우. 메시지는 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if a two or more equal elements are found in
            <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            한 컬렉션이 다른 컬렉션의 하위 집합인지를 테스트하고,
            하위 집합의 요소가 상위 집합에 없는 경우
            예외를 throw합니다.
            </summary>
            <param name="subset">
            다음의 하위 집합일 것으로 예상되는 컬렉션: <paramref name="superset"/>.
            </param>
            <param name="superset">
            다음의 상위 집합일 것으로 예상되는 컬렉션: <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            한 컬렉션이 다른 컬렉션의 하위 집합인지를 테스트하고,
            하위 집합의 요소가 상위 집합에 없는 경우
            예외를 throw합니다.
            </summary>
            <param name="subset">
            다음의 하위 집합일 것으로 예상되는 컬렉션: <paramref name="superset"/>.
            </param>
            <param name="superset">
            다음의 상위 집합일 것으로 예상되는 컬렉션: <paramref name="subset"/>
            </param>
            <param name="message">
            
            <paramref name="subset"/>의 요소가 다음에서 발견되지 않는 경우 예외에 포함할 메시지입니다.<paramref name="superset"/>.
            테스트 결과에 메시지가 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            한 컬렉션이 다른 컬렉션의 하위 집합인지를 테스트하고,
            하위 집합의 요소가 상위 집합에 없는 경우
            예외를 throw합니다.
            </summary>
            <param name="subset">
            다음의 하위 집합일 것으로 예상되는 컬렉션: <paramref name="superset"/>.
            </param>
            <param name="superset">
            다음의 상위 집합일 것으로 예상되는 컬렉션: <paramref name="subset"/>
            </param>
            <param name="message">
            
            <paramref name="subset"/>의 모든 요소가 다음에서 발견되지 않는 경우 예외에 포함할 메시지: <paramref name="superset"/>.
            테스트 결과에 메시지가 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="subset"/> is not found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            한 컬렉션이 다른 컬렉션의 하위 집합이 아닌지를 테스트하고,
            하위 집합의 요소가 상위 집합에도 있는 경우
            예외를 throw합니다.
            </summary>
            <param name="subset">
            다음의 하위 집합이 아닐 것으로 예상되는 컬렉션: <paramref name="superset"/>.
            </param>
            <param name="superset">
            다음의 상위 집합일 것으로 예상되지 않는 컬렉션: <paramref name="subset"/>
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            한 컬렉션이 다른 컬렉션의 하위 집합이 아닌지를 테스트하고,
            하위 집합의 요소가 상위 집합에도 있는 경우
            예외를 throw합니다.
            </summary>
            <param name="subset">
            다음의 하위 집합이 아닐 것으로 예상되는 컬렉션: <paramref name="superset"/>.
            </param>
            <param name="superset">
            다음의 상위 집합일 것으로 예상되지 않는 컬렉션: <paramref name="subset"/>
            </param>
            <param name="message">
            
            <paramref name="subset"/>의 모든 요소가 다음에서도 발견되는 경우 예외에 포함할 메시지: <paramref name="superset"/>.
            테스트 결과에 메시지가 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsNotSubsetOf(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            한 컬렉션이 다른 컬렉션의 하위 집합이 아닌지를 테스트하고,
            하위 집합의 요소가 상위 집합에도 있는 경우
            예외를 throw합니다.
            </summary>
            <param name="subset">
            다음의 하위 집합이 아닐 것으로 예상되는 컬렉션: <paramref name="superset"/>.
            </param>
            <param name="superset">
            다음의 상위 집합일 것으로 예상되지 않는 컬렉션: <paramref name="subset"/>
            </param>
            <param name="message">
            
            <paramref name="subset"/>의 모든 요소가 다음에서도 발견되는 경우 예외에 포함할 메시지: <paramref name="superset"/>.
            테스트 결과에 메시지가 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if every element in <paramref name="subset"/> is also found in
            <paramref name="superset"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            두 컬렉션에 동일한 요소가 포함되어 있는지를 테스트하고,
            한 컬렉션이 다른 컬렉션에 없는 요소를 포함하는 경우 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 요소를
            포함합니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성되는
            컬렉션입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            두 컬렉션에 동일한 요소가 포함되어 있는지를 테스트하고,
            한 컬렉션이 다른 컬렉션에 없는 요소를 포함하는 경우 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 요소를
            포함합니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성되는
            컬렉션입니다.
            </param>
            <param name="message">
            요소가 컬렉션 중 하나에서는 발견되었지만 다른 곳에서는 발견되지
            않은 경우 예외에 포함할 메시지. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            두 컬렉션에 동일한 요소가 포함되어 있는지를 테스트하고,
            한 컬렉션이 다른 컬렉션에 없는 요소를 포함하는 경우 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 요소를
            포함합니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성되는
            컬렉션입니다.
            </param>
            <param name="message">
            요소가 컬렉션 중 하나에서는 발견되었지만 다른 곳에서는 발견되지
            않은 경우 예외에 포함할 메시지. 메시지가 테스트 결과에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element was found in one of the collections but not
            the other.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            두 컬렉션에 서로 다른 요소가 포함되어 있는지를 테스트하고,
            두 컬렉션이 순서와 상관없이 동일한 요소를 포함하는 경우 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 여기에는 테스트가 실제 컬렉션과 다를 것으로
            예상하는 요소가 포함됩니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성되는
            컬렉션입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            두 컬렉션에 서로 다른 요소가 포함되어 있는지를 테스트하고,
            두 컬렉션이 순서와 상관없이 동일한 요소를 포함하는 경우 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 여기에는 테스트가 실제 컬렉션과 다를 것으로
            예상하는 요소가 포함됩니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성되는
            컬렉션입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 동일한 요소를 포함하는 경우: <paramref name="expected"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEquivalent(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            두 컬렉션에 서로 다른 요소가 포함되어 있는지를 테스트하고,
            두 컬렉션이 순서와 상관없이 동일한 요소를 포함하는 경우 예외를
            throw합니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 여기에는 테스트가 실제 컬렉션과 다를 것으로
            예상하는 요소가 포함됩니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성되는
            컬렉션입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 동일한 요소를 포함하는 경우: <paramref name="expected"/>. 메시지가
            테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if the two collections contained the same elements, including
            the same number of duplicate occurrences of each element.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type)">
            <summary>
            지정된 컬렉션의 모든 요소가 예상 형식의 인스턴스인지를 테스트하고
            예상 형식이 하나 이상의 요소의 상속 계층 구조에 없는 경우
            예외를 throw합니다.
            </summary>
            <param name="collection">
            테스트가 지정된 형식 중 하나일 것으로 예상하는 요소가 포함된
            컬렉션.
            </param>
            <param name="expectedType">
            다음의 각 요소의 예상 형식: <paramref name="collection"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String)">
            <summary>
            지정된 컬렉션의 모든 요소가 예상 형식의 인스턴스인지를 테스트하고
            예상 형식이 하나 이상의 요소의 상속 계층 구조에 없는 경우
            예외를 throw합니다.
            </summary>
            <param name="collection">
            테스트가 지정된 형식 중 하나일 것으로 예상하는 요소가 포함된
            컬렉션.
            </param>
            <param name="expectedType">
            다음의 각 요소의 예상 형식: <paramref name="collection"/>.
            </param>
            <param name="message">
            
            <paramref name="collection"/>의 요소가 다음의 인스턴스가 아닌 경우 예외에 포함할 메시지:
            <paramref name="expectedType"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AllItemsAreInstancesOfType(System.Collections.ICollection,System.Type,System.String,System.Object[])">
            <summary>
            지정된 컬렉션의 모든 요소가 예상 형식의 인스턴스인지를 테스트하고
            예상 형식이 하나 이상의 요소의 상속 계층 구조에 없는 경우
            예외를 throw합니다.
            </summary>
            <param name="collection">
            테스트가 지정된 형식 중 하나일 것으로 예상하는 요소가 포함된
            컬렉션.
            </param>
            <param name="expectedType">
            다음의 각 요소의 예상 형식: <paramref name="collection"/>.
            </param>
            <param name="message">
            
            <paramref name="collection"/>의 요소가 다음의 인스턴스가 아닌 경우 예외에 포함할 메시지:
            <paramref name="expectedType"/>. 메시지가 테스트 결과에 표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if an element in <paramref name="collection"/> is null or
            <paramref name="expectedType"/> is not in the inheritance hierarchy
            of an element in <paramref name="collection"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            지정된 컬렉션이 같은지를 테스트하고, 두 컬렉션이 같지 않으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고 있는
            것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는 같은 것으로
            간주됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 컬렉션입니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            지정된 컬렉션이 같은지를 테스트하고, 두 컬렉션이 같지 않으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고 있는
            것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는 같은 것으로
            간주됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 컬렉션입니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            지정된 컬렉션이 같은지를 테스트하고, 두 컬렉션이 같지 않으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고 있는
            것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는 같은 것으로
            간주됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 컬렉션입니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            지정된 컬렉션이 다른지를 테스트하고, 두 컬렉션이 같으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고
            있는 것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는
            같은 것으로 간주됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 컬렉션. 테스트가 다음과 일치하지 않을 것으로 예상하는
            컬렉션입니다. <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String)">
            <summary>
            지정된 컬렉션이 다른지를 테스트하고, 두 컬렉션이 같으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고
            있는 것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는
            같은 것으로 간주됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 컬렉션. 테스트가 다음과 일치하지 않을 것으로 예상하는
            컬렉션입니다. <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.String,System.Object[])">
            <summary>
            지정된 컬렉션이 다른지를 테스트하고, 두 컬렉션이 같으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고
            있는 것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는
            같은 것으로 간주됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 컬렉션. 테스트가 다음과 일치하지 않을 것으로 예상하는
            컬렉션입니다. <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            지정된 컬렉션이 같은지를 테스트하고, 두 컬렉션이 같지 않으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고 있는
            것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는 같은 것으로
            간주됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 컬렉션입니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="comparer">
            컬렉션의 요소를 비교할 때 사용할 비교 구현.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            지정된 컬렉션이 같은지를 테스트하고, 두 컬렉션이 같지 않으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고 있는
            것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는 같은 것으로
            간주됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 컬렉션입니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="comparer">
            컬렉션의 요소를 비교할 때 사용할 비교 구현.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            지정된 컬렉션이 같은지를 테스트하고, 두 컬렉션이 같지 않으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고 있는
            것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는 같은 것으로
            간주됩니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션. 테스트가 예상하는 컬렉션입니다.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="comparer">
            컬렉션의 요소를 비교할 때 사용할 비교 구현.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같지 않은 경우: <paramref name="expected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="expected"/> is not equal to
            <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer)">
            <summary>
            지정된 컬렉션이 다른지를 테스트하고, 두 컬렉션이 같으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고
            있는 것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는
            같은 것으로 간주됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 컬렉션. 테스트가 다음과 일치하지 않을 것으로 예상하는
            컬렉션입니다. <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="comparer">
            컬렉션의 요소를 비교할 때 사용할 비교 구현.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String)">
            <summary>
            지정된 컬렉션이 다른지를 테스트하고, 두 컬렉션이 같으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고
            있는 것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는
            같은 것으로 간주됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 컬렉션. 테스트가 다음과 일치하지 않을 것으로 예상하는
            컬렉션입니다. <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="comparer">
            컬렉션의 요소를 비교할 때 사용할 비교 구현.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.AreNotEqual(System.Collections.ICollection,System.Collections.ICollection,System.Collections.IComparer,System.String,System.Object[])">
            <summary>
            지정된 컬렉션이 다른지를 테스트하고, 두 컬렉션이 같으면 예외를
            throw합니다. 같음이란 동일한 요소를 동일한 순서 및 양으로 가지고
            있는 것이라고 정의됩니다. 동일한 값에 대한 서로 다른 참조는
            같은 것으로 간주됩니다.
            </summary>
            <param name="notExpected">
            비교할 첫 번째 컬렉션. 테스트가 다음과 일치하지 않을 것으로 예상하는
            컬렉션입니다. <paramref name="actual"/>.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션. 테스트 중인 코드에 의해 생성된
            컬렉션입니다.
            </param>
            <param name="comparer">
            컬렉션의 요소를 비교할 때 사용할 비교 구현.
            </param>
            <param name="message">
            다음과 같은 경우 예외에 포함할 메시지: <paramref name="actual"/>
            이(가) 다음과 같은 경우: <paramref name="notExpected"/>. 메시지가 결과 테스트에
            표시됩니다.
            </param>
            <param name="parameters">
            다음의 서식을 지정할 때 사용할 매개 변수의 배열: <paramref name="message"/>.
            </param>
            <exception cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssertFailedException">
            Thrown if <paramref name="notExpected"/> is equal to <paramref name="actual"/>.
            </exception>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.IsSubsetOfHelper(System.Collections.ICollection,System.Collections.ICollection)">
            <summary>
            첫 번째 컬렉션이 두 번째 컬렉션의 하위 집합인지를
            확인합니다. 한 집합에 중복된 요소가 포함된 경우, 하위 집합에 있는 요소의
            발생 횟수는 상위 집합에 있는 발생 횟수와 같거나
            작아야 합니다.
            </summary>
            <param name="subset">
            테스트가 다음에 포함될 것으로 예상하는 컬렉션: <paramref name="superset"/>.
            </param>
            <param name="superset">
            테스트가 다음을 포함할 것으로 예상하는 컬렉션: <paramref name="subset"/>.
            </param>
            <returns>
           다음의 경우 True <paramref name="subset"/>이(가)
            <paramref name="superset"/>의 하위 집합인 경우 참, 나머지 경우는 거짓.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.GetElementCounts(System.Collections.ICollection,System.Int32@)">
            <summary>
            지정된 컬렉션에서 각 요소의 발생 횟수를 포함하는
            사전을 생성합니다.
            </summary>
            <param name="collection">
            처리할 컬렉션.
            </param>
            <param name="nullCount">
            컬렉션에 있는 null 요소의 수.
            </param>
            <returns>
            지정된 컬렉션에 있는 각 요소의 발생 횟수를 포함하는
            딕셔너리.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.FindMismatchedElement(System.Collections.ICollection,System.Collections.ICollection,System.Int32@,System.Int32@,System.Object@)">
            <summary>
            두 컬렉션 간의 불일치 요소를 찾습니다. 불일치 요소란
            예상 컬렉션에 나타나는 횟수가 실제 컬렉션에
            나타나는 횟수와 다른 요소를 말합니다. 컬렉션은
            같은 수의 요소가 있는 Null이 아닌 다른 참조로
            간주됩니다. 이 수준에서의 확인 작업은 호출자의
            책임입니다. 불일치 요소가 없으면 함수는 false를
            반환하고 출력 매개 변수가 사용되지 않습니다.
            </summary>
            <param name="expected">
            비교할 첫 번째 컬렉션.
            </param>
            <param name="actual">
            비교할 두 번째 컬렉션.
            </param>
            <param name="expectedCount">
            다음의 예상 발생 횟수:
            <paramref name="mismatchedElement"/> 또는 불일치 요소가 없는 경우
            영(0).
            </param>
            <param name="actualCount">
            다음의 실제 발생 횟수:
            <paramref name="mismatchedElement"/> 또는 불일치 요소가 없는 경우
            영(0).
            </param>
            <param name="mismatchedElement">
            불일치 요소(null일 수 있음) 또는 불일치 요소가 없는 경우
            null.
            </param>
            <returns>
            불일치 요소가 발견되면 참, 발견되지 않으면 거짓.
            </returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CollectionAssert.ObjectComparer">
            <summary>
            object.Equals를 사용하여 개체 비교합니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException">
            <summary>
            프레임워크 예외에 대한 기본 클래스입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String,System.Exception)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 메시지. </param>
            <param name="ex"> 예외. </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestAssertException"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="msg"> 메시지. </param>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages">
            <summary>
              지역화된 문자열 등을 찾기 위한 강력한 형식의 리소스 클래스입니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ResourceManager">
            <summary>
              이 클래스에서 사용하는 캐시된 ResourceManager 인스턴스를 반환합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Culture">
            <summary>
              이 강력한 형식의 리소스 클래스를 사용하여 모든 리소스 조회에
              대한 현재 스레드의 CurrentUICulture 속성을 재정의합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AccessStringInvalidSyntax">
            <summary>
              [액세스 문자열의 구문이 잘못되었습니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ActualHasMismatchedElements">
            <summary>
              [예상 컬렉션에 &lt;{2}&gt;은(는) {1}개가 포함되어야 하는데 실제 컬렉션에는 {3}개가 포함되어 있습니다. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AllItemsAreUniqueFailMsg">
            <summary>
              [중복된 항목이 있습니다. &lt;{1}&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualCaseFailMsg">
            <summary>
              [예상 값: &lt;{1}&gt;. 대/소문자가 다른 실제 값: &lt;{2}&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDeltaFailMsg">
            <summary>
              [예상 값 &lt;{1}&gt;과(와) 실제 값 &lt;{2}&gt;의 차이가 &lt;{3}&gt;보다 크지 않아야 합니다. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualDifferentTypesFailMsg">
            <summary>
              [예상 값: &lt;{1}({2})&gt;. 실제 값: &lt;{3}({4})&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreEqualFailMsg">
            <summary>
              [예상 값: &lt;{1}&gt;. 실제 값: &lt;{2}&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualDeltaFailMsg">
            <summary>
              [예상 값 &lt;{1}&gt;과(와) 실제 값 &lt;{2}&gt;의 차이가 &lt;{3}&gt;보다 커야 합니다. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreNotEqualFailMsg">
            <summary>
              [예상 값: &lt;{1}&gt;을(를) 제외한 모든 값. 실제 값: &lt;{2}&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AreSameGivenValues">
            <summary>
              [AreSame()에 값 형식을 전달하면 안 됩니다. Object로 변환된 값은 동일한 값으로 간주되지 않습니다. AreEqual()을 사용해 보세요. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AssertionFailed">
            <summary>
              [{0}이(가) 실패했습니다. {1}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.AsyncUITestMethodNotSupported">
            <summary>
              [async TestMethod with UITestMethodAttribute는 지원되지 않습니다. async를 제거하거나 TestMethodAttribute를 사용하세요.]와 유사한 지역화된 문자열 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsEmpty">
            <summary>
              [두 컬렉션이 모두 비어 있습니다. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameElements">
            <summary>
              [두 컬렉션에 같은 요소가 포함되어 있습니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothCollectionsSameReference">
            <summary>
              [두 컬렉션 참조가 동일한 컬렉션 개체를 가리킵니다. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.BothSameElements">
            <summary>
              [두 컬렉션에 같은 요소가 포함되어 있습니다. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.CollectionEqualReason">
            <summary>
              [{0}({1})]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_NullInMessages">
            <summary>
               [(null)]과 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.Common_ObjectString">
            <summary>
              Looks up a localized string similar to (object).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ContainsFail">
            <summary>
              ['{0}' 문자열이 '{1}' 문자열을 포함하지 않습니다. {2}.]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DataDrivenResultDisplayName">
            <summary>
              [{0}({1})]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.DoNotUseAssertEquals">
            <summary>
              [어설션에 Assert.Equals를 사용할 수 없습니다. 대신 Assert.AreEqual 및 오버로드를 사용하세요.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementNumbersDontMatch">
            <summary>
              [컬렉션의 요소 수가 일치하지 않습니다. 예상 값: &lt;{1}&gt;. 실제 값: &lt;{2}&gt;.{0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementsAtIndexDontMatch">
            <summary>
              [인덱스 {0}에 있는 요소가 일치하지 않습니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch">
            <summary>
              [인덱스 {1}에 있는 요소는 예상 형식이 아닙니다. 예상 형식: &lt;{2}&gt;. 실제 형식: &lt;{3}&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ElementTypesAtIndexDontMatch2">
            <summary>
              [인덱스 {1}에 있는 요소가 (null)입니다. 예상 형식: &lt;{2}&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EndsWithFail">
            <summary>
              ['{0}' 문자열이 '{1}' 문자열로 끝나지 않습니다. {2}.]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.EqualsTesterInvalidArgs">
            <summary>
              [잘못된 인수 - EqualsTester에는 Null을 사용할 수 없습니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.ErrorInvalidCast">
            <summary>
              [{0} 형식의 개체를 {1} 형식의 개체로 변환할 수 없습니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InternalObjectNotValid">
            <summary>
              [참조된 내부 개체가 더 이상 유효하지 않습니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidParameterToAssert">
            <summary>
              ['{0}' 매개 변수가 잘못되었습니다. {1}.]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.InvalidPropertyType">
            <summary>
              [{0} 속성의 형식은 {2}이어야 하는데 실제로는 {1}입니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsInstanceOfFailMsg">
            <summary>
              [{0} 예상 형식: &lt;{1}&gt;. 실제 형식: &lt;{2}&gt;.]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsMatchFail">
            <summary>
              ['{0}' 문자열이 '{1}' 패턴과 일치하지 않습니다. {2}.]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotInstanceOfFailMsg">
            <summary>
              [잘못된 형식: &lt;{1}&gt;. 실제 형식: &lt;{2}&gt;. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.IsNotMatchFail">
            <summary>
              ['{0}' 문자열이 '{1}' 패턴과 일치합니다. {2}.]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoDataRow">
            <summary>
               [DataRowAttribute가 지정되지 않았습니다. DataTestMethodAttribute에는 하나 이상의 DataRowAttribute가 필요합니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NoExceptionThrown">
            <summary>
              [{1} 예외를 예상했지만 예외가 throw되지 않았습니다. {0}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NullParameterToAssert">
            <summary>
              ['{0}' 매개 변수가 잘못되었습니다. 이 값은 Null일 수 없습니다. {1}.](과)와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.NumberOfElementsDiff">
            <summary>
              [요소 수가 다릅니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorConstructorNotFound">
            <summary>
              다음과 유사한 지역화된 문자열을 조회합니다.
                 [지정한 시그니처를 가진 생성자를 찾을 수 없습니다. 전용 접근자를 다시 생성해야 할 수 있습니다.
                 또는 멤버가 기본 클래스에 정의된 전용 멤버일 수 있습니다. 기본 클래스에 정의된 전용 멤버인 경우에는 이 멤버를 정의하는 형식을
                 PrivateObject의 생성자에 전달해야 합니다.]
               
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.PrivateAccessorMemberNotFound">
            <summary>
              다음과 유사한 지역화된 문자열을 조회합니다.
                 [지정한 멤버({0})를 찾을 수 없습니다. 전용 접근자를 다시 생성해야 할 수 있습니다.
                 또는 멤버가 기본 클래스에 정의된 전용 멤버일 수 있습니다. 기본 클래스에 정의된 전용 멤버인 경우에는 이 멤버를 정의하는 형식을
                 PrivateObject의 생성자에 전달해야 합니다.]
               
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.StartsWithFail">
            <summary>
              ['{0}' 문자열이 '{1}' 문자열로 시작되지 않습니다. {2}.]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_ExpectedExceptionTypeMustDeriveFromException">
            <summary>
              [예상 예외 형식은 System.Exception이거나 System.Exception에서 파생된 형식이어야 합니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_FailedToGetExceptionMessage">
            <summary>
              [(예외로 인해 {0} 형식의 예외에 대한 메시지를 가져오지 못했습니다.)]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoException">
            <summary>
              [테스트 메서드에서 예상 예외 {0}을(를) throw하지 않았습니다. {1}](과)와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodNoExceptionDefault">
            <summary>
              [테스트 메서드에서 예상 예외를 throw하지 않았습니다. 예외는 테스트 메서드에 정의된 {0} 특성에 의해 예상되었습니다.]와 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongException">
            <summary>
               [테스트 메서드에서 {0} 예외를 throw했지만 {1} 예외를 예상했습니다. 예외 메시지: {2}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.UTF_TestMethodWrongExceptionDerivedAllowed">
            <summary>
              [테스트 메서드에서 {0} 예외를 throw했지만 {1} 예외 또는 해당 예외에서 파생된 형식을 예상했습니다. 예외 메시지: {2}]과(와) 유사한 지역화된 문자열을 조회합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.FrameworkMessages.WrongExceptionThrown">
             <summary>
               [{1} 예외를 예상했지만 {2} 예외를 throw했습니다. {0}
            예외 메시지: {3}
            스택 추적: {4}]과(와) 유사한 지역화된 문자열을 조회합니다.
             </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome">
            <summary>
            단위 테스트 결과
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Failed">
            <summary>
            테스트가 실행되었지만 문제가 있습니다.
            예외 또는 실패한 어설션과 관련된 문제일 수 있습니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Inconclusive">
            <summary>
            테스트가 완료되었지만, 성공인지 실패인지를 알 수 없습니다.
            중단된 테스트에 사용된 것일 수 있습니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Passed">
            <summary>
            아무 문제 없이 테스트가 실행되었습니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.InProgress">
            <summary>
            테스트가 현재 실행 중입니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Error">
            <summary>
            테스트를 실행하려고 시도하는 동안 시스템 오류가 발생했습니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Timeout">
            <summary>
            테스트가 시간 초과되었습니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Aborted">
            <summary>
            테스트가 사용자에 의해 중단되었습니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.UnitTestOutcome.Unknown">
            <summary>
            테스트의 상태를 알 수 없습니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper">
            <summary>
            단위 테스트 프레임워크에 대한 도우미 기능을 제공합니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.UtfHelper.GetExceptionMsg(System.Exception)">
            <summary>
            재귀적으로 모든 내부 예외에 대한 메시지를 포함하여 예외 메시지를
            가져옵니다.
            </summary>
            <param name="ex">오류 메시지 정보가 포함된 </param>
            <returns>문자열에 대한 메시지 가져오기의 예외</returns>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 클래스와 함께 사용할 수 있는 시간 제한에 대한 열거형입니다.
            열거형의 형식은 일치해야 합니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout.Infinite">
            <summary>
            무제한입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute">
            <summary>
            테스트 클래스 특성입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestClassAttribute.GetTestMethodAttribute(Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute)">
            <summary>
            이 테스트를 실행할 수 있는 테스트 메서드 특성을 가져옵니다.
            </summary>
            <param name="testMethodAttribute">이 메서드에 정의된 테스트 메서드 특성 인스턴스입니다.</param>
            <returns><see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute"/> 이 테스트를 실행하는 데 사용됩니다.</returns>
            <remarks>Extensions can override this method to customize how all methods in a class are run.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute">
            <summary>
             테스트 메서드 특성입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            테스트 메서드를 실행합니다.
            </summary>
            <param name="testMethod">실행할 테스트 메서드입니다.</param>
            <returns>테스트 결과를 나타내는 TestResult 개체의 배열입니다.</returns>
            <remarks>Extensions can override this method to customize running a TestMethod.</remarks>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestInitializeAttribute">
            <summary>
            테스트 초기화 특성입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestCleanupAttribute">
            <summary>
            테스트 정리 특성입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.IgnoreAttribute">
            <summary>
            무시 특성입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute">
            <summary>
            테스트 속성 특성입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="name">
            이름.
            </param>
            <param name="value">
            값.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Name">
            <summary>
            이름을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestPropertyAttribute.Value">
            <summary>
            값을 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassInitializeAttribute">
            <summary>
            클래스 초기화 특성입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.ClassCleanupAttribute">
            <summary>
            클래스 정리 특성입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyInitializeAttribute">
            <summary>
            어셈블리 초기화 특성입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.AssemblyCleanupAttribute">
            <summary>
            어셈블리 정리 특성입니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute">
            <summary>
            테스트 소유자
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="owner">
            소유자.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.OwnerAttribute.Owner">
            <summary>
            소유자를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute">
            <summary>
            Priority 특성 - 단위 테스트의 우선 순위를 지정하는 데 사용됩니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.#ctor(System.Int32)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="priority">
            우선 순위.
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.PriorityAttribute.Priority">
            <summary>
            우선 순위를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute">
            <summary>
            테스트의 설명
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.#ctor(System.String)">
            <summary>
            테스트를 설명하는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="description">설명입니다.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DescriptionAttribute.Description">
            <summary>
             테스트의 설명을 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute">
            <summary>
            CSS 프로젝트 구조 URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.#ctor(System.String)">
            <summary>
            CSS 프로젝트 구조 URI에 대한 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="cssProjectStructure">CSS 프로젝트 구조 URI입니다.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssProjectStructureAttribute.CssProjectStructure">
            <summary>
            CSS 프로젝트 구조 URI를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute">
            <summary>
            CSS 반복 URI
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.#ctor(System.String)">
            <summary>
            CSS 반복 URI에 대한 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="cssIteration">CSS 반복 URI입니다.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.CssIterationAttribute.CssIteration">
            <summary>
            CSS 반복 URI를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute">
            <summary>
            WorkItem 특성 - 이 테스트와 연결된 작업 항목을 지정하는 데 사용됩니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.#ctor(System.Int32)">
            <summary>
            WorkItem 특성에 대한 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="id">작업 항목에 대한 ID입니다.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.WorkItemAttribute.Id">
            <summary>
            연결된 작업 항목에 대한 ID를 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute">
            <summary>
            Timeout 특성 - 단위 테스트의 시간 제한을 지정하는 데 사용됩니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(System.Int32)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="timeout">
            시간 제한.
            </param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.#ctor(Microsoft.VisualStudio.TestTools.UnitTesting.TestTimeout)">
            <summary>
            미리 설정된 시간 제한이 있는 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
            <param name="timeout">
            시간 제한
            </param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TimeoutAttribute.Timeout">
            <summary>
            시간 제한을 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult">
            <summary>
            어댑터에 반환할 TestResult 개체입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.#ctor">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/> 클래스의 새 인스턴스를 초기화합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DisplayName">
            <summary>
            결과의 표시 이름을 가져오거나 설정합니다. 여러 결과를 반환할 때 유용합니다.
            Null인 경우 메서드 이름은 DisplayName으로 사용됩니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Outcome">
            <summary>
            테스트 실행의 결과를 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestFailureException">
            <summary>
            테스트 실패 시 throw할 예외를 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogOutput">
            <summary>
            테스트 코드에서 로그한 메시지의 출력을 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.LogError">
            <summary>
            테스트 코드에서 로그한 메시지의 출력을 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DebugTrace">
            <summary>
            테스트 코드에 의한 디버그 추적을 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.TestContextMessages">
            <summary>
            Gets or sets the debug traces by test code.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.Duration">
            <summary>
            테스트 실행의 지속 시간을 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.DatarowIndex">
            <summary>
            데이터 소스에서 데이터 행 인덱스를 가져오거나 설정합니다. 데이터 기반 테스트에서
            개별 데이터 행 실행의 결과에 대해서만 설정합니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ReturnValue">
            <summary>
            테스트 메서드의 반환 값을 가져오거나 설정합니다(현재 항상 Null).
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult.ResultFiles">
            <summary>
            테스트로 첨부한 결과 파일을 가져오거나 설정합니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute">
            <summary>
            데이터 기반 테스트에 대한 연결 문자열, 테이블 이름 및 행 액세스 방법을 지정합니다.
            </summary>
            <example>
            [DataSource("Provider=SQLOLEDB.1;Data Source=source;Integrated Security=SSPI;Initial Catalog=EqtCoverage;Persist Security Info=False", "MyTable")]
            [DataSource("dataSourceNameFromConfigFile")]
            </example>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultProviderName">
            <summary>
            DataSource의 기본 공급자 이름입니다.
            </summary>
        </member>
        <member name="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DefaultDataAccessMethod">
            <summary>
            기본 데이터 액세스 방법입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String,System.String,Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 클래스의 새 인스턴스를 초기화합니다. 이 인스턴스는 데이터 소스에 액세스할 데이터 공급자, 연결 문자열, 데이터 테이블 및 데이터 액세스 방법으로 초기화됩니다.
            </summary>
            <param name="providerInvariantName">고정 데이터 공급자 이름(예: System.Data.SqlClient)</param>
            <param name="connectionString">
            데이터 공급자별 연결 문자열. 
            경고: 연결 문자열에는 중요한 데이터(예: 암호)가 포함될 수 있습니다.
            연결 문자열은 소스 코드와 컴파일된 어셈블리에 일반 텍스트로 저장됩니다. 
            이 중요한 정보를 보호하려면 소스 코드 및 어셈블리에 대한 액세스를 제한하세요.
            </param>
            <param name="tableName">데이터 테이블의 이름.</param>
            <param name="dataAccessMethod">데이터에 액세스할 순서를 지정합니다.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String,System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 클래스의 새 인스턴스를 초기화합니다. 이 인스턴스는 연결 문자열 및 테이블 이름으로 초기화됩니다.
            OLEDB 데이터 소스에 액세스하기 위한 연결 문자열 및 데이터 테이블을 지정하세요.
            </summary>
            <param name="connectionString">
            데이터 공급자별 연결 문자열. 
            경고: 연결 문자열에는 중요한 데이터(예: 암호)가 포함될 수 있습니다.
            연결 문자열은 소스 코드와 컴파일된 어셈블리에 일반 텍스트로 저장됩니다. 
            이 중요한 정보를 보호하려면 소스 코드 및 어셈블리에 대한 액세스를 제한하세요.
            </param>
            <param name="tableName">데이터 테이블의 이름.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.#ctor(System.String)">
            <summary>
            <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/> 클래스의 새 인스턴스를 초기화합니다. 이 인스턴스는 설정 이름과 연결된 연결 문자열 및 데이터 공급자로 초기화됩니다.
            </summary>
            <param name="dataSourceSettingName">app.config 파일의 &lt;microsoft.visualstudio.qualitytools&gt; 섹션에 있는 데이터 소스의 이름.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ProviderInvariantName">
            <summary>
            데이터 소스의 데이터 공급자를 나타내는 값을 가져옵니다.
            </summary>
            <returns>
            데이터 공급자 이름. 데이터 공급자를 개체 초기화에서 지정하지 않은 경우 System.Data.OleDb의 기본 공급자가 반환됩니다.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.ConnectionString">
            <summary>
            데이터 소스의 연결 문자열을 나타내는 값을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.TableName">
            <summary>
            데이터를 제공하는 테이블 이름을 나타내는 값을 가져옵니다.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataAccessMethod">
             <summary>
             데이터 소스에 액세스하는 데 사용되는 메서드를 가져옵니다.
             </summary>
            
             <returns>
              <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod"/> 값 중 하나입니다. <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute"/>이(가) 초기화되지 않은 경우 다음 기본값이 반환됩니다. <see cref="F:Microsoft.VisualStudio.TestTools.UnitTesting.DataAccessMethod.Random"/>.
            </returns>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DataSourceAttribute.DataSourceSettingName">
            <summary>
            app.config 파일의 &lt;microsoft.visualstudio.qualitytools&gt; 섹션에서 찾은 데이터 소스의 이름을 가져옵니다.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute">
            <summary>
            데이터를 인라인으로 지정할 수 있는 데이터 기반 테스트의 특성입니다.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.Execute(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod)">
            <summary>
            모든 데이터 행을 찾고 실행합니다.
            </summary>
            <param name="testMethod">
            테스트 메서드.
            </param>
            <returns>
            배열 <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestResult"/>.
            </returns>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DataTestMethodAttribute.RunDataDrivenTest(Microsoft.VisualStudio.TestTools.UnitTesting.ITestMethod,Microsoft.VisualStudio.TestTools.UnitTesting.DataRowAttribute[])">
            <summary>
            데이터 기반 테스트 메서드를 실행합니다.
            </summary>
            <param name="testMethod"> 실행할 테스트 메서드. </param>
            <param name="dataRows"> 데이터 행. </param>
            <returns> 실행 결과. </returns>
        </member>
    </members>
</doc>
