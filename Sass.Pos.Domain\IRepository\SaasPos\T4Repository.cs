﻿

using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
 public partial interface IActivity_AttendRecordRepository : IRepositoryBase<Activity_AttendRecord> {}
  

 public partial interface ICommission_EmpRecordRepository : IRepositoryBase<Commission_EmpRecord> {}
  

 public partial interface ICommission_PermitInfoRepository : IRepositoryBase<Commission_PermitInfo> {}
  

 public partial interface ICommission_PermitItemRepository : IRepositoryBase<Commission_PermitItem> {}
  

 public partial interface ICommission_PermitSchemesConfigRepository : IRepositoryBase<Commission_PermitSchemesConfig> {}
  

 public partial interface ICommission_SchemesInfoRepository : IRepositoryBase<Commission_SchemesInfo> {}
  

 public partial interface ICoupon_And_ProRepository : IRepositoryBase<Coupon_And_Pro> {}
  

 public partial interface ICoupon_CampaignRepository : IRepositoryBase<Coupon_Campaign> {}
  

 public partial interface ICoupon_Campaign_ClaimRepository : IRepositoryBase<Coupon_Campaign_Claim> {}
  

 public partial interface ICoupon_Cancel_RecordRepository : IRepositoryBase<Coupon_Cancel_Record> {}
  

 public partial interface ICoupon_Claim_RecordRepository : IRepositoryBase<Coupon_Claim_Record> {}
  

 public partial interface ICoupon_InfoRepository : IRepositoryBase<Coupon_Info> {}
  

 public partial interface ICoupon_MiddleRepository : IRepositoryBase<Coupon_Middle> {}
  

 public partial interface ICoupon_RuleRepository : IRepositoryBase<Coupon_Rule> {}
  

 public partial interface ICoupon_Use_RecordRepository : IRepositoryBase<Coupon_Use_Record> {}
  

 public partial interface IEnergy_Metering_CategoryRepository : IRepositoryBase<Energy_Metering_Category> {}
  

 public partial interface IEnergy_Metering_CategoryItemRepository : IRepositoryBase<Energy_Metering_CategoryItem> {}
  

 public partial interface IEnergy_Metering_RecordRepository : IRepositoryBase<Energy_Metering_Record> {}
  

 public partial interface IEnergy_Metering_RecordItemRepository : IRepositoryBase<Energy_Metering_RecordItem> {}
  

 public partial interface IOrder_AmountRecordRepository : IRepositoryBase<Order_AmountRecord> {}
  

 public partial interface IOrder_ItemRepository : IRepositoryBase<Order_Item> {}
  

 public partial interface IOrder_RefundRepository : IRepositoryBase<Order_Refund> {}
  

 public partial interface IOrder_RelevanceRepository : IRepositoryBase<Order_Relevance> {}
  

 public partial interface IPayment_DataRepository : IRepositoryBase<Payment_Data> {}
  

 public partial interface IPos_Storage_FdTypeRepository : IRepositoryBase<Pos_Storage_FdType> {}
  

 public partial interface IPos_Storage_FoodRepository : IRepositoryBase<Pos_Storage_Food> {}
  

 public partial interface IShopRepository : IRepositoryBase<Shop> {}
  

 public partial interface IShop_Basic_ServiceRepository : IRepositoryBase<Shop_Basic_Service> {}
  

 public partial interface IShop_BookCache_BlackListRepository : IRepositoryBase<Shop_BookCache_BlackList> {}
  

 public partial interface IShop_BookCacheInfoRepository : IRepositoryBase<Shop_BookCacheInfo> {}
  

 public partial interface IShop_BookCaheOpenInfoRepository : IRepositoryBase<Shop_BookCaheOpenInfo> {}
  

 public partial interface IShop_BookGoodRepository : IRepositoryBase<Shop_BookGood> {}
  

 public partial interface IShop_BookPermitRepository : IRepositoryBase<Shop_BookPermit> {}
  

 public partial interface IShop_BookRtInfoRepository : IRepositoryBase<Shop_BookRtInfo> {}
  

 public partial interface IShop_BTime_NoticeRepository : IRepositoryBase<Shop_BTime_Notice> {}
  

 public partial interface IShop_BusinessTimeRepository : IRepositoryBase<Shop_BusinessTime> {}
  

 public partial interface IShop_GoodsRepository : IRepositoryBase<Shop_Goods> {}
  

 public partial interface IShop_GoodsImageRepository : IRepositoryBase<Shop_GoodsImage> {}
  

 public partial interface IShop_GoodsPayMethodRepository : IRepositoryBase<Shop_GoodsPayMethod> {}
  

 public partial interface IShop_GoodsSkuRepository : IRepositoryBase<Shop_GoodsSku> {}
  

 public partial interface IShop_ModeLinkRepository : IRepositoryBase<Shop_ModeLink> {}
  

 public partial interface IShop_Time_ModeRepository : IRepositoryBase<Shop_Time_Mode> {}
  

 public partial interface IShop_Time_ModeItemRepository : IRepositoryBase<Shop_Time_ModeItem> {}
  

 public partial interface IShop_TimeRtInfoDepositRepository : IRepositoryBase<Shop_TimeRtInfoDeposit> {}
  

 public partial interface ISys_CallBackErrorRepository : IRepositoryBase<Sys_CallBackError> {}
  

 public partial interface ISys_FileDownLoadRepository : IRepositoryBase<Sys_FileDownLoad> {}
  

 public partial interface IUser_CouponRepository : IRepositoryBase<User_Coupon> {}
  

 public partial interface IUser_Coupon_EventsRepository : IRepositoryBase<User_Coupon_Events> {}
  

 public partial interface IUser_OrderRepository : IRepositoryBase<User_Order> {}
  

 public partial interface IUser_Order_ReturnRepository : IRepositoryBase<User_Order_Return> {}
  

 public partial interface IWay_FoodMapRepository : IRepositoryBase<Way_FoodMap> {}
  

 public partial interface IWay_StoreMapRepository : IRepositoryBase<Way_StoreMap> {}
  

 public partial interface IWay_Verify_FoodItemRepository : IRepositoryBase<Way_Verify_FoodItem> {}
  

 public partial interface IWay_Verify_LinkRepository : IRepositoryBase<Way_Verify_Link> {}
  

 public partial interface IWay_VerifyItemRepository : IRepositoryBase<Way_VerifyItem> {}
  

 public partial interface IWay_VerifyRecordRepository : IRepositoryBase<Way_VerifyRecord> {}
  

}
