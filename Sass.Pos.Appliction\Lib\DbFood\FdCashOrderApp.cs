﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class FdCashOrderApp : AppBase<FdCashOrder>
    {
        public List<GetFdCashOrderInfoModel> GetOrderInfoByFdNo(GetFdCashOrderInfoContext context)
        {
            return Repository.FdCashOrder.GetCashOrderInfoByFdNo(context);
        }

        public List<GetFdCashOrderAndFdInfoModel> GetFdCashOrderData(GetFdCashOrderDataContext context)
        {
            return Repository.FdCashOrder.GetFdCashOrderData(context);
        }
    }
}
