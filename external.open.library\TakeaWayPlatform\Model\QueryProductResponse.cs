﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.Model
{
    public class QueryProductResponse
    {
        /// <summary>
        /// 第三方商品编号
        /// </summary>
        public string ProductId { get; set; }
        /// <summary>
        /// 第三方商品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 第三方商品类型
        /// </summary>
        public string ProductType { get; set; }
        /// <summary>
        /// 原价
        /// </summary>
        public decimal OriginAmount { get; set; }
        /// <summary>
        /// 实收价格
        /// </summary>
        public decimal ActualAmount { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
    }
}
