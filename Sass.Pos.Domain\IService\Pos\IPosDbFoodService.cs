﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IPosDbFoodService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetFoodDataModel>> GetFoodData(GetFoodDataContext context);

        [OperationContract]
        ResponseContext<List<GetFdDataExModel>> GetFdDataExByStore(GetFdDataExContext context);

        [OperationContract]
        ResponseContext<List<GetFtTypeSelectDataModel>> GetFtTypeSelectData(GetFtTypeSelectDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetFdTypeDataModel>> GetFdTypeData(GetFdTypeDataContext context);

        [OperationContract]
        ResponseContext<List<GetPrnSelectDataModel>> GetPrnSelectData(GetPrnSelectDataContext context);
    }
}
