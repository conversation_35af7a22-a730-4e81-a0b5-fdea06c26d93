﻿

using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.GrouponBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.GrouponBase
{
 public partial interface IActivityInfoRepository : IRepositoryBase<ActivityInfo> {}
  

 public partial interface ICacheTableRepository : IRepositoryBase<CacheTable> {}
  

 public partial interface ICanChangeGrouponRepository : IRepositoryBase<CanChangeGroupon> {}
  

 public partial interface ICodeInfoChangeLogRepository : IRepositoryBase<CodeInfoChangeLog> {}
  

 public partial interface ICouponUseInfoRepository : IRepositoryBase<CouponUseInfo> {}
  

 public partial interface ICouponUsersFreezeRepository : IRepositoryBase<CouponUsersFreeze> {}
  

 public partial interface IElectronCouponRepository : IRepositoryBase<ElectronCoupon> {}
  

 public partial interface IEntityCouponLogRepository : IRepositoryBase<EntityCouponLog> {}
  

 public partial interface IFdOrderNoRepository : IRepositoryBase<FdOrderNo> {}
  

 public partial interface IGrouponCodeInfoRepository : IRepositoryBase<GrouponCodeInfo> {}
  

 public partial interface IGrouponCodeInfobakRepository : IRepositoryBase<GrouponCodeInfobak> {}
  

 public partial interface IGrouponCodeInfobak1Repository : IRepositoryBase<GrouponCodeInfobak1> {}
  

 public partial interface IGrouponCommentInfoRepository : IRepositoryBase<GrouponCommentInfo> {}
  

 public partial interface IGrouponConsumeInfoRepository : IRepositoryBase<GrouponConsumeInfo> {}
  

 public partial interface IGrouponGiveCodeInfoRepository : IRepositoryBase<GrouponGiveCodeInfo> {}
  

 public partial interface IGrouponGiveInfoRepository : IRepositoryBase<GrouponGiveInfo> {}
  

 public partial interface IGrouponGiveMsgAlertRepository : IRepositoryBase<GrouponGiveMsgAlert> {}
  

 public partial interface IGrouponInfoRepository : IRepositoryBase<GrouponInfo> {}
  

 public partial interface IGrouponInfoApplyLogRepository : IRepositoryBase<GrouponInfoApplyLog> {}
  

 public partial interface IGrouponInfoLogRepository : IRepositoryBase<GrouponInfoLog> {}
  

 public partial interface IGrouponLockRepository : IRepositoryBase<GrouponLock> {}
  

 public partial interface IGrouponLockBakRepository : IRepositoryBase<GrouponLockBak> {}
  

 public partial interface IGrouponTeamRepository : IRepositoryBase<GrouponTeam> {}
  

 public partial interface Iillness_ChangeCodeRepository : IRepositoryBase<illness_ChangeCode> {}
  

 public partial interface Iillness_CodedelayRepository : IRepositoryBase<illness_Codedelay> {}
  

 public partial interface IInputPhoneInfoRepository : IRepositoryBase<InputPhoneInfo> {}
  

 public partial interface ILogRepository : IRepositoryBase<Log> {}
  

 public partial interface INApplyNoAndCodeRepository : IRepositoryBase<NApplyNoAndCode> {}
  

 public partial interface INApplyStatusInfoRepository : IRepositoryBase<NApplyStatusInfo> {}
  

 public partial interface INAreaInfoRepository : IRepositoryBase<NAreaInfo> {}
  

 public partial interface INBaseCodeInfoRepository : IRepositoryBase<NBaseCodeInfo> {}
  

 public partial interface INBuildModelRepository : IRepositoryBase<NBuildModel> {}
  

 public partial interface INCardLableRepository : IRepositoryBase<NCardLable> {}
  

 public partial interface INCardModelRepository : IRepositoryBase<NCardModel> {}
  

 public partial interface INCardTypeRepository : IRepositoryBase<NCardType> {}
  

 public partial interface INCardUseModelRepository : IRepositoryBase<NCardUseModel> {}
  

 public partial interface INCodeRefundRepository : IRepositoryBase<NCodeRefund> {}
  

 public partial interface INCodeStatusRepository : IRepositoryBase<NCodeStatus> {}
  

 public partial interface INDistributeSetGrouponRepository : IRepositoryBase<NDistributeSetGroupon> {}
  

 public partial interface INForceVerificationRepository : IRepositoryBase<NForceVerification> {}
  

 public partial interface INGrouponActivationInfoRepository : IRepositoryBase<NGrouponActivationInfo> {}
  

 public partial interface INGrouponApplyInfoRepository : IRepositoryBase<NGrouponApplyInfo> {}
  

 public partial interface INGrouponBillRepository : IRepositoryBase<NGrouponBill> {}
  

 public partial interface INGrouponBillStatusRepository : IRepositoryBase<NGrouponBillStatus> {}
  

 public partial interface INGrouponCodeConsumeInfoRepository : IRepositoryBase<NGrouponCodeConsumeInfo> {}
  

 public partial interface INGrouponCodeInfoRepository : IRepositoryBase<NGrouponCodeInfo> {}
  

 public partial interface INGrouponCodeRecoveryInfoRepository : IRepositoryBase<NGrouponCodeRecoveryInfo> {}
  

 public partial interface INGrouponConsumeBatchTypeRepository : IRepositoryBase<NGrouponConsumeBatchType> {}
  

 public partial interface INGrouponGiveCodeInfoRepository : IRepositoryBase<NGrouponGiveCodeInfo> {}
  

 public partial interface INGrouponInfoRepository : IRepositoryBase<NGrouponInfo> {}
  

 public partial interface INGrouponProjectRepository : IRepositoryBase<NGrouponProject> {}
  

 public partial interface INGrouponSaleStatisticsRepository : IRepositoryBase<NGrouponSaleStatistics> {}
  

 public partial interface INGrouponSelTypeInfoRepository : IRepositoryBase<NGrouponSelTypeInfo> {}
  

 public partial interface INGrouponSendInfoRepository : IRepositoryBase<NGrouponSendInfo> {}
  

 public partial interface INGrouponTemplateRepository : IRepositoryBase<NGrouponTemplate> {}
  

 public partial interface INGrouponTransactionRepository : IRepositoryBase<NGrouponTransaction> {}
  

 public partial interface INGrouponUseModelRepository : IRepositoryBase<NGrouponUseModel> {}
  

 public partial interface INHtmlTemplateRepository : IRepositoryBase<NHtmlTemplate> {}
  

 public partial interface INProjectTypeInfoRepository : IRepositoryBase<NProjectTypeInfo> {}
  

 public partial interface INSaleModelRepository : IRepositoryBase<NSaleModel> {}
  

 public partial interface INSendRecordRepository : IRepositoryBase<NSendRecord> {}
  

 public partial interface INSetGrouponRepository : IRepositoryBase<NSetGroupon> {}
  

 public partial interface INSubSetGrouponRepository : IRepositoryBase<NSubSetGroupon> {}
  

 public partial interface INValidModelRepository : IRepositoryBase<NValidModel> {}
  

 public partial interface IPhoneChangeOpenidRepository : IRepositoryBase<PhoneChangeOpenid> {}
  

 public partial interface IProductOrderInfoRepository : IRepositoryBase<ProductOrderInfo> {}
  

 public partial interface IProductSpecRepository : IRepositoryBase<ProductSpec> {}
  

 public partial interface IReceiveFillInfoRepository : IRepositoryBase<ReceiveFillInfo> {}
  

 public partial interface IReceiveRecordRepository : IRepositoryBase<ReceiveRecord> {}
  

 public partial interface ISeasonCode2019_TestRepository : IRepositoryBase<SeasonCode2019_Test> {}
  

 public partial interface ISendBrithdayCertificate_SearchRepository : IRepositoryBase<SendBrithdayCertificate_Search> {}
  

 public partial interface ISpecialCardApplyInfoRepository : IRepositoryBase<SpecialCardApplyInfo> {}
  

 public partial interface ISpecialCardDynamicInfoRepository : IRepositoryBase<SpecialCardDynamicInfo> {}
  

 public partial interface ISpecialCardStateInfoRepository : IRepositoryBase<SpecialCardStateInfo> {}
  

 public partial interface ISpecialOperatorStateInfoRepository : IRepositoryBase<SpecialOperatorStateInfo> {}
  

 public partial interface ITBLOGRepository : IRepositoryBase<TBLOG> {}
  

 public partial interface IWeChatCouponOrderNoRepository : IRepositoryBase<WeChatCouponOrderNo> {}
  

}
