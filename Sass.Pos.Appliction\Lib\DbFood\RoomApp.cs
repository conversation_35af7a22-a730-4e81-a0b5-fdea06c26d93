﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class RoomApp : AppBase<Room>
    {
        public GetRoomInfoModel GetRoomInfo(GetRoomInfoContext context)
        {
            return Repository.Room.GetRoomInfo(context);
        }

        public bool CheckOutPay(CheckOutPayContext context)
        {
            return Repository.Room.CheckOutPay(context);
        }

        public bool ContinueOrder(ContinueOrderContext context)
        {
            return Repository.Room.ContinueOrder(context);
        }

        public NewRoom GetRoom(string rmNo)
        {
            return Repository.Room.GetRoom(rmNo);
        }

        public GetRtInfoModel GetRmType(string rtNo)
        {
            return Repository.Room.GetRmType(rtNo);
        }

        public GetRoomCurrentNumber GetRoomNumber(string rmNo, string invNo)
        {
            return Repository.Room.GetRoomNumber(rmNo, invNo);
        }

        public int UpdateRoomRate(int DiscRate, int FixedDisc, string RmNo)
        {
            return Repository.Room.UpdateRoomRate(DiscRate, FixedDisc, RmNo);
        }

        public int ChangeRmStatus(ChangeRmStatusContext context)
        {
            return Repository.Room.ChangeRmStatus(context);
        }
    }
}
