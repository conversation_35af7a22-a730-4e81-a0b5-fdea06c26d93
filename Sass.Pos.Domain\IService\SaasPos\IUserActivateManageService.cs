﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IUserActivateManageService
    {
        [OperationContract]
        ResponseContext<UserActivateModel> UserActivate(UserActivateContext context);

        [OperationContract]
        ResponseContext<GetUserActivateModel> GetUserActivateData(GetUserActivateContext context);
    }
}
