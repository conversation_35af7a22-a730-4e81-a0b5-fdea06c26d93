﻿using ComponentApplicationServiceInterface.Context.Response;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Saas.Pos.Common.Exceptions;
using Saas.Pos.Common.Extend;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.ServiceClient;
using Saas.Pos.Common.Tools;
using Saas.Pos.Drive.SaasPos.ExportRecord;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.BI.BusinessReport
{
    public class DbFoodBusinessReport : BIBase
    {
        /// <summary>
        /// 获取门店营业数据
        /// </summary>
        /// <returns></returns>
        public ResponseContext<List<DbFoodBusinessReportModel>> GetBusinessData(DbFoodBusinessReportContext context)
        {
            return ActionFun.Run(context, () =>
            {
                if ((context.EndDate - context.StartDate).TotalDays > 31)
                    throw new ExMessage("最多导出一个月数据！");

                var data = app.DbFood.FdInv.GetBusinessData(context);

                var result = data.GroupBy(w => w.InvNo).Select(w => new DbFoodBusinessReportModel()
                {
                    InvNo = w.Key,
                    RmNo = w.FirstOrDefault().RmNo,
                    RmArea = w.FirstOrDefault().RmArea,
                    MemberNo = w.FirstOrDefault().MemberNo,
                    Number = w.FirstOrDefault().InNumbers,
                    Tot = w.FirstOrDefault().Tot,
                    WorkDate = w.FirstOrDefault().WorkDate,
                    Items = w.Select(item => new FdCashOrderItem()
                    {
                        FdCashType = item.CashType,
                        FdNo = item.FdNo
                    }).ToList()
                }).ToList();

                return result;
            });
        }

        /// <summary>
        /// 导出门店营业报表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<ReturnBool> ExprotBusinessData(DbFoodBusinessReportContext context)
        {
            var fileKey = Guid.NewGuid();
            var shop = StoreHelper.StoreManage.GetStoreInfo(context.ShopId);
            var dirPath = ConfigurationManager.AppSettings["OrderExcel"].ToString();
            var filePath = Path.Combine(dirPath, fileKey.ToString() + ".xlsx");
            var fileName = $"{shop.StoreName}_每日业绩报表({DateTime.Now.ToString("yyyyMMddHHmmss")})";
            long fileSize = 0;
            var resp = ActionFun.Run(context, () =>
            {
                var fileDrive = new FileDownLoadManage();

                fileDrive.InsertDownLoad(new ExportExcelExContext()
                {
                    fileName = fileName + ".xlsx",
                    DealType = 1,
                    BookUserId = context.BookUserId,
                    BookUserName = context.BookUserName,
                    FileKey = fileKey,
                    FileForamt = ".xlsx"
                });

                return new ReturnBool() { Boole = true };
            });

            Task.Run(() =>
            {
                var fileData = app.SaasPos.Sys_FileDownLoad.FindEntity(w => w.FileKey == fileKey);
                try
                {
                    var posClient = ServiceClientBase.GetPosClient(context.ShopId);
                    var contextEx = context.Clone<SERVICE.PROXY.PosService.DbFoodBusinessReportContext>();
                    var respData = posClient.GetBusinessData(contextEx);
                    if (respData.state != ResponseType.success)
                        throw new ExMessage("查询门店订单失败！" + respData.message);

                    #region 基础数据查询
                    //查询开房数据
                    var openCacheData = app.Rms.opencacheinfo.GetOpenCacheList(new GetBookCacheContext()
                    {
                        ShopId = context.ShopId,
                        StartDate = context.StartDate.ToString("yyyyMMdd"),
                        EndDate = context.EndDate.ToString("yyyyMMdd")
                    });

                    var sumData = (from open in openCacheData
                                   join inv in respData.data
                                   on open.Invno equals inv.InvNo
                                   select new
                                   {
                                       OpenData = open,
                                       InvData = inv
                                   }).ToList();

                    //当前门店的所有白天档时段
                    var timeNos = openCacheData.Select(w => w.Beg_Key).Distinct().ToList();
                    var shopTimeNos = AppSingle.App.Storage.DbDat.ShopTimeList.Where(w => timeNos.Contains(w.TimeNo) && w.TimeMode == 1 && w.ShopId == context.ShopId).Select(w => w.TimeNo).ToList();
                    var timeInfos = AppSingle.App.Storage.DbDat.TimeList.Where(w => shopTimeNos.Contains(w.TimeNo)).OrderBy(w => w.BegTime).ToList();
                    //堂会门店下单消费类型
                    var thFdCashTypes = sumData.Where(w => w.InvData.RmArea == 0 && !shopTimeNos.Contains(w.OpenData.Beg_Key)).
                        Select(w => w.InvData.Items.Select(x => x.FdCashType)).SelectMany(w => w).Distinct().ToList();
                    //名堂门店下单消费类型
                    var kbossFdCashTypes = sumData.Where(w => w.InvData.RmArea == 1 && !shopTimeNos.Contains(w.OpenData.Beg_Key))
                        .Select(w => w.InvData.Items.Select(x => x.FdCashType)).SelectMany(w => w).Distinct().ToList();
                    //白天档消费模式
                    var cashTypes = openCacheData.Where(w => shopTimeNos.Contains(w.Beg_Key)).Select(w => new { w.CtName, w.CtNo }).Distinct().ToList();

                    #endregion

                    IWorkbook workbook = new XSSFWorkbook();
                    ISheet sheet = workbook.CreateSheet(fileName);

                    ICellStyle cellStyle = workbook.CreateCellStyle();
                    cellStyle.Alignment = HorizontalAlignment.Center;
                    cellStyle.VerticalAlignment = VerticalAlignment.Center;
                    var titleList = new List<string>() { "日期", "星期", "总营业收入", "白天档", "晚间档", "全天总营客批数", "白天档", "晚间档" };

                    #region 表头单元格合并赋值
                    //合并标提
                    var totalIndex = 8 + (cashTypes.Count * timeInfos.Count) + timeInfos.Count + thFdCashTypes.Count + kbossFdCashTypes.Count + 5;
                    CellRangeAddress titleRange = new CellRangeAddress(0, 0, 0, totalIndex);
                    sheet.AddMergedRegion(titleRange);
                    IRow titleRow = sheet.CreateRow(0);
                    ICell titleCell = titleRow.CreateCell(0);
                    titleCell.SetCellValue($"{context.StartDate.ToString("MM月dd日")}-{context.EndDate.ToString("MM月dd日")}{shop.StoreName}每日业绩报表");
                    titleCell.CellStyle = cellStyle;
                    //合并门店
                    CellRangeAddress shopRange = new CellRangeAddress(1, 1, 0, 1);
                    sheet.AddMergedRegion(shopRange);
                    IRow twoRow = sheet.CreateRow(1);
                    ICell shopCell = twoRow.CreateCell(0);
                    shopCell.SetCellValue(shop.StoreName);
                    shopCell.CellStyle = cellStyle;
                    //合并每日营收数据
                    CellRangeAddress bussinessRange = new CellRangeAddress(1, 1, 2, 4);
                    sheet.AddMergedRegion(bussinessRange);
                    ICell bussinessCell = twoRow.CreateCell(2);
                    bussinessCell.SetCellValue("每日营收数据");
                    bussinessCell.CellStyle = cellStyle;
                    //合并带客数据
                    CellRangeAddress customRange = new CellRangeAddress(1, 1, 5, 7);
                    sheet.AddMergedRegion(customRange);
                    ICell customCell = twoRow.CreateCell(5);
                    customCell.SetCellValue("每日带客数据");
                    customCell.CellStyle = cellStyle;

                    //第三行
                    IRow threeRow = sheet.CreateRow(2);
                    for (int i = 0; i < titleList.Count; i++)
                    {
                        CellRangeAddress minTitleRange = new CellRangeAddress(2, 3, 0 + i, 0 + i);
                        sheet.AddMergedRegion(minTitleRange);

                        ICell minTitleCell = threeRow.CreateCell(i);
                        minTitleCell.SetCellValue(titleList[i]);
                        minTitleCell.CellStyle = cellStyle;
                    }

                    //第四行
                    IRow fourRow = sheet.CreateRow(3);

                    #endregion

                    //获取跨越总天数
                    var totalDays = (context.EndDate.Date - context.StartDate.Date).TotalDays;

                    for (int i = 0; i <= totalDays; i++)
                    {
                        var date = context.StartDate.AddDays(i);
                        if (i == 0)
                        {
                            #region 动态单元格合并

                            var cellIndex = 8 + (cashTypes.Count * timeInfos.Count) + timeInfos.Count;
                            //合并K+餐带客批数
                            ICell daytitCell = twoRow.CreateCell(8);
                            CellRangeAddress dayRange = new CellRangeAddress(1, 1, 8, cellIndex);
                            sheet.AddMergedRegion(dayRange);
                            daytitCell.SetCellValue("K+餐带客批数");
                            daytitCell.CellStyle = cellStyle;

                            ICell dayNumberCell = twoRow.CreateCell(cellIndex + 1);
                            CellRangeAddress dayNumberRange = new CellRangeAddress(1, 3, cellIndex + 1, cellIndex + 1);
                            sheet.AddMergedRegion(dayNumberRange);
                            dayNumberCell.SetCellValue("当天用餐人数");
                            dayNumberCell.CellStyle = cellStyle;

                            cellIndex = cellIndex + 2;
                            ICell theveningCell = twoRow.CreateCell(cellIndex);
                            CellRangeAddress theveningRange = new CellRangeAddress(1, 1, cellIndex, cellIndex + 1 + thFdCashTypes.Count);
                            sheet.AddMergedRegion(theveningRange);
                            theveningCell.SetCellValue("堂会晚档带客批数");
                            theveningCell.CellStyle = cellStyle;

                            //重新统计当前索引
                            cellIndex = cellIndex + 2 + thFdCashTypes.Count;
                            ICell kbossEveningCell = twoRow.CreateCell(cellIndex);
                            CellRangeAddress kbossEveningRange = new CellRangeAddress(1, 1, cellIndex, cellIndex + 1 + kbossFdCashTypes.Count);
                            sheet.AddMergedRegion(kbossEveningRange);
                            kbossEveningCell.SetCellValue("名堂区晚档带客批数");
                            kbossEveningCell.CellStyle = cellStyle;

                            #endregion

                            #region 合并时段与晚档数据

                            var newCellIndex = 8;
                            for (int j = 0; j < timeInfos.Count; j++)
                            {
                                CellRangeAddress timeRange = new CellRangeAddress(2, 2, newCellIndex, newCellIndex + cashTypes.Count);
                                sheet.AddMergedRegion(timeRange);
                                ICell timeCell = threeRow.CreateCell(newCellIndex);
                                timeCell.SetCellValue(timeInfos[j].TimeName);
                                timeCell.CellStyle = cellStyle;

                                //循环每个时段里面的消费模式
                                for (int k = 0; k < cashTypes.Count; k++)
                                {
                                    ICell cashTypeCell = fourRow.CreateCell(newCellIndex + k);
                                    cashTypeCell.SetCellValue(cashTypes[k].CtName);
                                    cashTypeCell.CellStyle = cellStyle;
                                }

                                ICell subTotCell = fourRow.CreateCell(newCellIndex + cashTypes.Count);
                                subTotCell.SetCellValue("小计");
                                subTotCell.CellStyle = cellStyle;
                                //当前下标+1就是每个都多一个小计，再+1是为了下一个进来不再+1，就是可用的下标
                                newCellIndex += cashTypes.Count + 1;
                            }

                            //合并单元格
                            CellRangeAddress subTotBatchRange = new CellRangeAddress(2, 3, newCellIndex, newCellIndex);
                            sheet.AddMergedRegion(subTotBatchRange);
                            var subTotBatchCell = threeRow.CreateCell(newCellIndex);
                            subTotBatchCell.SetCellValue("小计批数");
                            subTotBatchCell.CellStyle = cellStyle;

                            //堂会区晚档 下标需要加1，因为这里多了一个当天用餐人数
                            newCellIndex = newCellIndex + 1 + 1;
                            for (int k = 0; k < thFdCashTypes.Count; k++)
                            {
                                CellRangeAddress thCashTypeRange = new CellRangeAddress(2, 3, newCellIndex + k, newCellIndex + k);
                                sheet.AddMergedRegion(thCashTypeRange);

                                ICell thCashTypeCell = threeRow.CreateCell(newCellIndex + k);
                                thCashTypeCell.SetCellValue(thFdCashTypes[k]);
                                thCashTypeCell.CellStyle = cellStyle;
                            }

                            newCellIndex = newCellIndex + thFdCashTypes.Count;
                            CellRangeAddress thSubBatchRange = new CellRangeAddress(2, 3, newCellIndex, newCellIndex);
                            sheet.AddMergedRegion(thSubBatchRange);
                            ICell thSubBatchCell = threeRow.CreateCell(newCellIndex);
                            thSubBatchCell.SetCellValue("小计批数");
                            thSubBatchCell.CellStyle = cellStyle;

                            CellRangeAddress thTurnoverRange = new CellRangeAddress(2, 3, newCellIndex + 1, newCellIndex + 1);
                            sheet.AddMergedRegion(thTurnoverRange);
                            ICell thTurnoverCell = threeRow.CreateCell(newCellIndex + 1);
                            thTurnoverCell.SetCellValue("营业额");
                            thTurnoverCell.CellStyle = cellStyle;

                            //名堂区晚档
                            newCellIndex = newCellIndex + 2;
                            for (int k = 0; k < kbossFdCashTypes.Count; k++)
                            {
                                CellRangeAddress kbossCashTypeRange = new CellRangeAddress(2, 3, newCellIndex + k, newCellIndex + k);
                                sheet.AddMergedRegion(kbossCashTypeRange);

                                ICell kbossCashTypeCell = threeRow.CreateCell(newCellIndex + k);
                                kbossCashTypeCell.SetCellValue(kbossFdCashTypes[k]);
                                kbossCashTypeCell.CellStyle = cellStyle;
                            }

                            newCellIndex = newCellIndex + kbossFdCashTypes.Count;
                            CellRangeAddress kbSubBatchRange = new CellRangeAddress(2, 3, newCellIndex, newCellIndex);
                            sheet.AddMergedRegion(kbSubBatchRange);
                            ICell kbSubBatchCell = threeRow.CreateCell(newCellIndex);
                            kbSubBatchCell.SetCellValue("小计批数");
                            kbSubBatchCell.CellStyle = cellStyle;

                            CellRangeAddress kbTurnoverRange = new CellRangeAddress(2, 3, newCellIndex + 1, newCellIndex + 1);
                            sheet.AddMergedRegion(kbTurnoverRange);
                            ICell kbTurnoverCell = threeRow.CreateCell(newCellIndex + 1);
                            kbTurnoverCell.SetCellValue("营业额");
                            kbTurnoverCell.CellStyle = cellStyle;

                            #endregion
                        }

                        #region 数据合并
                        //将数据按照每日隔开
                        var sumDateData = sumData.Where(w => w.InvData.WorkDate == date.ToString("yyyyMMdd")).ToList();
                        IRow row = sheet.CreateRow(i + 4);
                        ICell dateCell = row.CreateCell(0);
                        dateCell.SetCellValue(date.ToString("MM月dd日"));
                        ICell weekCell = row.CreateCell(1);
                        weekCell.SetCellValue(Enum.GetName(typeof(ShopWeekTimeEnum), (int)date.DayOfWeek));
                        ICell totalCell = row.CreateCell(2);
                        totalCell.SetCellValue(sumDateData.Sum(w => w.InvData.Tot));
                        ICell dayCell = row.CreateCell(3);
                        ICell eveningCell = row.CreateCell(4);
                        dayCell.SetCellValue(sumDateData.Where(w => shopTimeNos.Contains(w.OpenData.Beg_Key)).Sum(w => w.InvData.Tot));
                        eveningCell.SetCellValue(sumDateData.Where(w => !shopTimeNos.Contains(w.OpenData.Beg_Key)).Sum(w => w.InvData.Tot));
                        ICell totNumCell = row.CreateCell(5);
                        ICell dayNumCell = row.CreateCell(6);
                        ICell eveningNumCell = row.CreateCell(7);
                        totNumCell.SetCellValue(sumDateData.Count);
                        dayNumCell.SetCellValue(sumDateData.Count(w => shopTimeNos.Contains(w.OpenData.Beg_Key)));
                        eveningNumCell.SetCellValue(sumDateData.Count(w => !shopTimeNos.Contains(w.OpenData.Beg_Key)));

                        var currentIndex = 8;//初始化单元格的索引
                                             //时段带客赋值
                        for (int j = 0; j < timeInfos.Count; j++)
                        {
                            var data = sumDateData.Where(w => w.OpenData.Beg_Key == timeInfos[j].TimeNo).ToList();

                            for (int k = 0; k < cashTypes.Count; k++)
                            {
                                ICell timeNumCell = row.CreateCell(currentIndex);
                                timeNumCell.SetCellValue(data.Count(w => w.OpenData.CtNo == cashTypes[k].CtNo));
                                currentIndex++;
                            }

                            ICell timeNumSubTotCell = row.CreateCell(currentIndex);
                            timeNumSubTotCell.SetCellValue(data.Count);
                            currentIndex++;
                        }
                        //小计批数
                        var timeNumTotCell = row.CreateCell(currentIndex);
                        timeNumTotCell.SetCellValue(sumDateData.Count(w => shopTimeNos.Contains(w.OpenData.Beg_Key)));
                        //当天用餐人数
                        currentIndex += 1;
                        var allNumCell = row.CreateCell(currentIndex);
                        allNumCell.SetCellValue(sumDateData.Sum(w => w.InvData.Number));
                        currentIndex += 1;
                        //堂会晚档带客
                        for (int k = 0; k < thFdCashTypes.Count; k++)
                        {
                            var thEveningCell = row.CreateCell(currentIndex);
                            thEveningCell.SetCellValue(sumDateData.Count(w => !shopTimeNos.Contains(w.OpenData.Beg_Key) && w.InvData.Items.Any(x => x.FdCashType == thFdCashTypes[k]) && w.InvData.RmArea == 0));
                            currentIndex++;
                        }
                        var thSubBatchDataCell = row.CreateCell(currentIndex);
                        thSubBatchDataCell.SetCellValue(sumDateData.Count(w => !shopTimeNos.Contains(w.OpenData.Beg_Key) && w.InvData.RmArea == 0));
                        var thSubAmountCell = row.CreateCell(currentIndex + 1);
                        thSubAmountCell.SetCellValue(sumDateData.Where(w => !shopTimeNos.Contains(w.OpenData.Beg_Key) && w.InvData.RmArea == 0).Sum(w => w.InvData.Tot));

                        currentIndex += 2;
                        //名堂区晚档带客
                        for (int k = 0; k < kbossFdCashTypes.Count; k++)
                        {
                            var kbEveningCell = row.CreateCell(currentIndex);
                            kbEveningCell.SetCellValue(sumDateData.Count(w => !shopTimeNos.Contains(w.OpenData.Beg_Key) && w.InvData.Items.Any(x => x.FdCashType == kbossFdCashTypes[k]) && w.InvData.RmArea != 0));
                            currentIndex++;
                        }

                        var kbSubBatchDataCell = row.CreateCell(currentIndex);
                        kbSubBatchDataCell.SetCellValue(sumDateData.Count(w => !shopTimeNos.Contains(w.OpenData.Beg_Key) && w.InvData.RmArea != 0));
                        var kbSubAmountCell = row.CreateCell(currentIndex + 1);
                        kbSubAmountCell.SetCellValue(sumDateData.Where(w => !shopTimeNos.Contains(w.OpenData.Beg_Key) && w.InvData.RmArea != 0).Sum(w => w.InvData.Tot));

                        #endregion
                    }

                    using (var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                    {
                        workbook.Write(fs);
                    }

                    workbook.Close();
                    FileInfo fileInfo = new FileInfo(filePath);
                    fileSize = fileInfo.Length;
                    filePath = Path.GetFullPath(filePath);
                    fileData.FileSize = ((double)fileSize / 1024).ToString("0.00") + "KB";
                    fileData.State = 2;
                    fileData.Address = filePath;
                }
                catch (ClientException ex)
                {
                    LogHelper.Error("每日业绩报表导出异常！" + ex.Message);
                    fileData.State = 3;
                    fileData.FileErrMsg = ex.Message;
                }
                finally
                {
                    app.SaasPos.Sys_FileDownLoad.Update(fileData);
                }
            });

            return resp;
        }

        /// <summary>
        /// 获取门店营业列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<bool> GetBusinessDataList(DbFoodBusinessListContext context)
        {
            return ActionFun.Run(context, () =>
            {

                return true;
            });
        }
    }
}
