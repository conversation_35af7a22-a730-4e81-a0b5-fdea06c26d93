﻿using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.CouponManage
{
    public interface ICouponManageCancel
    {
        /// <summary>
        /// 优惠卷撤销
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ReturnInt CouponDataCancel(CouponDataCancelContext context);
    }
}
