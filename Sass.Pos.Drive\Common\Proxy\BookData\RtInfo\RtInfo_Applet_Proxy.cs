﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    /// <summary>
    /// 房型代理小程序端
    /// </summary>
    public class RtInfo_Applet_Proxy : RtBaseProxy
    {
        /// <summary>
        /// 获取是否存在门店房型配置
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="shopId"></param>
        /// <param name="rtNo"></param>
        /// <returns></returns>
        public static bool GetBookRtInfo(DateTime startTime, DateTime endTime, int shopId, string rtNo)
        {
            return AppSingle.App.Storage.BookingDat.WorkNotRtInfoList.FirstOrDefault(x => x.ShopId == shopId && x.WorkTimeStart <= startTime && x.WorkTimeEnd >= endTime && x.RtNo == rtNo) == null;
        }

        /// <summary>
        /// 获取超限预订数
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="shopId">门店编号</param>
        /// <param name="rtNo">房型编号</param>
        /// <returns></returns>
        public static int GetBookOutNumber(DateTime startTime, DateTime endTime, int shopId, string rtNo)
        {
            var bookOut = AppSingle.App.Storage.BookingDat.WorkBookOutList.FirstOrDefault(w => w.WorkTimeStart <= startTime && w.WorkTimeEnd >= endTime && w.ShopId == shopId && w.RtNo == rtNo);

            return bookOut == null ? 0 : bookOut.Number;
        }

        /// <summary>
        /// 根据传入人数，获取最佳房型
        /// </summary>
        /// <param name="shopId">门店</param>
        /// <param name="timeNo">时段</param>
        /// <param name="numbers">人数</param>
        /// <returns></returns>
        public static string GetBestRtNo(int shopId, string timeNo, int numbers)
        {
            return AppSingle.App.Storage.BookingDat.RoomDataStorage.Where(w => w.ShopId == shopId && w.TimeNo == timeNo && w.NumberMax >= numbers).OrderBy(w => w.NumberMax).FirstOrDefault().RtNo;
        }

        public override object GetShopRtType(DateTime date, string timeNo)
        {
            return GetBookRtType(date,timeNo).ToList();
        }
    }
}
