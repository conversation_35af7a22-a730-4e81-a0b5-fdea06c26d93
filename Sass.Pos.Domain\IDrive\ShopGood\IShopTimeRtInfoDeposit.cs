﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive
{
    public interface IShopTimeRtInfoDeposit
    {
        /// <summary>
        /// 根据门店，房型，时段获取订金
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<GetShopTimeRtInfoDepositModel> GetDeposit(GetShopTimeRtInfoDepositContext context);

        ResponseContext<ReturnInt> SaveDeposit(SaveShopTimeRtInfoDepositContext context);

        ResponseContext<DeleteRtinfoDepositDataModel> DeleteRtinfoDepositData(DeleteRtinfoDepositDataContext context);

        ResponseContext<ReturnInt> ChangeDepositInfoStatus(ChangeDepositInfoStatusContext context);

        ResponseContext<RespPaginationModel<GetShopTimeRtInfoDepositListModel>> GetDepositList(GetDepositListContext context);

        ResponseContext<Shop_TimeRtInfoDeposit> GetTimeRtDepositInfo(GetTimeRtDepositInfoContext context);
    }
}
