﻿using Saas.Pos.Application.Lib.SaasPos;
using Saas.Pos.Domain.IRepository.Bar;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using Saas.Pos.Model.Enum;
using Saas.Pos.Repository.Lib.Bar;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.Bar
{
    public class WineStockApp
    {
        public WineStockRepository WineRepository = new WineStockRepository();
        /// <summary>
        /// 查询需要发送短信的用户信息(小于30天大于15天)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<custData> GetWineStockData()
        {
            return WineRepository.GetWineStockData();
        }

        /// <summary>
        /// 查询需要发送短信的用户信息(小于15天大于0天)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<custData> GetWineStockDataEx()
        {
            return WineRepository.GetWineStockDataEx();
        }

        /// <summary>
        /// 已成功发送短信(添加发送信息记录)
        /// </summary>
        /// <returns></returns>
        public int EditWineStockData(List<EditSendReardContext> context)
        {
            return WineRepository.EditWineStockData(context);
        }

        /// <summary>
        /// 查询用户存酒列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<UserCustWineData> GetUserCustData(UserCustDataContext context)
        {
            //主要数据
            var mainData = WineRepository.GetUserCustMainData(context);

            mainData = mainData.Where(i => i.ExDatetime.AddDays(context.AppDay) >= DateTime.Now).ToList();
            //明细数据
            var detailData = WineRepository.GetUserCustDetailData(new GetUserCustDetailDataContext() { iKeyMsgs = mainData.Select(i => i.ikeyMsg).ToList() });

            //合并之后数据
            var data = mainData.Select(i => new UserCustWineData()
            {
                CustData = i,
                DrinksDatas = detailData.Where(j => j.ikeyMsg == i.ikeyMsg).ToList()
            }).ToList();

            return data;
        }

        /// <summary>
        /// 员工查询存酒数据
        /// </summary>
        /// <param name="context"></param>
        /// <param name="ExDays">过期天数</param>
        /// <returns></returns>
        public List<GetWineDataByBarIdModelEx> GetWineDataByBarId(GetWineDataByBarIdContext context, int ExDays)
        {
            //用户信息
            var mainData = WineRepository.GetWineMainData(context, ExDays);

            //明细数据
            var detailData = WineRepository.GetUserCustDetailData(new GetUserCustDetailDataContext() { iKeyMsgs = mainData.Select(i => i.iKeyMsg).ToList() });

            //合并之后数据
            var data = mainData.Select(i => new GetWineDataByBarIdModelEx()
            {
                MsgInfo = i,
                DrinksDatas = detailData.Where(j => j.ikeyMsg == i.iKeyMsg).ToList()

            }).ToList();

            return data;
        }

        public List<UserDrinksData> GetDrinkList(string MsgPassWord, int DrShopId)
        {
            List<UserDrinksData> data = new List<UserDrinksData>();

            //存酒授权信息
            var list = WineRepository.GetMsgInfoData(MsgPassWord, string.Empty);
            //存酒信息为空
            if (list == null || string.IsNullOrEmpty(list.iKeyMsg))
                return data;

            var nowDate = DateTime.Now;
            var MsgPassWordEx = string.Empty;//待会判断的值


            //判断过期
            if (list.MsgStatus == 3)
            {
                //判断有无授权信息
                if (list.DrCheckId != null && list.DrTime != null)
                    //判断授权时间是否大于当前时间:是就取
                    if (list.DrTime >= nowDate)
                        MsgPassWordEx = list.MsgPassword;
            }
            else if (list.MsgStatus == 2)
                MsgPassWordEx = list.MsgPassword;
            if (string.IsNullOrEmpty(MsgPassWordEx))
                return data;
            //获取存酒明细信息
            data = WineRepository.GetDrinkList(MsgPassWordEx, DrShopId);
            return data;
        }

        /// <summary>
        /// vue管理平台查询用户存酒数据(用户信息)
        /// </summary>
        /// <param name="context"></param>
        /// <param name="ExDays"></param>
        /// <returns></returns>
        public List<GetWineMainDataOnVueModel> GetWineMainDataOnVue(GetUserWineDataOnVueContext context)
        {
            return WineRepository.GetWineMainDataOnVue(context);
        }

        /// <summary>
        /// vue管理平台查询用户存酒数据(存酒明细列表)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetWineDetailDataOnVueModel> GetWineDetailDataOnVue(GetWineDetailDataOnVueContext context)
        {
            return WineRepository.GetWineDetailDataOnVue(context);
        }

        /// <summary>
        /// 员工查询存酒明细
        /// </summary>
        /// <param name="iKeyMsg"></param>
        /// <param name="ExDays"></param>
        /// <returns></returns>
        public GetDrinkListByUserContext GetDrinkListByUser(string iKeyMsg, int ExDays, int DrShopId)
        {
            GetDrinkListByUserContext data = new GetDrinkListByUserContext();

            //存酒授权信息
            var list = WineRepository.GetMsgInfoData(string.Empty, iKeyMsg);
            //存酒信息为空
            if (list == null || string.IsNullOrEmpty(list.iKeyMsg))
                return data;

            var nowDate = DateTime.Now;
            var MsgPassWordEx = string.Empty;//待会判断的值

            //判断过期
            if (list.MsgStatus == 3)
            {
                //判断有无授权信息
                if (list.DrCheckId != null && list.DrTime != null)
                {
                    //判断授权时间是否大于当前时间:是就取
                    if (list.DrTime >= nowDate)
                        MsgPassWordEx = list.MsgPassword;
                }
                //员工权限可下单过期多少天数据
                else if (ExDays > 0)
                {
                    if (list.ExDatetime.AddDays(ExDays) >= nowDate)
                        MsgPassWordEx = list.MsgPassword;
                }
            }
            else if (list.MsgStatus == 2)
                MsgPassWordEx = list.MsgPassword;
            if (string.IsNullOrEmpty(MsgPassWordEx))
                return data;
            data.MsgInfo = list;
            //获取存酒明细信息
            data.Drinks = WineRepository.GetDrinkList(MsgPassWordEx, DrShopId);
            return data;
        }

        public List<GetFoodNosModel> GetFoodNos(GetFoodNosContext context)
        {
            return WineRepository.GetFoodNos(context);
        }

        /// <summary>
        /// 修改存酒表状态
        /// </summary>
        /// <param name="iKeyMsg"></param>
        /// <returns></returns>
        public int UpdateMsgInfoState(string MsgPassWord, string BarName, string DrMemory, int ShopId, string RmNo)
        {
            return WineRepository.UpdateMsgInfoState(MsgPassWord, BarName, DrMemory, ShopId, RmNo);
        }

        /// <summary>
        /// 根据存酒key修改存酒表状态
        /// </summary>
        /// <param name="iKeyMsg"></param>
        /// <returns></returns>
        public int UpdateMsgInfoStateByKey(string iKeyMsg, string BarName, string DrMemory, int ShopId, string RmNo)
        {
            return WineRepository.UpdateMsgInfoStateByKey(iKeyMsg, BarName, DrMemory, ShopId, RmNo);
        }


        /// <summary>
        /// 存酒
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public int InsertWineUser(SaveWineUserContext context, WineUserStatusEnum MsgStatus)
        {
            return WineRepository.InsertWineUser(context, MsgStatus);
        }

        public string GetMsgPassWord(int shopid)
        {
            Random random = new Random();
            string msgPassWord;
            bool isUnique;

            do
            {
                // 生成三个范围内的随机数
                int number1 = random.Next(100, 1000); // 生成100到999之间的随机数
                int number2 = random.Next(100, 1000); // 生成100到999之间的随机数
                int number3 = random.Next(100, 1000); // 生成100到999之间的随机数

                // 构建形如 "000-000-000" 的随机数字符串
                msgPassWord = $"{shopid}-{number1}-{number2}-{number3}";

                // 检查生成的随机密码是否在数据库中唯一
                string sql = $@"select count(1) as msgCount from msginfo where MsgPassword='{msgPassWord}'";
                var msgCount = BarMysqlHelper.ado.QueryList<GetMsgPassWordModel>(sql).FirstOrDefault();

                // 如果msgCount为空或msgCount.msgCount为0，则表示唯一
                isUnique = msgCount == null || msgCount.msgCount == 0;

            } while (!isUnique); // 如果不唯一，则继续生成新的随机密码

            return msgPassWord;
        }

        /// <summary>
        /// 查询商品类型信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetFdTypeDataModel> GetFdTypeDatas(GetFdTypeDataContext context)
        {
            return WineRepository.GetFdTypeDatas(context);
        }

        /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetFdDataModel> GetFdDatas(GetFdDataContext context)
        {
            return WineRepository.GetFdDatas(context);
        }

        /// <summary>
        /// 执行编辑操作
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public int EditDataBySql(SaveDrCheckDataContext context)
        {
            var nowData = DateTime.Now;
            var checkSql = $@"insert into drcheckinfo(DrTime,UserId,UserName,InTime,DrCheckRemark,iKeyMsg)
                    values('{nowData.AddHours(24)}','{context.Wkno}','{context.Name}','{nowData}','{context.DrCheckRemark}',
                    '{context.iKeyMsg}');
                    UPDATE msginfo set DrCheckId=LAST_INSERT_ID() WHERE iKeyMsg='{context.iKeyMsg}'";
            return WineRepository.EditDataBySql(checkSql);
        }

        /// <summary>
        /// 查询sql语句数据的条数
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public GetSelectDataCount GetSelectDataCount(string iKeyMsg)
        {
            var getSql = $@"select count(1) Count from msginfo where iKeyMsg='{iKeyMsg}'";
            return WineRepository.GetSelectDataCount(getSql);
        }

        /// <summary>
        /// 查询授权信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetDrCheckDataModel> GetDrCheckData(GetDrCheckDataContext context)
        {
            return WineRepository.GetDrCheckData(context);
        }

        /// <summary>
        /// 查询授权明细数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetDrCheckDetailDataModel> GetDrCheckDetailData(GetDrCheckDetailDataContext context)
        {
            //获取授权明细数据
            var data = WineRepository.GetDrCheckDetailData(context);
            return data;
        }

        /// <summary>
        /// 查询酒水明细
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public GetWineDataModel GetWineData(GetWineDataContext context)
        {
            //查询商品信息
            var fddata = WineRepository.GetfdLists(context.ShopId);

            //查询商品分类信息
            var ftdata = WineRepository.GetfdTypeLists(context.ShopId);

            //查询商品单位信息
            var unitdata = WineRepository.GetUnitLists();

            var data = new GetWineDataModel()
            {
                fdList = fddata,
                fdType = ftdata,
                unit = unitdata.Select(i => i.Unit).ToList()
            };

            return data;
        }

        /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetfdListEx> GetFdList(GetfdListContext context)
        {
            return WineRepository.GetfdListsEx(context);
        }

        /// <summary>
        /// 查询商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetfdTypeList> GetFtList(GetftListContext context)
        {
            return WineRepository.GetfdTypeListsEx(context);
        }

        /// <summary>
        /// 根据门店查询商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetfdTypeList> GetfdTypeLists(int ShopId)
        {
            return WineRepository.GetfdTypeLists(ShopId);
        }

        /// <summary>
        /// 查询单位信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetUnitList> GetUnitList(GetUnitListContext context)
        {
            return WineRepository.GetUnitListsEx(context);
        }

        /// <summary>
        /// 新增商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public EditFdDataModel InsertFdData(List<EditFdDataContext> context)
        {
            //查询有没有已存在的商品编号
            var fdnos = context.Select(i => $@"'{i.FdNo}'").ToArray();
            var sqlCount = $@"select Count(1) Count from barfood where fdno in ({string.Join(",", fdnos)})";
            var count = WineRepository.GetSelectDataCount(sqlCount);
            if (fdnos.Length != count.Count && count.Count > 0)
                throw new ExMessage("有重复的商品编号!");
            if (fdnos.Length == count.Count)
                throw new ExMessage("商品编号全部重复!");

            var sql = string.Empty;
            foreach (var item in context)
            {
                sql += $@"insert into barfood 
                       (FtNo,FdNo,FdCName,SumTotal,ShopId,Number) 
                        values('{item.FtNo}','{item.FdNo}','{item.FdCName}',{item.SumTotal},{item.ShopId},'{item.Number}');";
            }

            var editCount = WineRepository.EditDataBySql(sql);
            if (editCount <= 0)
                throw new ExMessage("新增商品信息失败!");

            return new EditFdDataModel();
        }

        /// <summary>
        /// 编辑商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public EditFdDataModel EditFdData(EditFdDataContext context)
        {
            //查询有没有已存在的商品编号
            var sqlCount = $@"select Count(1) Count from barfood where fdno = '{context.FdNo}'";
            var count = WineRepository.GetSelectDataCount(sqlCount);
            if (count.Count <= 0)
                throw new ExMessage("找不到商品信息!");

            var sql = $@"update barfood set FtNo='{context.FtNo}',FdCName='{context.FdCName}',SumTotal={context.SumTotal},
                        ShopId={context.ShopId},Number='{context.Number}' where FdNo='{context.FdNo}';";

            var editCount = WineRepository.EditDataBySql(sql);
            if (editCount <= 0)
                throw new ExMessage("编辑商品信息失败!");

            return new EditFdDataModel();
        }

        /// <summary>
        /// 删除商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public DeleteFdDataModel DeleteFdData(DeleteFdDataContext context)
        {
            var sqlCount = $@"select count(1) Count from barfood where fdno='{context.FdNo}' and ShopId={context.ShopId}";
            var count = WineRepository.GetSelectDataCount(sqlCount);
            if (count.Count <= 0)
                throw new ExMessage("找不到商品信息!");

            var sql = $@"delete from barfood where fdno='{context.FdNo}' and ShopId={context.ShopId}";
            var deleteCount = WineRepository.EditDataBySql(sql);
            if (deleteCount <= 0)
                throw new ExMessage("删除商品信息失败!");

            return new DeleteFdDataModel();
        }

        /// <summary>
        /// 批量新增商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public EditFtDataModel InsertFtData(List<EditFtDataContext> context)
        {
            //查询有没有已存在的分类编号
            var ftnos = context.Select(i => $@"'{i.FtNo}'").ToArray();
            var sqlCount = $@"select count(1) Count from barfdtype where ftno in ({string.Join(",", ftnos)})";
            var count = WineRepository.GetSelectDataCount(sqlCount);
            if (ftnos.Length != count.Count && count.Count > 0)
                throw new ExMessage("有重复的商品分类编号!");
            if (ftnos.Length == count.Count)
                throw new ExMessage("商品分类编号全部重复!");

            var sql = string.Empty;

            foreach (var item in context)
            {
                sql += $@"insert into barfdtype 
                         (FtNo,FtCName,ShopId) 
                         values('{item.FtNo}','{item.FtCName}',{item.ShopId});";
            }

            var editCount = WineRepository.EditDataBySql(sql);
            if (editCount <= 0)
                throw new ExMessage("新增商品分类信息失败!");

            return new EditFtDataModel();
        }

        /// <summary>
        /// 编辑商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public EditFtDataModel EditFtData(EditFtDataContext context)
        {
            //查询有没有已存在的分类编号
            var sqlCount = $@"select count(1) Count from barfdtype where ftno = '{context.FtNo}'";
            var count = WineRepository.GetSelectDataCount(sqlCount);
            if (count.Count <= 0)
                throw new ExMessage("找不到商品分类信息!");

            var sql = $@"update barfdtype set FtCName='{context.FtCName}',ShopId={context.ShopId} where FtNo='{context.FtNo}';";
            var editCount = WineRepository.EditDataBySql(sql);
            if (editCount <= 0)
                throw new ExMessage("编辑商品分类信息失败!");

            return new EditFtDataModel();
        }

        /// <summary>
        /// 删除商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public DeleteFtDataModel DeleteFtData(DeleteFtDataContext context)
        {
            var sqlCount = $@"select count(1) Count from barfdtype where ftno='{context.FtNo}' and shopid={context.ShopId}";
            var count = WineRepository.GetSelectDataCount(sqlCount);
            if (count.Count <= 0)
                throw new ExMessage("找不到商品分类信息!");

            var sql = $@"delete from barfdtype where ftno='{context.FtNo}' and shopid={context.ShopId}";
            var deleteCount = WineRepository.EditDataBySql(sql);
            if (deleteCount <= 0)
                throw new ExMessage("删除商品分类信息失败!");

            return new DeleteFtDataModel();
        }

        /// <summary>
        /// 编辑单位信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public EditUnitDataModel EditUnitData(EditUnitDataContext context)
        {
            var sql = string.Empty;
            //修改
            if (context.Id > 0)
            {
                //判断有没有这条信息
                var sqlCount = $@"select count(1) Count from barunit where Id={context.Id}";
                var count = WineRepository.GetSelectDataCount(sqlCount);
                if (count.Count <= 0)
                    throw new ExMessage("找不到单位信息!");
                sql = $@"update barunit set Unit='{context.Unit}' where Id={context.Id};";
            }
            else
                sql = $@"insert into barunit (Unit) values('{context.Unit}');";

            var editCount = WineRepository.EditDataBySql(sql);
            if (editCount <= 0)
                throw new ExMessage("编辑单位信息失败!");

            return new EditUnitDataModel();
        }

        /// <summary>
        /// 删除单位信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public DeleteUnitModel DeleteUnit(DeleteUnitContext context)
        {
            var sqlCount = $@"select count(1) Count from barunit where Id={context.Id};";
            var count = WineRepository.GetSelectDataCount(sqlCount);
            if (count.Count <= 0)
                throw new ExMessage("找不到单位信息!");

            var sql = $@"delete from barunit where Id={context.Id};";
            var editCount = WineRepository.EditDataBySql(sql);
            if (editCount <= 0)
                throw new ExMessage("删除单位信息失败!");

            return new DeleteUnitModel();
        }
    }
}
