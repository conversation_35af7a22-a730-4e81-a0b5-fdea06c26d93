﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    public class EntityConversion
    {
        public static T Map<T>(object source)
        {
            var targetType = typeof(T);
            var sourceType = source.GetType();

            if (sourceType.IsClass && sourceType != typeof(string))
            {
                T destination = Activator.CreateInstance<T>();
                var targetProperties = targetType.GetProperties();
                foreach (var targetProperty in targetProperties)
                {
                    var sourceProperty = sourceType.GetProperty(targetProperty.Name, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (sourceProperty != null && sourceProperty.CanRead && targetProperty.CanWrite)
                    {
                        var sourceValue = sourceProperty.GetValue(source, null);
                        if (sourceValue != null && targetProperty.PropertyType.IsAssignableFrom(sourceProperty.PropertyType))
                        {
                            var deepCopyValue = GetValue(sourceValue);
                            targetProperty.SetValue(destination, deepCopyValue, null);
                        }
                        //判断是否是一个class类型，并且不是一个基元类型，并且不是string类型
                        else if (targetProperty.PropertyType.IsClass && !targetProperty.PropertyType.IsPrimitive && targetProperty.PropertyType != typeof(string))
                        {
                            var nestedDestination = targetProperty.GetValue(destination, null);
                            if (nestedDestination == null)
                            {
                                nestedDestination = Activator.CreateInstance(targetProperty.PropertyType);
                                targetProperty.SetValue(destination, nestedDestination, null);
                            }

                            Map(nestedDestination, sourceValue);
                        }
                    }
                }

                return destination;
            }
            else
            {
                return (T)source;
            }
        }

        private static void Map(object destination, object source)
        {
            var destinationType = destination.GetType();
            var sourceType = source.GetType();

            var destinationProperties = destinationType.GetProperties();
            foreach (var destinationProperty in destinationProperties)
            {
                var sourceProperty = sourceType.GetProperty(destinationProperty.Name, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (sourceProperty != null && sourceProperty.CanRead && destinationProperty.CanWrite)
                {
                    var sourceValue = sourceProperty.GetValue(source, null);
                    if (sourceValue != null)
                    {
                        if (destinationProperty.PropertyType.IsAssignableFrom(sourceProperty.PropertyType))
                        {
                            destinationProperty.SetValue(destination, sourceValue, null);
                        }
                        else if (destinationProperty.PropertyType.IsClass && !destinationProperty.PropertyType.IsPrimitive)
                        {
                            var nestedDestination = destinationProperty.GetValue(destination, null);
                            if (nestedDestination == null)
                            {
                                nestedDestination = Activator.CreateInstance(destinationProperty.PropertyType);
                                destinationProperty.SetValue(destination, nestedDestination, null);
                            }

                            Map(nestedDestination, sourceValue);
                        }
                    }
                }
            }
        }

        private static object GetValue(object sourceValue)
        {
            Type type = sourceValue.GetType();
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>))
            {
                Type elementType = type.GetGenericArguments()[0];
                MethodInfo method = typeof(EntityConversion).GetMethod("DeepCopyList", BindingFlags.Static | BindingFlags.NonPublic);
                MethodInfo genericMethod = method.MakeGenericMethod(elementType);
                return genericMethod.Invoke(null, new object[] { sourceValue });
            }

            return sourceValue;
        }

        private static List<T> DeepCopyList<T>(List<T> source)
        {
            List<T> newList = new List<T>();
            foreach (T item in source)
            {
                var copiedItem = Map<T>(item);
                newList.Add(copiedItem);
            }
            return newList;
        }
    }
}
