﻿using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage.SmsPushMode
{
    public abstract class SmsPushModeBase
    {
        protected AppSession app = new AppSession();

        public abstract int SendMessage(GetWineStockData context);
    }
}
