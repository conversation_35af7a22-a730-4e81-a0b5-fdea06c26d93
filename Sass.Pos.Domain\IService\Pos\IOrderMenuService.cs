﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    /// <summary>
    /// 下单页面菜单接口
    /// </summary>
    public interface IOrderMenuService
    {
        /// <summary>
        /// 获取下单页面菜单数据
        /// </summary>
        [OperationContract]
        ResponseContext<List<GetOrderMenuData>> GetOrderMenuData(GetOrderMenuDataContext context);

        [OperationContract]
        ResponseContext<List<GetOrderMenuData>> GetOrderMenuDataByStore(GetOrderMenuDataContext context);

        /// <summary>
        /// 获取下单页面菜单商品明细数据
        /// </summary>
        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetOrderMenuDataDetail(GetOrderMenuDataDetailContext context);

        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetOrderMenuDataDetailByStore(GetOrderMenuDataDetailContext context);

        /// <summary>
        /// 根据商品名获取下单页面商品数据
        /// </summary>
        [OperationContract]
        ResponseContext<List<FdData>> GetDataByFdName(GetDataByFdNameContext context);

        [OperationContract]
        ResponseContext<List<FdData>> GetDataByFdNameByStore(GetDataByFdNameContext context);

        /// <summary>
        /// 结账
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ReturnBool> OrderMenuSub(OrderMenuSubContext context);

        [OperationContract]
        ResponseContext<List<GetFdUserAllModel>> GetFdUserAllByStore(GetFdUserAllContext context);

        [OperationContract]
        ResponseContext<List<GetFdUserAllModel>> GetFdUserAll(GetFdUserAllContext context);

        [OperationContract]
        ResponseContext<ReturnBool> OrderMenuSubByStore(OrderMenuSubContext context);

        /// <summary>
        /// 根据商品编号获取附加
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<List<AddItemContext>> GetAddItemData(GetAddItemContext context);

        [OperationContract]
        ResponseContext<List<AddItemContext>> GetAddItemDataByStore(GetAddItemContext context);

        /// <summary>
        /// 获取下单页面菜单数据
        /// </summary>
        [OperationContract]
        ResponseContext<GetOrderMenuDataModel> GetOrderMenuDataV1(GetOrderMenuDataContext context);

        [OperationContract]
        ResponseContext<List<GetOrderMenuData>> GetOrderMenuDataByStoreV1(GetOrderMenuDataContext context);

        /// <summary>
        /// 获取下单页面菜单商品明细数据
        /// </summary>
        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetOrderMenuDataDetailV1(GetOrderMenuDataDetailContext context);

        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetOrderMenuDataDetailByStoreV1(GetOrderMenuDataDetailContext context);

        [OperationContract]
        ResponseContext<ReturnBool> OrderMenuSubByStoreV1(OrderMenuSubContext context);

        [OperationContract]
        ResponseContext<ReturnBool> OrderMenuSubV1(OrderMenuSubContext context);

        [OperationContract]
        ResponseContext<GetOrderMenuDataModel> GetOrderMenuDataV2(GetOrderMenuDataContext context);

        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetEmpSingingPackageByStore(GetEmpSingingPackageContext context);

        [OperationContract]
        ResponseContext<List<FdDataDetail>> GetEmpSingingPackage(GetEmpSingingPackageContext context);

        [OperationContract]
        ResponseContext<ReturnBool> ReplacePackageByStore(OrderMenuSubContext context);
    }
}
