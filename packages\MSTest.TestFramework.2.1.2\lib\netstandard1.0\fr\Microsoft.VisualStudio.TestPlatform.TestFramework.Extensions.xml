<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute">
            <summary>
            Permet de spécifier l'élément de déploiement (fichier ou répertoire) pour un déploiement par test.
            Peut être spécifié sur une classe de test ou une méthode de test.
            Peut avoir plusieurs instances de l'attribut pour spécifier plusieurs éléments.
            Le chemin de l'élément peut être absolu ou relatif. S'il est relatif, il l'est par rapport à RunConfig.RelativePathRoot.
            </summary>
            <example>
            [DeploymentItem("file1.xml")]
            [DeploymentItem("file2.xml", "DataFiles")]
            [DeploymentItem("bin\Debug")]
            </example>
            <remarks>
            DeploymentItemAttribute is currently not supported in .Net Core. This is just a placehodler for support in the future.
            </remarks>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>.
            </summary>
            <param name="path">Fichier ou répertoire à déployer. Le chemin est relatif au répertoire de sortie de build. L'élément est copié dans le même répertoire que les assemblys de tests déployés.</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.#ctor(System.String,System.String)">
            <summary>
            Initialise une nouvelle instance de la classe <see cref="T:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute"/>
            </summary>
            <param name="path">Chemin relatif ou absolu du fichier ou du répertoire à déployer. Le chemin est relatif au répertoire de sortie de build. L'élément est copié dans le même répertoire que les assemblys de tests déployés.</param>
            <param name="outputDirectory">Chemin du répertoire dans lequel les éléments doivent être copiés. Il peut être absolu ou relatif au répertoire de déploiement. Tous les fichiers et répertoires identifiés par <paramref name="path"/> vont être copiés dans ce répertoire.</param>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.Path">
            <summary>
            Obtient le chemin du fichier ou dossier source à copier.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.DeploymentItemAttribute.OutputDirectory">
            <summary>
            Obtient le chemin du répertoire dans lequel l'élément est copié.
            </summary>
        </member>
        <member name="T:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext">
            <summary>
            Classe TestContext. Cette classe doit être complètement abstraite, et ne doit contenir aucun
            membre. L'adaptateur va implémenter les membres. Les utilisateurs du framework ne doivent
            y accéder que via une interface bien définie.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.Properties">
            <summary>
            Obtient les propriétés de test d'un test.
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.FullyQualifiedTestClassName">
            <summary>
            Obtient le nom complet de la classe contenant la méthode de test en cours d'exécution
            </summary>
            <remarks>
            This property can be useful in attributes derived from ExpectedExceptionBaseAttribute.
            Those attributes have access to the test context, and provide messages that are included
            in the test results. Users can benefit from messages that include the fully-qualified
            class name in addition to the name of the test method currently being executed.
            </remarks>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.TestName">
            <summary>
            Obtient le nom de la méthode de test en cours d'exécution
            </summary>
        </member>
        <member name="P:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.CurrentTestOutcome">
            <summary>
            Obtient le résultat de test actuel.
            </summary>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String)">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="message">formatted message string</param>
        </member>
        <member name="M:Microsoft.VisualStudio.TestTools.UnitTesting.TestContext.WriteLine(System.String,System.Object[])">
            <summary>
            Used to write trace messages while the test is running
            </summary>
            <param name="format">format string</param>
            <param name="args">the arguments</param>
        </member>
    </members>
</doc>
