﻿using Saas.Pos.Common.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage.SmsPushMode
{
    public class SmsPushModeFactory
    {
        public static SmsPushModeBase SendMessage(int ReNew)
        {
            string className = null;
            if (ReNew == 0)
                className = "FristSave";
            else if (ReNew == 1)
                className = "FristRenew";
            else if (ReNew == 2)
                className = "FristSave";

            if (string.IsNullOrEmpty(className))
                throw new ExMessage("未对接该模式！");

            return AssemblyHelper.CreateInstance<SmsPushModeBase>("Saas.Pos.Drive", "Saas.Pos.Drive.Bar.WineStockManage.SmsPushMode", "SmsPush" + className);
        }
    }
}
