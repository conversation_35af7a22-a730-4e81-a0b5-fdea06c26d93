﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IGiftRoleRepository : IRepositoryBase<GiftRole>
    {
        List<GetGiftRoleDataModel> GetGiftRoleData(GetGiftRoleDataContext context);

        List<GetSceneRoleBindDataModel> GetSceneRoleBindData(GetSceneRoleBindDataContext context);

        List<GetSceneRoleBindDataModel> GetSceneRoleBindByIdData(GetSceneRoleBindByIdDataContext context);
    }
}
