﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IFdUserRepository : IRepositoryBase<FdUser>
    {
        string GetUserName(string userId);

        List<GetFdUserAllModel> GetFdUserAll(GetFdUserAllContext context);
    }
}
