﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IPos_Storage_FoodRepository : IRepositoryBase<Pos_Storage_Food>
    {
        List<GetPosFdDataModel> GetPosFdData(GetPosFdDataContext context);
    }
}
