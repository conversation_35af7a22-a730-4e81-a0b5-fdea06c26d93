﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFood.GiftOrderManage
{
    /// <summary>
    /// 赠送下单账户管理
    /// </summary>
    public interface IGiftOrderAccount
    {
        ResponseContext<RespPaginationModel<GetGiftAccountDataModel>> GetAllGiftAccountData(GetAllGiftAccountDataContext context);

        ResponseContext<GetGiftAccountDataModel> GetGiftAccountData(GetGiftAccountDataContext context);

        ResponseContext<EditGiftAccountDataModel> EditGiftAccountData(EditGiftAccountDataContext context);

        ResponseContext<RespPaginationModel<GetGiftAccOpenterRecordDataModel>> GetGiftAccOpenterRecordData(GetGiftAccOpenterRecordDataContext context);

        ResponseContext<List<GetAccountSelectDataModel>> GetAccountSelectData(GetAccountSelectDataContext context);

        /// <summary>
        /// 账户金额数量变动
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<AccountChangeModel> AccountChange(SetAccountChangeContext context);

        ResponseContext<RespPaginationModel<GetGiftAccountDataModel>> GetAllGiftAccountDataByStore(GetAllGiftAccountDataContext context);

        ResponseContext<RespPaginationModel<GetAccountOperationRecordModel>> GetAccountOperationRecord(GetAccountOperationRecordContext context);

        ResponseContext<RespPaginationModel<GetAccountOperationRecordModel>> GetAccountOperationRecordByStore(GetAccountOperationRecordContext context);

        ResponseContext<GetGiftAccountDataModel> GetGiftAccountDataByStore(GetGiftAccountDataContext context);

        ResponseContext<EditGiftAccountDataModel> EditGiftAccountDataByStore(EditGiftAccountDataContext context);

        ResponseContext<RespPaginationModel<GetGiftAccOpenterRecordDataModel>> GetGiftAccOpenterRecordDataByStore(GetGiftAccOpenterRecordDataContext context);

        ResponseContext<List<GetAccountSelectDataModel>> GetAccountSelectDataByStore(GetAccountSelectDataContext context);
    }
}
