﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.StoreManage
{
    /// <summary>
    /// 门店管理基础服务
    /// </summary>
    public interface IStoreManageBasicService
    {
        /// <summary>
        /// 查询基础服务功能
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<BasicService>> GetBasicService(GetStoreManageContext context);

        /// <summary>
        /// 删除基础服务功能(软删除)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> DeleteBasicService(DeleteBasicServiceContest context);

        /// <summary>
        /// 新增基础服务数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> InsertBasicService(InsertBasicServiceContext context);

        /// <summary>
        /// 修改基础服务数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnInt> EditBasicService(EditBasicServiceContext context);
    }
}
