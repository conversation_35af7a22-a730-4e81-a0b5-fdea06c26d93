﻿using Saas.Pos.Common;
using Saas.Pos.Common.Rms;
using Saas.Pos.Drive.Common.Extend;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    public abstract class BookingBaseProxy : BookDateBaseProxy
    {
        AppSession app = new AppSession();

        /// <summary>
        /// 获取条件下的门店剩余房型数量
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public virtual List<GetBookRoomInfoModel> GetBooking(GetBookingContext context)
        {
            var day = (int)Math.Ceiling((context.EndTime - context.StartTime).TotalDays);
            if (day <= 0)
                throw new ExMessage("时间间隔有误，最少需要一天的数据！");

            var query = AppSingle.App.Storage.BookingDat.RoomDataStorage.AsEnumerable();
            if (context.ShopId > 0)
                query = query.Where(w => w.ShopId == context.ShopId);
            if (!string.IsNullOrEmpty(context.TimeNo))
                query = query.Where(w => w.TimeNo == context.TimeNo);
            if (!string.IsNullOrEmpty(context.RtNo))
                query = query.Where(w => w.RtNo == context.RtNo);

            //计算动态数据
            var resultData = GetWorkOutList(query.ToList(), day, context.StartTime);

            //获取数据库中用户预约数据，以保证是最准确的数据
            var bookCacheList = app.Rms.bookcacheinfo.GetCaCheData(context.StartTime, context.EndTime).Where(w => w.ShopId == context.ShopId).ToList();
            resultData = CalculateRoomNumber(resultData, bookCacheList);

            return resultData;
        }

        /// <summary>
        /// 根据传入数据进行计算，返回已经处理过超订的房型数据
        /// </summary>
        /// <param name="roomData">门店基础数据</param>
        /// <param name="workNot">限制条件模型</param>
        /// <param name="count">返回当前时间至Count天的数据</param>
        /// <returns></returns>
        public static List<GetBookRoomInfoModel> GetWorkOutList(List<BookingRoomDataModel> roomData, int count, DateTime startTime)
        {
            var resultData = new List<GetBookRoomInfoModel>();
            for (int i = 0; i < count; i++)
            {
                var currentDate = startTime.AddDays(i);
                var data = new GetBookRoomInfoModel();
                data.Date = currentDate.ToString("yyyyMMdd");
                data.Book = new List<StoreBooking>();
                data.Book.AddRange(roomData.GroupBy(x => x.ShopId).ToList().Select(item =>
                {
                    var storeBooking = new StoreBooking();
                    storeBooking.SId = item.Key;
                    storeBooking.RtInfo = new List<RtBookingInfo>();

                    //合并门店数据
                    item.ToList().OrderBy(x => x.NumberMax).ToList().ForEach(w =>
                    {
                        var tuple = TimeInfoHelper.TimeConvert(currentDate, w.TimeName);
                        var shopDis = Shop_Applet_Proxy.GetBookShop(item.Key, tuple.Item1, tuple.Item2);
                        //var timeNoDis = TimeInfo_Applet_Proxy.GetWorkNotShopTime(item.Key, w.TimeNo, tuple.Item1, tuple.Item2);
                        var rtNoDis = RtInfo_Applet_Proxy.GetBookRtInfo(tuple.Item1, tuple.Item2, item.Key, w.RtNo);

                        var rtData = new RtBookingInfo()
                        {
                            RtNo = w.RtNo,
                            TNo = w.TimeNo,
                        };
                        //存在禁用也要返回数据，但是要把数据标识为不可用
                        if (!shopDis || !rtNoDis)
                        {
                            rtData.RmQty = 0;
                            rtData.cb = true;
                        }
                        else
                        {
                            rtData.RmQty = w.TotalCount + RtInfo_Applet_Proxy.GetBookOutNumber(tuple.Item1, tuple.Item2, item.Key, w.RtNo);
                            rtData.cb = false;
                        }

                        storeBooking.RtInfo.Add(rtData);
                    });

                    return storeBooking;
                }));
                resultData.Add(data);
            }

            return resultData;
        }

        /// <summary>
        /// 加工动态数据返回经过减除已预订用户的剩余房型数量
        /// </summary>
        /// <param name="roomData"></param>
        /// <param name="cacheData"></param>
        /// <returns></returns>
        public static List<GetBookRoomInfoModel> CalculateRoomNumber(List<GetBookRoomInfoModel> roomData, List<BookCaCheModel> cacheData)
        {
            roomData.ForEach(date =>
            {
                date.Book.ForEach(book =>
                {
                    //处理源于客户预订的房间数据
                    var shopBookCacheList = cacheData.Where(w => w.ShopId == book.SId && w.ComeDate == date.Date).ToList();

                    shopBookCacheList.ForEach(cache =>
                    {
                        //如果跨时段了，就按照直落来处理
                        if (cache.BeginTimeNo != cache.EndTimeNo)
                        {
                            var beginTimeInfo = AppSingle.App.Storage.DbDat.TimeList.GetFirst(cache.BeginTimeNo);
                            //获取当前预约用户跨越的所有时段，方便后续的加减
                            var crossTimeNos = beginTimeInfo.GetCrossTimeNos(AppSingle.App.Storage.DbDat.ShopTimeList,
                                                                     AppSingle.App.Storage.DbDat.TimeList,
                                                                     cache.ShopId, cache.EndTimeNo, DateTime.ParseExact(date.Date, "yyyyMMdd", null));

                            var rtInfo = book.RtInfo.Where(w => w.RtNo == cache.RtNo && crossTimeNos.Contains(w.TNo) && w.cb).ToList();
                            book.RtInfo.RemoveAll(w => w.RtNo == cache.RtNo && crossTimeNos.Contains(w.TNo) && w.cb);

                            //到这一步，如果出现为false的数据，肯定是已经门店或者时段或者房型被禁用了的,所以只取可用的数据
                            rtInfo.ForEach(w =>
                            {
                                w.RmQty = (w.RmQty - 1) <= 0 ? 0 : w.RmQty - 1;
                                w.cb = w.RmQty <= 0 ? true : false;
                            });

                            book.RtInfo.AddRange(rtInfo);
                        }
                        else
                        {
                            var rtInfo = book.RtInfo.Where(w => w.RtNo == cache.RtNo && w.TNo == cache.BeginTimeNo && !w.cb).ToList();
                            book.RtInfo.RemoveAll(w => w.RtNo == cache.RtNo && w.TNo == cache.BeginTimeNo && !w.cb);

                            rtInfo.ForEach(w =>
                            {
                                w.RmQty = (w.RmQty - 1) <= 0 ? 0 : w.RmQty - 1;
                                w.cb = w.RmQty <= 0 ? true : false;
                            });

                            book.RtInfo.AddRange(rtInfo);
                        }
                    });
                });
            });

            return roomData;
        }
    }
}
