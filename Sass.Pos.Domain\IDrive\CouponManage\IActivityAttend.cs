﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.CouponManage
{
    public interface IActivityAttend
    {
        /// <summary>
        /// 添加活动赠送
        /// </summary>
        /// <returns></returns>
        bool CreateActivityAttend(List<CreateActivityAttendContext> context);

        /// <summary>
        /// 撤销活动赠送
        /// </summary>
        /// <returns></returns>
        Activity_AttendRecord RevokeActivityAttend(RevokeActivityAttendContext context);

        /// <summary>
        /// 校验是否已经参与过活动
        /// </summary>
        /// <param name="context"></param>
        /// <returns>返回已经参与过的记录</returns>
        List<CheckAlreadyAttendModel> CheckAlreadyAttend(CheckAlreadyAttendContext context);

        ResponseContext<RespPaginationModel<Activity_AttendRecord>> GetActivityAttendList(GetActivityAttendListContext context);

        ResponseContext<RespPaginationModel<GetActivityDataModel>> GetActivityData(GetActivityDataContext context);
    }
}
