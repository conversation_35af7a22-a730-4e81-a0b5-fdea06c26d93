<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=4.4.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	</configSections>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
			<parameters>
				<parameter value="v11.0" />
			</parameters>
		</defaultConnectionFactory>
	</entityFramework>
	<connectionStrings>
		<add name="MIMSEntities" connectionString="metadata=.\MIMS\MIMS_DB.csdl|.\MIMS\MIMS_DB.ssdl|.\MIMS\MIMS_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=MIMS;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
		<add name="rms2019Entities" connectionString="metadata=res://*/Rms.RMS_DB.csdl|res://*/Rms.RMS_DB.ssdl|res://*/Rms.RMS_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=yy.tang-hui.com.cn;initial catalog=rms2019;user id=sa;password=Musicbox@***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
		<add name="dbfoodEntities" connectionString="metadata=res://*/DbFood.DbFood_DB.csdl|res://*/DbFood.DbFood_DB.ssdl|res://*/DbFood.DbFood_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=dbfood;user id=sa;password=***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
		<add name="GrouponBase_20200516Entities" connectionString="metadata=res://*/GrouponBase.GrouponBase_DB.csdl|res://*/GrouponBase.GrouponBase_DB.ssdl|res://*/GrouponBase.GrouponBase_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***********;initial catalog=GrouponBase;user id=sa;password=Musicbox***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
		<add name="Saas_PosEntities" connectionString="metadata=res://*/SaasPos.SaasPos_DB.csdl|res://*/SaasPos.SaasPos_DB.ssdl|res://*/SaasPos.SaasPos_DB.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=************;initial catalog=saas.pos;user id=sa;password=***;encrypt=False;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
		<add name="SongBaseEntities" connectionString="metadata=res://*/SongBase.SongBase.csdl|res://*/SongBase.SongBase.ssdl|res://*/SongBase.SongBase.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***********;initial catalog=SongBase;user id=sa;password=Musicbox***;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
	</connectionStrings>
</configuration>