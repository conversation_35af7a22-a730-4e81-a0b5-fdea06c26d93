﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Request
{
    public class MeituanRequestBase
    {
        public MeituanRequestBase()
        {
            format = "json";
            v = "1";
            sign_method = "MD5";
        }

        /// <summary>
        /// 点评到综开放平台分配给应用的AppKey
        /// </summary>
        public string app_key { get; set; }
        /// <summary>
        /// 当前时间时间戳
        /// </summary>
        public string timestamp { get; set; }
        /// <summary>
        /// 商家授权成功后，点评到综开放平台颁发给应用的授权信息。当对接模块的标签上注明：“需要授权”，则此参数必传；“不需要授权”，则此参数不需要传
        /// </summary>
        public string session { get; set; }
        /// <summary>
        /// 响应格式。默认为json格式。
        /// </summary>
        public string format { get; set; }
        /// <summary>
        /// API协议版本，默认值：1，此后版本升级，会递增。
        /// </summary>
        public string v { get; set; }
        /// <summary>
        /// 签名的摘要算法，默认值为：MD5。
        /// </summary>
        public string sign_method { get; set; }
        /// <summary>
        /// API输入参数签名结果。
        /// </summary>
        public string sign { get; set; }
    }
}
