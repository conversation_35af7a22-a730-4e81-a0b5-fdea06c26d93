﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Rms.NumberPool
{
    /// <summary>
    /// 顺序生成号码(废弃)
    /// </summary>
    public class NumberPoolOrderService : NumberPoolServiceBase
    {
        int Index = 0;
        SystemConfigModel config;
        /// <summary>
        /// 当日已生成的号码
        /// </summary>
        private static List<KeyValuePair<string, DateTime>> PublishedNumber = new List<KeyValuePair<string, DateTime>>();

        public NumberPoolOrderService(List<KeyValuePair<string, DateTime>> _PublishedNumber, SystemConfigModel _config, DateTime bookDate) : base(bookDate)
        {
            config = _config;
            PublishedNumber = _PublishedNumber;

            var publishedNumber = PublishedNumber.Where(w => w.Value.Date == BookDate.Date).Select(w => w.Key).ToList();
            if (publishedNumber.Count > 0)
            {
                string numberStr = publishedNumber.OrderByDescending(w => w).FirstOrDefault();
                if (!string.IsNullOrEmpty(_config.Initial))
                    numberStr = numberStr.Replace(_config.Initial, "");

                Index = int.Parse(numberStr);
            }
        }

        public override string GenerateNumber()
        {
            //找出预约日所有的预约记录
            var publishedNumber = PublishedNumber.Where(w => w.Value.Date == BookDate.Date).Select(w => w.Key).ToList();
            Index++;
            string returnStr = string.Empty;
            //获取所有不允许出现的数字
            var existNumbers = new List<int>();
            if (!string.IsNullOrEmpty(config.existNumbers))
                existNumbers = config.existNumbers.Split(',').Select(x => Convert.ToInt32(x)).ToList();

            //一直循环，直到满足条件再结束循环
            while (true)
            {
                //如果不符合条件就重新排序Index
                if (!Inspect(existNumbers, Index))
                    ProcessNumber(existNumbers);

                returnStr = Index.ToString().PadLeft(config.Length, '0');
                if (!string.IsNullOrEmpty(config.Initial))
                    returnStr = config.Initial + returnStr;

                if (publishedNumber.FirstOrDefault(w => w == returnStr) != null)
                {
                    returnStr = string.Empty;
                    Index++;
                }
                else
                    break;
            }

            return returnStr;
        }

        protected override bool Inspect(List<int> existNumbers, int number)
        {
            if (existNumbers == null || existNumbers.Count <= 0)
                return true;

            foreach (var item in existNumbers)
            {
                if (number.ToString().Contains(item.ToString()))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 处理编号，保证编号符合要求（进入此方法说明一定存在了不该存在的数据）
        /// </summary>
        private void ProcessNumber(List<int> existNumbers)
        {
            var newNumberStr = string.Empty;
            var numberStr = Index.ToString();

            for (int i = 0; i < numberStr.Length; i++)
            {
                int charInt = int.Parse(numberStr[i].ToString());
                if (existNumbers.Any(w => w == charInt))
                {
                    //检测是否还存在不允许出现的数字
                    var next = true;
                    while (next)
                    {
                        charInt++;
                        next = existNumbers.Where(w => w == charInt).Count() > 0;
                    }
                }

                newNumberStr += charInt.ToString();
            }

            Index = int.Parse(newNumberStr);
        }
    }
}
