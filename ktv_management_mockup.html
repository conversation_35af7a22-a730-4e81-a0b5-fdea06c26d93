<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KTV 会员管理系统 - 设计稿</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>
    <style type="text/tailwindcss">
        @layer utilities {
            .modal-backdrop {
                @apply fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center transition-opacity;
            }
            .modal-content {
                @apply bg-slate-800 rounded-xl shadow-2xl p-8 w-full max-w-md transform transition-transform scale-95;
            }
            .btn {
                @apply px-4 py-2 rounded-md font-semibold text-sm transition-all duration-200 ease-in-out;
            }
            .btn-primary {
                @apply bg-sky-500 hover:bg-sky-600 text-white;
            }
            .btn-secondary {
                @apply bg-slate-600 hover:bg-slate-700 text-white;
            }
            .btn-danger {
                @apply bg-red-500 hover:bg-red-600 text-white;
            }
            .btn-success {
                @apply bg-emerald-500 hover:bg-emerald-600 text-white;
            }
        }
    </style>
</head>
<body class="bg-slate-900 text-gray-200 font-sans antialiased">

    <div id="app" class="container mx-auto p-4 sm:p-6 lg:p-8">
        
        <header class="mb-8">
            <h1 class="text-4xl font-bold text-white tracking-tight">KTV 会员管理系统</h1>
            <p class="text-slate-400 mt-2">房间状态实时概览与会员操作</p>
        </header>

        <main>
            <div id="room-list" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                <!-- Room cards will be dynamically inserted here -->
            </div>
        </main>

    </div>

    <!-- Assign Member Modal -->
    <div id="assign-modal" class="modal-backdrop hidden opacity-0" onclick="closeAssignModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <h2 class="text-2xl font-bold mb-6 text-white">中途当房</h2>
            <form>
                <input type="hidden" id="assign-room-id">
                <div class="mb-4">
                    <label for="member-id" class="block text-slate-300 mb-2">会员手机号 / ID</label>
                    <input type="text" id="member-id" class="w-full bg-slate-700 border border-slate-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-sky-500" placeholder="请输入会员信息">
                </div>
                <div class="flex justify-end gap-4 mt-8">
                    <button type="button" class="btn btn-secondary" onclick="closeAssignModal()">取消</button>
                    <button type="submit" class="btn btn-primary">确认绑定</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Transfer Room Modal -->
    <div id="transfer-modal" class="modal-backdrop hidden opacity-0" onclick="closeTransferModal()">
        <div class="modal-content" onclick="event.stopPropagation()">
            <h2 class="text-2xl font-bold mb-6 text-white">转房并当房</h2>
            <form>
                <input type="hidden" id="transfer-room-id">
                <div class="mb-4">
                    <p class="text-slate-300">当前房间: <span id="current-room-name" class="font-bold text-white"></span></p>
                    <p class="text-slate-300">当前会员: <span id="current-member-name" class="font-bold text-white"></span></p>
                </div>
                <div class="mb-4">
                    <label for="target-room" class="block text-slate-300 mb-2">选择目标空闲房间</label>
                    <select id="target-room" class="w-full bg-slate-700 border border-slate-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-sky-500">
                        <!-- Available rooms will be populated here -->
                    </select>
                </div>
                <div class="flex justify-end gap-4 mt-8">
                    <button type="button" class="btn btn-secondary" onclick="closeTransferModal()">取消</button>
                    <button type="submit" class="btn btn-primary">确认转移</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const roomData = [
            { id: 'V01', status: 'occupied', member: '张三 (138****1234)', duration: '2h 15m' },
            { id: 'V02', status: 'available' },
            { id: 'V03', status: 'available' },
            { id: 'V05', status: 'occupied', member: '李四 (VIP888)', duration: '0h 45m' },
            { id: 'K01', status: 'cleaning' },
            { id: 'K02', status: 'available' },
            { id: 'K03', status: 'occupied', member: '王五 (159****5678)', duration: '1h 30m' },
            { id: 'P01', status: 'reserved', member: '赵六 (预订 20:00)', duration: 'N/A' },
            { id: 'P02', status: 'available' },
            { id: 'V06', status: 'available' },
        ];

        const statusMap = {
            occupied: { text: '占用中', color: 'bg-red-500', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.414-1.414L11 10.586V6z" clip-rule="evenodd" /></svg>` },
            available: { text: '空闲', color: 'bg-green-500', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" /></svg>` },
            cleaning: { text: '清扫中', color: 'bg-yellow-500', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" /></svg>` },
            reserved: { text: '已预订', color: 'bg-purple-500', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" /><path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" /></svg>` }
        };

        function renderRooms() {
            const roomListEl = document.getElementById('room-list');
            roomListEl.innerHTML = roomData.map(room => {
                const statusInfo = statusMap[room.status] || { text: '未知', color: 'bg-gray-500', icon: '' };
                
                let actionsHtml = '';
                if (room.status === 'occupied') {
                    actionsHtml = `
                        <div class="border-t border-slate-700 mt-4 pt-4 flex flex-wrap gap-2">
                            <button class="btn btn-secondary text-xs flex-grow" onclick="openAssignModal('${room.id}')">中途当房</button>
                            <button class="btn btn-secondary text-xs flex-grow" onclick="openTransferModal('${room.id}', '${room.member}')">转房</button>
                            <button class="btn btn-danger text-xs flex-grow" onclick="unbindMember('${room.id}')">解绑</button>
                            <button class="btn btn-success text-xs flex-grow" onclick="checkout('${room.id}')">结账</button>
                        </div>
                    `;
                } else if (room.status === 'available') {
                     actionsHtml = `
                        <div class="border-t border-slate-700 mt-4 pt-4 flex flex-wrap gap-2">
                            <button class="btn btn-primary w-full" onclick="openAssignModal('${room.id}')">开房</button>
                        </div>
                    `;
                }

                return `
                    <div class="bg-slate-800 rounded-lg shadow-lg p-5 flex flex-col justify-between transition-all hover:scale-105 hover:shadow-sky-500/20">
                        <div>
                            <div class="flex justify-between items-start">
                                <h3 class="text-2xl font-bold text-white">${room.id}</h3>
                                <span class="text-xs font-semibold inline-flex items-center px-2.5 py-1 rounded-full ${statusInfo.color} text-white">
                                    ${statusInfo.icon}
                                    ${statusInfo.text}
                                </span>
                            </div>
                            <div class="mt-4 text-slate-300 text-sm min-h-[40px]">
                                ${room.member ? `<p><strong>会员:</strong> ${room.member}</p>` : ''}
                                ${room.duration && room.duration !== 'N/A' ? `<p><strong>时长:</strong> ${room.duration}</p>` : ''}
                            </div>
                        </div>
                        ${actionsHtml}
                    </div>
                `;
            }).join('');
        }

        // Modal logic
        const assignModal = document.getElementById('assign-modal');
        const transferModal = document.getElementById('transfer-modal');
        const assignForm = assignModal.querySelector('form');
        const transferForm = transferModal.querySelector('form');

        function openAssignModal(roomId) {
            document.getElementById('assign-room-id').value = roomId;
            assignModal.classList.remove('hidden');
            setTimeout(() => assignModal.classList.remove('opacity-0'), 10);
            setTimeout(() => assignModal.querySelector('.modal-content').classList.remove('scale-95'), 10);
        }

        function closeAssignModal() {
            assignModal.classList.add('opacity-0');
            assignModal.querySelector('.modal-content').classList.add('scale-95');
            setTimeout(() => {
                assignModal.classList.add('hidden');
                assignForm.reset();
            }, 300);
        }

        function openTransferModal(roomId, memberName) {
            document.getElementById('transfer-room-id').value = roomId;
            document.getElementById('current-room-name').textContent = roomId;
            document.getElementById('current-member-name').textContent = memberName;
            
            const targetRoomSelect = document.getElementById('target-room');
            targetRoomSelect.innerHTML = roomData
                .filter(r => r.status === 'available')
                .map(r => `<option value="${r.id}">${r.id}</option>`)
                .join('');

            transferModal.classList.remove('hidden');
            setTimeout(() => transferModal.classList.remove('opacity-0'), 10);
            setTimeout(() => transferModal.querySelector('.modal-content').classList.remove('scale-95'), 10);
        }

        function closeTransferModal() {
            transferModal.classList.add('opacity-0');
            transferModal.querySelector('.modal-content').classList.add('scale-95');
            setTimeout(() => {
                transferModal.classList.add('hidden');
                transferForm.reset();
            }, 300);
        }

        // Action Handlers
        function unbindMember(roomId) {
            if (!confirm(`确定要为房间 ${roomId} 解绑会员吗？这不会改变房间的占用状态。`)) return;

            const room = roomData.find(r => r.id === roomId);
            if (room) {
                alert(`房间 ${roomId} 已成功解绑会员 ${room.member}！`);
                room.member = undefined;
                renderRooms();
            }
        }

        function checkout(roomId) {
            if (!confirm(`确定要为房间 ${roomId} 办理结账吗？房间状态将变为清扫中。`)) return;

            const room = roomData.find(r => r.id === roomId);
            if (room) {
                alert(`房间 ${roomId} 已结账，状态更改为清扫中。`);
                room.status = 'cleaning';
                room.member = undefined;
                room.duration = undefined;
                renderRooms();
            }
        }

        // Form Submission Handlers
        assignForm.addEventListener('submit', (event) => {
            event.preventDefault();
            const roomId = document.getElementById('assign-room-id').value;
            const memberId = document.getElementById('member-id').value;

            if (!memberId.trim()) {
                alert('请输入会员信息！');
                return;
            }

            const room = roomData.find(r => r.id === roomId);
            if (room) {
                const isOpeningRoom = room.status === 'available';
                room.status = 'occupied';
                room.member = memberId;
                if(isOpeningRoom) {
                    room.duration = '0h 0m'; 
                }
                
                renderRooms();
                closeAssignModal();
                alert(`成功将会员 ${memberId} 绑定到房间 ${roomId}！`);
            }
        });

        transferForm.addEventListener('submit', (event) => {
            event.preventDefault();
            const sourceRoomId = document.getElementById('transfer-room-id').value;
            const targetRoomId = document.getElementById('target-room').value;

            if (!targetRoomId) {
                alert('没有可用的目标房间！');
                return;
            }

            if (!confirm(`确定将会员从 ${sourceRoomId} 转到 ${targetRoomId} 吗？`)) return;

            const sourceRoom = roomData.find(r => r.id === sourceRoomId);
            const targetRoom = roomData.find(r => r.id === targetRoomId);

            if (sourceRoom && targetRoom) {
                // Update target room
                targetRoom.status = 'occupied';
                targetRoom.member = sourceRoom.member;
                targetRoom.duration = sourceRoom.duration;

                // Update source room
                sourceRoom.status = 'cleaning'; // Or 'available'. 'cleaning' is a safe default.
                sourceRoom.member = undefined;
                sourceRoom.duration = undefined;

                renderRooms();
                closeTransferModal();
                alert(`成功将房间 ${sourceRoomId} 的会员转移到 ${targetRoomId}！`);
            }
        });

        // Initial render
        document.addEventListener('DOMContentLoaded', renderRooms);
    </script>

</body>
</html>
