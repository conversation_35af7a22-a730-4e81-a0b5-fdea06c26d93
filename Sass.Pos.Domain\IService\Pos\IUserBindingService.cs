﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IUserBindingService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftRoleDataModel>> GetGiftRoleData(GetGiftRoleDataContext context);

        [OperationContract]
        ResponseContext<List<GetRoleSelectDataModel>> GetRoleSelectData(GetRoleSelectDataContext context);

        [OperationContract]
        ResponseContext<EditGiftRoleDataModel> EditGiftRoleData(EditGiftRoleDataContext context);

        [OperationContract]
        ResponseContext<List<GetTreeRoleDataModel>> GetTreeRoleData(GetTreeRoleDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftAccountDataModel>> GetAllGiftAccountData(GetAllGiftAccountDataContext context);

        [OperationContract]
        ResponseContext<GetGiftAccountDataModel> GetGiftAccountData(GetGiftAccountDataContext context);

        [OperationContract]
        ResponseContext<EditGiftAccountDataModel> EditGiftAccountData(EditGiftAccountDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftAccOpenterRecordDataModel>> GetGiftAccOpenterRecordData(GetGiftAccOpenterRecordDataContext context);

        [OperationContract]
        ResponseContext<List<GetAccountSelectDataModel>> GetAccountSelectData(GetAccountSelectDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftSceneDataModel>> GetGiftSceneData(GetGiftSceneDataContext context);

        [OperationContract]
        ResponseContext<List<GetSceneSelectDataModel>> GetSceneSelectData(GetSceneSelectDataContext context);

        [OperationContract]
        ResponseContext<EditGiftSceneDataModel> EditGiftSceneData(EditGiftSceneDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftAccSceDataModel>> GetGiftAccSceData(GetGiftAccSceDataContext context);

        [OperationContract]
        ResponseContext<DistributionGiftAccSceDataModel> DistributionGiftAccSceData(DistributionGiftAccSceDataContext context);

        [OperationContract]
        ResponseContext<BindIngSceRoleDataModel> BindIngSceRoleData(BindIngSceRoleDataContext context);

        [OperationContract]
        ResponseContext<List<GetSceneTreeDataModel>> GetSceneTreeData(GetSceneTreeDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetSceneRoleBindDataModel>> GetSceneRoleBindData(GetSceneRoleBindDataContext context);

        [OperationContract]
        ResponseContext<List<GetSceneRoleBindDataModel>> GetSceneRoleBindByIdData(GetSceneRoleBindByIdDataContext context);

        [OperationContract]
        ResponseContext<EditAccSceDataModel> EditAccSceData(EditAccSceDataContext context);

        [OperationContract]
        ResponseContext<DeleteSceneRoleBindDataModel> DeleteSceneRoleBindData(DeleteSceneRoleBindDataContext context);


        [OperationContract]
        ResponseContext<RespPaginationModel<GetSceneRoleConfigExModel>> GetSceneRoleConfigData(GetSceneRoleConfigDataContext context);

        [OperationContract]
        ResponseContext<List<GetUserScenceData>> GetUserSceData(GetUserScenceDataContext context);

        [OperationContract]
        ResponseContext<List<GetUserScenceFdNoModel>> GetUserSceFdNoData(GetUserSceFdNoDataContext context);

        [OperationContract]
        ResponseContext<SaveSceneRoleConfigModel> SaveSceneRoleConfig(SaveSceneRoleConfigContext context);

        [OperationContract]
        ResponseContext<SaveSceneRoleConfigModel> DelSceneRoleConfig(DeleteSceneRoleConfigContext context);

        [OperationContract]
        ResponseContext<GetSceneRoleConfigModel> GetSceneRoleConfig(GetSceneRoleConfigContext context);

        [OperationContract]
        ResponseContext<GiftOrderModel> GiftOrder(GiftOrderContext context);

        [OperationContract]
        ResponseContext<GiftOrderModel> CancelOrder(CancelOrderContext context);

        [OperationContract]
        ResponseContext<string> SaveBinding(SaveUserBindingContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetUserBindingModel>> GetUserBindingList(GetUserBindingListContext context);

        [OperationContract]
        ResponseContext<string> SaveBindingByStore(SaveUserBindingContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetUserBindingModel>> GetUserBindingListByStore(GetUserBindingListContext context);

        [OperationContract]
        ResponseContext<GiftOrderModel> GiftOrderByStore(GiftOrderContext context);

        [OperationContract]
        ResponseContext<GiftOrderModel> CancelOrderByStore(CancelOrderContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftSceneDataModel>> GetGiftSceneDataByStore(GetGiftSceneDataContext context);

        [OperationContract]
        ResponseContext<List<GetSceneSelectDataModel>> GetSceneSelectDataByStore(GetSceneSelectDataContext context);

        [OperationContract]
        ResponseContext<EditGiftSceneDataModel> EditGiftSceneDataByStore(EditGiftSceneDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftAccSceDataModel>> GetGiftAccSceDataByStore(GetGiftAccSceDataContext context);

        [OperationContract]
        ResponseContext<EditAccSceDataModel> EditAccSceDataByStore(EditAccSceDataContext context);

        [OperationContract]
        ResponseContext<DeleteSceneRoleBindDataModel> DeleteSceneRoleBindDataByStore(DeleteSceneRoleBindDataContext context);

        [OperationContract]
        ResponseContext<DistributionGiftAccSceDataModel> DistributionGiftAccSceDataByStore(DistributionGiftAccSceDataContext context);

        [OperationContract]
        ResponseContext<BindIngSceRoleDataModel> BindIngSceRoleDataByStore(BindIngSceRoleDataContext context);

        [OperationContract]
        ResponseContext<List<GetSceneTreeDataModel>> GetSceneTreeDataByStore(GetSceneTreeDataContext context);

        [OperationContract]
        ResponseContext<List<GetUserScenceDataModel>> GetUserSceDataByStore(GetUserScenceDataContext context);

        [OperationContract]
        ResponseContext<List<GetUserScenceFdNoModel>> GetUserSceFdNoDataByStore(GetUserSceFdNoDataContext context);

        [OperationContract]
        ResponseContext<SaveSceneRoleConfigModel> SaveSceneRoleConfigByStore(SaveSceneRoleConfigContext context);

        [OperationContract]
        ResponseContext<SaveSceneRoleConfigModel> DelSceneRoleConfigByStore(DeleteSceneRoleConfigContext context);

        [OperationContract]
        ResponseContext<GetSceneRoleConfigModel> GetSceneRoleConfigByStore(GetSceneRoleConfigContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetSceneRoleBindDataModel>> GetSceneRoleBindDataByStore(GetSceneRoleBindDataContext context);

        [OperationContract]
        ResponseContext<List<GetSceneRoleBindDataModel>> GetSceneRoleBindByIdDataByStore(GetSceneRoleBindByIdDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetSceneRoleConfigExModel>> GetSceneRoleConfigDataByStore(GetSceneRoleConfigDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftAccountDataModel>> GetAllGiftAccountDataByStore(GetAllGiftAccountDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetAccountOperationRecordModel>> GetAccountOperationRecord(GetAccountOperationRecordContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetAccountOperationRecordModel>> GetAccountOperationRecordByStore(GetAccountOperationRecordContext context);

        [OperationContract]
        ResponseContext<GetGiftAccountDataModel> GetGiftAccountDataByStore(GetGiftAccountDataContext context);

        [OperationContract]
        ResponseContext<EditGiftAccountDataModel> EditGiftAccountDataByStore(EditGiftAccountDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftAccOpenterRecordDataModel>> GetGiftAccOpenterRecordDataByStore(GetGiftAccOpenterRecordDataContext context);

        [OperationContract]
        ResponseContext<List<GetAccountSelectDataModel>> GetAccountSelectDataByStore(GetAccountSelectDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetGiftRoleDataModel>> GetGiftRoleDataByStore(GetGiftRoleDataContext context);

        [OperationContract]
        ResponseContext<List<GetRoleSelectDataModel>> GetRoleSelectDataByStore(GetRoleSelectDataContext context);

        [OperationContract]
        ResponseContext<EditGiftRoleDataModel> EditGiftRoleDataByStore(EditGiftRoleDataContext context);

        [OperationContract]
        ResponseContext<List<GetTreeRoleDataModel>> GetTreeRoleDataByStore(GetTreeRoleDataContext context);
    }
}
