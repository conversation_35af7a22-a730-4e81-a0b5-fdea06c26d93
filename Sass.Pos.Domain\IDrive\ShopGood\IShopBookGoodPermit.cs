﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Rms.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive
{
    public interface IShopBookGoodPermit
    {
        ResponseContext<ReturnInt> SavePermit(SaveShopBookPermitContext context);

        ResponseContext<DeletePermitDataModel> DeletePermitData(DeletePermitDataContext context);

        ResponseContext<RespPaginationModel<GetPermitDataExModel>> GetPermitDataEx(GetPermitDataExContext context);

        ResponseContext<int> ChangePermitStatus(ChangePermitStatusContext context);

        /// <summary>
        /// 通过预约许可商品ID与支付Id获取Sku相关信息
        /// </summary>
        /// <returns></returns>
        List<GetOrderBookSkuModel> GetOrderSkuList(List<GetOrderBookSkuContext> contexts);

        List<GetOrderBookSkuModel> GetSkuList(GetSkuListContext context);

        List<GetShopTimeMode> GetShopTimeMode();

        List<string> GetPeimitRtNos(List<int> permitIds);
    }
}
