﻿using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class rminfoApp : AppBase<rminfo>
    {
        public List<OpeningModel> GetStoreRoom(int shopId)
        {
            var rmQuery = Repository.rminfo.IQueryable().AsEnumerable();
            var areaQuery = Repository.areainfo.IQueryable().AsEnumerable();

            var data = (from rm in rmQuery
                        join area in areaQuery on new { rm.AreaId, rm.ShopId } equals new { area.AreaId, area.ShopId }
                        where rm.ShopId == shopId
                        group rm by new { area.AreaName, area.AreaId } into groupData
                        orderby groupData.Key.AreaId
                        select new OpeningModel()
                        {
                            Area = groupData.Key.AreaName,
                            RmList = groupData.ToList()
                        }).ToList();

            return data;
        }
    }
}
