﻿using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    public abstract class ShopDataBaseProxy : BookDateBaseProxy
    {
        public virtual List<GetBookShopModel> GetBookShop()
        {
            var result = AppSingle.App.Storage.DbDat.ShopList.Select(i => new GetBookShopModel()
            {
                Id = i.ShopId.ToString(),
                Name = i.ShopName
            }).ToList();

            return result.ToList();
        }
    }
}
