﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Global.Configs.SaasPos
{
    public class ShopBookConfig
    {
        public Dictionary<int, string> WeekDic = new Dictionary<int, string>();
        public ShopBookConfig()
        {
            WeekDic.Add(0, "星期日");
            WeekDic.Add(1, "星期一");
            WeekDic.Add(2, "星期二");
            WeekDic.Add(3, "星期三");
            WeekDic.Add(4, "星期四");
            WeekDic.Add(5, "星期五");
            WeekDic.Add(6, "星期六");
        }

        /// <summary>
        /// 查询全部预约商品信息
        /// </summary>
        public string AllWeek = "7";
    }
}
