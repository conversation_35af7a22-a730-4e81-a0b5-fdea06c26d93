﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    /// <summary>
    /// 订单返还管理
    /// </summary>
    public interface IOrderManageReturn
    {
        /// <summary>
        /// 查询订单返还信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<GetOrderReturnByIdModel> GetOrderReturnById(GetOrderReturnByIdContext context);

        /// <summary>
        /// 订单返还
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<UserOrderReturnModel> UserOrderReturn(UserOrderReturnContext context);
    }
}
