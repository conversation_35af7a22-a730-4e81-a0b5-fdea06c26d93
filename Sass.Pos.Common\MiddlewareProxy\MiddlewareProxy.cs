﻿using MiddlewareLibrary.NoSql;
using Saas.Pos.Common.Objects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MiddlewareProxy
{
    public class MiddlewareProxy
    {
        public MiddlewareProxy()
        {
            MQ = new RabbitMq.RabbitMQ();
            DB_KV = IOC.DI<IKeyValueSql>("DB_KV");
        }
        public RabbitMq.RabbitMQ MQ { get; set; }
        /// <summary>
        ///KEY -VALUE  数据库
        /// </summary>
        public IKeyValueSql DB_KV { get; set; }
    }
}
