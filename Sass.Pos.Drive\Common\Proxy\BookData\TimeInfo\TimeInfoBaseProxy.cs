﻿using Saas.Pos.Drive.Common.Extend;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    public abstract class TimeInfoBaseProxy : BookDateBaseProxy
    {
        public virtual object GetTimeSection(DateTime date)
        {
            //获取门店所有房型数量
            var totalCount = AppSingle.App.Storage.BookingDat.RoomDataStorage.Where(w => w.ShopId == Proxy.Context.ShopId).GroupBy(x => x.RtNo).Select(x => x.Max(w => w.TotalCount)).ToList().Sum();
            var workNotTimes = AppSingle.App.Storage.BookingDat.WorkNotShopTimeList.Where(w => w.ShopId == Proxy.Context.ShopId).ToList();
            var bookCacheList = AppSingle.App.Storage.BookingDat.BookCacheList.Where(w => w.ShopId == Proxy.Context.ShopId && w.ComeDate == date.ToString("yyyy-MM-dd")).ToList();
            var query = AppSingle.App.Storage.DbDat.ShopTimeList.Where(w => w.ShopId == Proxy.Context.ShopId);
            var timeQuery = AppSingle.App.Storage.DbDat.TimeList;

            var list = (from qu in query
                        join time in timeQuery on qu.TimeNo equals time.TimeNo
                        orderby time.BegTime
                        select new
                        {
                            TimeNo = qu.TimeNo,
                            TimeName = time.TimeName
                        }).ToList();

            var resultData = list.Select(w =>
            {
                var workNotTime = workNotTimes.FirstOrDefault(x => x.WorkTimeStart >= date.Date && x.WorkTimeEnd >= date.Date && x.TimeNo == w.TimeNo);
                //查出所有可直落时段
                var timeInfo = timeQuery.GetFirst(w.TimeNo);
                var allTimeNos = timeInfo.GetTimeNos(query.ToList(), timeQuery, Proxy.Context.ShopId, date);
                allTimeNos.Remove(w.TimeNo);//去除掉本身这个时段才是可直落时段
                //生成所有直落时段
                var strDownList = allTimeNos.Select(x =>
                {
                    var strWorkNotTime = workNotTimes.FirstOrDefault(z => z.WorkTimeStart >= date.Date && z.WorkTimeEnd >= date.Date && z.TimeNo == x);
                    var shopTime = list.FirstOrDefault(z => z.TimeNo == x);

                    return new BookTimeInfoModel()
                    {
                        TNo = x,
                        TName = shopTime.TimeName,
                        TotCount = totalCount,
                        ReQty = totalCount,
                        Stop = strWorkNotTime == null ? false : true
                    };
                }).ToList();

                return new BookTimeInfoModel()
                {
                    TNo = w.TimeNo,
                    TName = w.TimeName,
                    TotCount = totalCount,
                    ReQty = totalCount,
                    Stop = workNotTime == null ? false : true,
                    StrDown = strDownList
                };
            }).ToList();

            var data = new List<BookTimeInfoModel>();

            bookCacheList.ForEach(cache =>
            {
                var nomalTimes = resultData.FirstOrDefault(w => w.TNo == cache.BeginTimeNo);
                nomalTimes.ReQty -= 1;

                //直落预约数据
                if (cache.BeginTimeNo != cache.EndTimeNo)
                {
                    var beginTimeInfo = AppSingle.App.Storage.DbDat.TimeList.GetFirst(cache.BeginTimeNo);
                    var crossTimeNos = beginTimeInfo.GetCrossTimeNos(AppSingle.App.Storage.DbDat.ShopTimeList,
                                                            AppSingle.App.Storage.DbDat.TimeList,
                                                            cache.ShopId, cache.EndTimeNo, date);
                    //获取到跨越的时段，由于跨越时段里面查不到第一条数据，所以第一条数据需要单独减掉
                    nomalTimes.StrDown.Where(w => crossTimeNos.Contains(w.TNo)).ToList().ForEach(w =>
                    {
                        w.ReQty -= 1;
                    });
                }
            });

            return resultData;
        }
    }
}
