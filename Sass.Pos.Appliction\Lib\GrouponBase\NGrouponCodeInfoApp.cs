﻿using Saas.Pos.Model.Coupons.Model;
using Saas.Pos.Model.GrouponBase;
using Saas.Pos.Model.GrouponBase.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.GrouponBase
{
    public partial class NGrouponCodeInfoApp : AppBase<NGrouponCodeInfo>
    {
        public List<CutomCouponInfo> GetGrouponDetail(GetGrouponInfoContext context)
        {
            return Repository.NGrouponCodeInfo.GetGrouponDetail(context);
        }

        public List<CutomCouponInfo> GetGrouponDetailByDistributeId(int month, Guid distributeId)
        {
            return Repository.NGrouponCodeInfo.GetGrouponDetailByDistributeId(month, distributeId);
        }

        public NGrouponCodeInfo GetPreGrouponCode(int month, Guid distributeId)
        {
            return Repository.NGrouponCodeInfo.GetPreGrouponCode(month, distributeId);
        }

        public List<GetCustomUsedCouponModel> GetUsedCustomCoupon(Guid distributeId)
        {
            return Repository.NGrouponCodeInfo.GetUsedCustomCoupon(distributeId);
        }

        public int SendCodeInfo(SendCodeInfoContext context)
        {
            return Repository.NGrouponCodeInfo.SendCodeInfo(context);
        }
    }
}
