﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    public static class EnumHelper
    {
        public static string GetEnumDescription(Enum value)
        {
            var fieldInfo = value.GetType().GetField(value.ToString());
            if (fieldInfo == null)
                throw new Exception("枚举值不存在！");

            var attribute = (DescriptionAttribute)fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false).FirstOrDefault();

            return attribute != null ? attribute.Description : value.ToString();
        }

        /// <summary>
        /// 将枚举类型转换成List集合
        /// </summary>
        /// <param name="enumType"></param>
        /// <returns></returns>
        public static List<KeyValuePair<int, string>> GetEnumList(this Type enumType)
        {
            var dic = new List<KeyValuePair<int, string>>();

            if (!enumType.IsEnum)
                throw new Exception("非枚举类型！");

            var enumValues = Enum.GetValues(enumType);
            foreach (Enum item in enumValues)
            {
                string description = GetEnumDescription(item);
                dic.Add(new KeyValuePair<int, string>((int)Enum.Parse(enumType, item.ToString()), description));
            }

            return dic;
        }

        /// <summary>
        /// 根据description获取枚举的Value值
        /// </summary>
        /// <param name="description"></param>
        /// <returns></returns>
        public static int GetValue<T>(string description)
        {
            foreach (var field in typeof(T).GetFields())
            {
                var attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
                if (attribute != null)
                {
                    if (attribute.Description == description)
                    {
                        return (int)field.GetValue(null);
                    }
                }
            }
            return 0;
        }

        public static Dictionary<int, string> EnumToDictionary<T>() where T : Enum
        {
            var enumType = typeof(T);
            var values = Enum.GetValues(enumType);
            var dict = new Dictionary<int, string>();

            foreach (var value in values)
            {
                dict[(int)value] = Enum.GetName(enumType, value);
            }

            return dict;
        }


        /// <summary>
        /// 将枚举类型转换成List集合
        /// </summary>
        /// <param name="enumType"></param>
        /// <returns></returns>
        public static List<KeyValuePair<string, string>> GetEnumListToString(this Type enumType)
        {
            var dic = new List<KeyValuePair<string, string>>();

            if (!enumType.IsEnum)
                throw new Exception("非枚举类型！");

            var enumValues = Enum.GetValues(enumType);
            foreach (Enum item in enumValues)
            {
                string description = GetEnumDescription(item);
                dic.Add(new KeyValuePair<string, string>(((int)Enum.Parse(enumType, item.ToString())).ToString(), description));
            }

            return dic;
        }
    }
}
