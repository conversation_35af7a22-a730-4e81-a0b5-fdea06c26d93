﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class FoodApp : AppBase<Food>
    {
        public List<StoreReportModel> GetStoreReport(GetStoreReportContext context)
        {
            return Repository.Food.GetStoreReport(context);
        }

        public List<GetStoreFtTypeReportModel> GetTypeDetailReport(GetStoreFtTypeReportContext context)
        {
            return Repository.Food.GetDetailReport(context);
        }

        public List<HeadCountModel> GetHeadCountReport(GetStoreReportContext context)
        {
            return Repository.Food.GetHeadCountReport(context);
        }

        public List<WaitCreateModel> GetWaitCreate()
        {
            return Repository.Food.GetWaitCreate();
        }

        public GetFoodModel GetFood(string fdNo)
        {
            return Repository.Food.GetFood(fdNo);
        }

        /// <summary>
        /// 查询天王商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetFoodDataModel> GetFoodData(GetFoodDataContext context)
        {
            return Repository.Food.GetFoodData(context);
        }

        /// <summary>
        /// 查询天王商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetFdTypeDataModel> GetFdTypeData(GetFdTypeDataContext context)
        {
            return Repository.Food.GetFdTypeData(context);
        }

        public List<GetUserScenceFdNoModel> GetUserSceFdNoData(GetSceneRoleConfigModel context)
        {
            return Repository.Food.GetUserSceFdNoData(context);
        }
    }
}
