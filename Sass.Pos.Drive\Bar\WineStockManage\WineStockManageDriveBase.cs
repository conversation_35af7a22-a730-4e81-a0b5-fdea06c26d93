﻿using Saas.Pos.Common.Cache;
using Saas.Pos.Domain.IDrive.WineStockManage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    public class WineStockManageDriveBase : DriveBase
    {
        IWineStockReport _Wsr;
        /// <summary>
        /// 存酒报表
        /// </summary>
        public IWineStockReport Wsr
        {
            get
            {
                if (_Wsr == null)
                    _Wsr = new WineStockReportDrive(this, app);
                return _Wsr;

                //IWineStockReport wsr = null;
                //if (CustormMemoryCache.Cache.TryGetValue("WineStockReportDrive", out wsr))
                //    return wsr;
                //else
                //{
                //    wsr = new WineStockReportDrive(this);
                //    CustormMemoryCache.Cache.SetValue("WineStockReportDrive", wsr, DateTimeOffset.Now.AddMinutes(20));
                //    return wsr;
                //}
            }
        }

        IWineStockUser _Wsu;
        /// <summary>
        /// 用户存酒数据
        /// </summary>
        public IWineStockUser Wsu
        {
            get
            {
                if (_Wsu == null)
                    _Wsu = new WineStockUserDrive(this, app);
                return _Wsu;

                //IWineStockUser wsu = null;
                //if (CustormMemoryCache.Cache.TryGetValue("WineStockUserDrive", out wsu))
                //    return wsu;
                //else
                //{
                //    wsu = new WineStockUserDrive(this);
                //    CustormMemoryCache.Cache.SetValue("WineStockUserDrive", wsu, DateTimeOffset.Now.AddMinutes(20));
                //    return wsu;
                //}
            }
        }

        IFetchWineUser _Fwu;
        /// <summary>
        /// 取酒
        /// </summary>
        public IFetchWineUser Fwu
        {
            get
            {
                if (_Fwu == null)
                    _Fwu = new FetchWineUserDrive(this, app);
                return _Fwu;

                //IFetchWineUser fwu = null;
                //if (CustormMemoryCache.Cache.TryGetValue("FetchWineUserDrive", out fwu))
                //    return fwu;
                //else
                //{
                //    fwu = new FetchWineUserDrive(this);
                //    CustormMemoryCache.Cache.SetValue("FetchWineUserDrive", fwu, DateTimeOffset.Now.AddMinutes(20));
                //    return fwu;
                //}
            }
        }


        ISaveWineUser _Swu;
        /// <summary>
        /// 存酒
        /// </summary>
        public ISaveWineUser Swu
        {
            get
            {
                if (_Swu == null)
                    _Swu = new SaveWineUserDrive(this, app);
                return _Swu;
                //ISaveWineUser swu = null;
                //if (CustormMemoryCache.Cache.TryGetValue("SaveWineUserDrive", out swu))
                //    return swu;
                //else
                //{
                //    swu = new SaveWineUserDrive(this);
                //    CustormMemoryCache.Cache.SetValue("SaveWineUserDrive", swu, DateTimeOffset.Now.AddMinutes(20));
                //    return swu;
                //}
            }
        }

        IDrCheckManage _Dcm;
        /// <summary>
        /// 授权
        /// </summary>
        public IDrCheckManage Dcm
        {
            get
            {
                if (_Dcm == null)
                    _Dcm = new DrCheckManageDrive(this, app);
                return _Dcm;
                //IDrCheckManage dcm = null;
                //if (CustormMemoryCache.Cache.TryGetValue("DrCheckManageDrive", out dcm))
                //    return dcm;
                //else
                //{
                //    dcm = new DrCheckManageDrive(this);
                //    CustormMemoryCache.Cache.SetValue("DrCheckManageDrive", dcm, DateTimeOffset.Now.AddMinutes(20));
                //    return dcm;
                //}
            }
        }

        IWineData _Wd;
        /// <summary>
        /// 酒水信息管理
        /// </summary>
        public IWineData Wd
        {
            get
            {
                if (_Wd == null)
                    _Wd = new WineDataDrive(this, app);
                return _Wd;
                //IWineData wd = null;
                //if (CustormMemoryCache.Cache.TryGetValue("WineDataDrive", out wd))
                //    return wd;
                //else
                //{
                //    wd = new WineDataDrive(this);
                //    CustormMemoryCache.Cache.SetValue("WineDataDrive", wd, DateTimeOffset.Now.AddMinutes(20));
                //    return wd;
                //}
            }
        }
    }
}
