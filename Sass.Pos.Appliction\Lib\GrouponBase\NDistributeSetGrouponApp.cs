﻿using Saas.Pos.Model.Coupons.Context;
using Saas.Pos.Model.Coupons.Model;
using Saas.Pos.Model.GrouponBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Application.Lib.GrouponBase
{
    public partial class NDistributeSetGrouponApp : AppBase<NDistributeSetGroupon>
    {
        public List<GetCustomCouponDataModel> GetCustomCoupon(GetCustomCouponDataContext context)
        {
            return Repository.NDistributeSetGroupon.GetCustomCoupon(context);
        }
    }
}
