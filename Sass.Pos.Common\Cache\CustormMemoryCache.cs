﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Common.Cache
{
    public class CustormMemoryCache
    {
        /// <summary>
        /// 内存缓存
        /// </summary>
        MemoryCache cache = null;
        /// <summary>
        /// 默认过期时间
        /// </summary>
        int ExpireTime = 30;
        CustormMemoryCache()
        {
            cache = MemoryCache.Default;
        }

        #region 单例模式
        volatile static object lockObj = new object();
        static CustormMemoryCache _cache;
        public static CustormMemoryCache Cache
        {
            get
            {
                if (_cache == null)
                {
                    lock (lockObj)
                    {
                        if (_cache == null)
                            _cache = new CustormMemoryCache();
                    }
                }

                return _cache;
            }
        }
        #endregion

        public void SetValue<T>(string key, T obj, DateTimeOffset? expire = null)
        {
            if (cache == null)
                throw new NullReferenceException("缓存未初始化！");
            if (!expire.HasValue)
                expire = DateTimeOffset.Now.AddMinutes(ExpireTime);

            cache.Set(key, obj, expire.Value);
        }

        /// <summary>
        /// 确定内存中存在该key就返回值，否则会抛出异常
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public T GetValue<T>(string key)
        {
            if (cache == null)
                throw new NullReferenceException("缓存未初始化！");

            if (!cache.Contains(key))
                throw new Exception($"名称：{key}，不存在缓存中！");

            return (T)cache.Get(key);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="obj"></param>
        /// <returns></returns>
        public bool TryGetValue<T>(string key, out T obj)
        {
            if (cache == null)
                throw new NullReferenceException("缓存未初始化！");

            obj = default(T);
            if (!cache.Contains(key))
                return false;

            obj = (T)cache.Get(key);
            return true;
        }

        public bool Exist(string key)
        {
            if (cache == null)
                throw new NullReferenceException("缓存未初始化！");

            return cache.Contains(key);
        }

        public void RemoveKey(string key)
        {
            if (cache == null)
                throw new NullReferenceException("缓存未初始化！");

            if (cache.Contains(key))
                cache.Remove(key);
        }
    }
}
