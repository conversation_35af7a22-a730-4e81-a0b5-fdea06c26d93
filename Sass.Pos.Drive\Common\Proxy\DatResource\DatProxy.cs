﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.DatResource
{
    /// <summary>
    /// 数据资源代理基类
    /// </summary>
    public abstract class DatProxy : SysProxyBase
    {


        /// <summary>
        /// 数据代理集合
        /// </summary>
        protected List<DatProxy> DatProxyList = new List<DatProxy>();
        /// <summary>
        /// 数据仓储总代理
        /// </summary>
        protected StorageProxy Storage;
        /// <summary>
        /// 数据刷新
        /// </summary>
        public abstract void Refresh();
        /// <summary>
        /// 数据资源释放
        /// </summary>
        public abstract void Dispose();
        public void SetStorage(StorageProxy storage)
        {
            this.Storage = storage;
        }
        /// <summary>
        /// 设置代理
        /// </summary>
        /// <param name="proxy"></param>
        public virtual void SetProxy(DatProxy proxy)
        {
            DatProxyList.Add(proxy);
        }


    }
}
