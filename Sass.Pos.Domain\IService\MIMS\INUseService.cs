﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.MIMS.Context;
using Saas.Pos.Model.MIMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.MIMS
{
    [ServiceContract]
    public interface INUseService
    {
        [OperationContract]
        ResponseContext<List<GetNUseInfoModel>> GetBillRechargePlan(GetNUseInfoContext context);

        [OperationContract]
        ResponseContext<List<GetUseCouponInfoModel>> GetRechargePlan(GetUseCouponInfoContext context);
    }
}
