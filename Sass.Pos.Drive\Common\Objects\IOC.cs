﻿using Spring.Context;
using Spring.Context.Support;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Common.Objects
{
    /// <summary>
    /// 控制反转
    /// </summary>
    public static class IOC
    {
        static IApplicationContext _ctx;
        static IApplicationContext ctx
        {
            get
            {
                if (_ctx == null) _ctx = ContextRegistry.GetContext();
                return _ctx;
            }
        }
        /// <summary>
        /// 依赖注入
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="className">类名称</param>
        public static T DI<T>(string className)
        {
            try
            {
                return (T)ctx.GetObject(className);
            }
            catch (Exception ex)
            {

              return  default(T);
            }

        }
    }
}
