﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Rms
{
    [ServiceContract]
    public interface IRmsOpeningService
    {
        [OperationContract]
        ResponseContext<GetOpenCacheModel> GetOpenCacheData(GetOpenCacheContext context);

        [OperationContract]
        ResponseContext<string> OnlineOpening(OnlineOpeningContext context);

        [OperationContract]
        ResponseContext<List<OpeningModel>> GetStoreRoom(GetStoreRoomContext context);

        [OperationContract]
        ResponseContext<GetRoomOrderItemModel> GetRoomOrderItem(GetRoomOrderItemContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetFoodHeaderDataModel>> GetFoodHeaderData(GetFoodHeaderDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetDepositListModel>> GetDepositList(GetDepositListContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetBirthdayGiftRecordModel>> GetBirthdayList(GetBirthdayGiftRecordContext context);

        [OperationContract]
        ResponseContext<Model.SaasPos.Context.ReturnInt> SetRecordPrint(SetPrintRecordStatusContext context);


        [OperationContract]
        ResponseContext<Model.SaasPos.Context.ReturnInt> SetDepositState(SetDepositStateContext context);

        [OperationContract]
        ResponseContext<Model.SaasPos.Context.ReturnInt> SetBirthdPritState(SetBirthdPritStateContext context);

        [OperationContract]
        ResponseContext<GetOpenCacheDataModel> GetOpenCacheHeadData(GetOpenCacheDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetOpenRoomRecordsModel>> GetOpenRoomRecords(GetOpenRoomRecordsContext context);

        [OperationContract]
        ResponseContext<Model.SaasPos.Context.ReturnBool> EditOpenRoomRecords(EditOpenRoomRecordsContext context);

        [OperationContract]
        ResponseContext<Model.SaasPos.Context.ReturnInt> AllPrintedState(AllPrintedStateContext context);

        [OperationContract]
        ResponseContext<List<KeyValuePair<string, string>>> GetFoodTimeData(GetFoodTimeDataContext context);

        [OperationContract]
        ResponseContext<BookCacheInfo> GetOpenHeadInfo(GetOpenCacheInfoContext context);
    }
}
