﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.Rms
{
    public partial interface IbookcacheinfoRepository : IRepositoryBase<bookcacheinfo>
    {
        List<GetBookCacheModel> GetBookList(GetBookCacheContext context);

        List<BookCaCheModel> GetCaCheData(DateTime startTime, DateTime endTime);

        int GetOverTimeCount(int shopId, string comeDate, string timeName);

        List<GetOrderMakeByDay> GetShopMakeData(GetShopMakeDataContext context);

        List<GetMakeDataByDay> GetShopMakeExData(GetShopMakeDataContextByDay context);

        int GetOrdinaryCount(string comeDate, string custTel);

    }
}
