﻿using Saas.Pos.Model.SongBase;
using Saas.Pos.Model.SongBase.Context;
using Saas.Pos.Model.SongBase.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Appliction.Lib.SongBase
{
    public partial class SongScanApp : AppBase<SongScan>
    {
        public List<GetSongScanModel> GetSongScanList(GetSongScanContext context)
        {
            return Repository.SongScan.GetSongScanList(context);
        }
    }
}
