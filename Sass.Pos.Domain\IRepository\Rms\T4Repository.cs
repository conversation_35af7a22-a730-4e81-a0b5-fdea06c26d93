﻿

using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.Rms
{
 public partial interface Iapi_messageRepository : IRepositoryBase<api_message> {}
  

 public partial interface IareainfoRepository : IRepositoryBase<areainfo> {}
  

 public partial interface IbehapartinfoRepository : IRepositoryBase<behapartinfo> {}
  

 public partial interface IBehatypeinfoRepository : IRepositoryBase<Behatypeinfo> {}
  

 public partial interface IbillstatusRepository : IRepositoryBase<billstatus> {}
  

 public partial interface Ibirthday_gift_recordRepository : IRepositoryBase<birthday_gift_record> {}
  

 public partial interface Ibirthday_gift_record_detailedRepository : IRepositoryBase<birthday_gift_record_detailed> {}
  

 public partial interface Ibirthday_virtual_orderRepository : IRepositoryBase<birthday_virtual_order> {}
  

 public partial interface IbookcacheinfoRepository : IRepositoryBase<bookcacheinfo> {}
  

 public partial interface Ibookcacheinfo_preorderRepository : IRepositoryBase<bookcacheinfo_preorder> {}
  

 public partial interface IbookhistoryRepository : IRepositoryBase<bookhistory> {}
  

 public partial interface Ibookhistory_bakRepository : IRepositoryBase<bookhistory_bak> {}
  

 public partial interface Ibookhistory_bak202312081Repository : IRepositoryBase<bookhistory_bak202312081> {}
  

 public partial interface IbookmessageRepository : IRepositoryBase<bookmessage> {}
  

 public partial interface IcontypeinfoRepository : IRepositoryBase<contypeinfo> {}
  

 public partial interface IcontypetimeinfoRepository : IRepositoryBase<contypetimeinfo> {}
  

 public partial interface IcustbehainfoRepository : IRepositoryBase<custbehainfo> {}
  

 public partial interface IcustdemandRepository : IRepositoryBase<custdemand> {}
  

 public partial interface IcustdemandtitleRepository : IRepositoryBase<custdemandtitle> {}
  

 public partial interface ICustInfoRepository : IRepositoryBase<CustInfo> {}
  

 public partial interface Ideposit_foodRepository : IRepositoryBase<deposit_food> {}
  

 public partial interface IdepositinfoRepository : IRepositoryBase<depositinfo> {}
  

 public partial interface IexchangewaitRepository : IRepositoryBase<exchangewait> {}
  

 public partial interface IheartbeatRepository : IRepositoryBase<heartbeat> {}
  

 public partial interface IluserinfoRepository : IRepositoryBase<luserinfo> {}
  

 public partial interface ImsmsRepository : IRepositoryBase<msms> {}
  

 public partial interface IMSpeer_conflictdetectionconfigrequest1Repository : IRepositoryBase<MSpeer_conflictdetectionconfigrequest1> {}
  

 public partial interface IMSpeer_conflictdetectionconfigresponse1Repository : IRepositoryBase<MSpeer_conflictdetectionconfigresponse1> {}
  

 public partial interface IMSpeer_lsns1Repository : IRepositoryBase<MSpeer_lsns1> {}
  

 public partial interface IMSpeer_originatorid_history1Repository : IRepositoryBase<MSpeer_originatorid_history1> {}
  

 public partial interface IMSpeer_request1Repository : IRepositoryBase<MSpeer_request1> {}
  

 public partial interface IMSpeer_response1Repository : IRepositoryBase<MSpeer_response1> {}
  

 public partial interface ImvodmsgRepository : IRepositoryBase<mvodmsg> {}
  

 public partial interface ImvodroomRepository : IRepositoryBase<mvodroom> {}
  

 public partial interface INumberManageRepository : IRepositoryBase<NumberManage> {}
  

 public partial interface IopencacheinfoRepository : IRepositoryBase<opencacheinfo> {}
  

 public partial interface IopenhistoryRepository : IRepositoryBase<openhistory> {}
  

 public partial interface Iopenhistory_bakRepository : IRepositoryBase<openhistory_bak> {}
  

 public partial interface IoperationRepository : IRepositoryBase<operation> {}
  

 public partial interface IorderuserRepository : IRepositoryBase<orderuser> {}
  

 public partial interface IpricemodelRepository : IRepositoryBase<pricemodel> {}
  

 public partial interface IprintattributeRepository : IRepositoryBase<printattribute> {}
  

 public partial interface IprintrecordRepository : IRepositoryBase<printrecord> {}
  

 public partial interface IrmexchangeRepository : IRepositoryBase<rmexchange> {}
  

 public partial interface IrminfoRepository : IRepositoryBase<rminfo> {}
  

 public partial interface IrminfoChangedRepository : IRepositoryBase<rminfoChanged> {}
  

 public partial interface IRmOperationRepository : IRepositoryBase<RmOperation> {}
  

 public partial interface IRmOperation_HistoryRepository : IRepositoryBase<RmOperation_History> {}
  

 public partial interface IrmsmemberinfoRepository : IRepositoryBase<rmsmemberinfo> {}
  

 public partial interface IrtinfoRepository : IRepositoryBase<rtinfo> {}
  

 public partial interface IrtpriceRepository : IRepositoryBase<rtprice> {}
  

 public partial interface IshopbookusedInfoRepository : IRepositoryBase<shopbookusedInfo> {}
  

 public partial interface IshopinfoRepository : IRepositoryBase<shopinfo> {}
  

 public partial interface IshoporderinfoRepository : IRepositoryBase<shoporderinfo> {}
  

 public partial interface IshoporderitemRepository : IRepositoryBase<shoporderitem> {}
  

 public partial interface IshoptimeinfoRepository : IRepositoryBase<shoptimeinfo> {}
  

 public partial interface Isysarticlecolumns1Repository : IRepositoryBase<sysarticlecolumns1> {}
  

 public partial interface Isysarticles1Repository : IRepositoryBase<sysarticles1> {}
  

 public partial interface Isysarticleupdates1Repository : IRepositoryBase<sysarticleupdates1> {}
  

 public partial interface Isyspublications1Repository : IRepositoryBase<syspublications1> {}
  

 public partial interface Isysschemaarticles1Repository : IRepositoryBase<sysschemaarticles1> {}
  

 public partial interface Isyssubscriptions1Repository : IRepositoryBase<syssubscriptions1> {}
  

 public partial interface Isystranschemas1Repository : IRepositoryBase<systranschemas1> {}
  

 public partial interface ItimeinfoRepository : IRepositoryBase<timeinfo> {}
  

 public partial interface ItriggerRecordRepository : IRepositoryBase<triggerRecord> {}
  

 public partial interface IuserinfoRepository : IRepositoryBase<userinfo> {}
  

 public partial interface IworkbookoutRepository : IRepositoryBase<workbookout> {}
  

 public partial interface IworkconfigRepository : IRepositoryBase<workconfig> {}
  

 public partial interface IworknotcontypeRepository : IRepositoryBase<worknotcontype> {}
  

 public partial interface IworknotrtinfoRepository : IRepositoryBase<worknotrtinfo> {}
  

 public partial interface IworknotshopRepository : IRepositoryBase<worknotshop> {}
  

 public partial interface IworknotshoptimeRepository : IRepositoryBase<worknotshoptime> {}
  

}
