﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.IO;

namespace Saas.Pos.Common.Tools
{
    public class EncryptionHelper
    {
        /// <summary>
        /// 解密字符串
        /// </summary>
        /// <param name="encryptedString"></param>
        /// <returns></returns>
        public static string DecryptString(string encryptedString)
        {
            byte[] encryptedBytes = Convert.FromBase64String(encryptedString);

            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes(GlobalConfig.Global.Comm.CouponSecretKey);  // 密钥长度为 32 字节
                aesAlg.Mode = CipherMode.ECB;  // 使用 ECB 模式
                aesAlg.Padding = PaddingMode.PKCS7;  //使用 PKCS7 填充

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, null);  // ECB 模式不使用 IV

                byte[] decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
                return Encoding.UTF8.GetString(decryptedBytes);
            }
        }

        /// <summary>
        /// 加密字符串
        /// </summary>
        /// <param name="plainText"></param>
        /// <returns></returns>
        public static string Encrypt(string plainText)
        {
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Encoding.UTF8.GetBytes(GlobalConfig.Global.Comm.CouponSecretKey);  // 密钥长度为 32 字节
                aesAlg.Mode = CipherMode.ECB;  // 使用 ECB 模式
                aesAlg.Padding = PaddingMode.PKCS7;  // 使用 PKCS7 填充

                // 创建加密器
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, null);  // ECB 模式不使用 IV

                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);  // 将明文字符串转换为字节数组
                byte[] encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);  // 执行加密操作

                return Convert.ToBase64String(encryptedBytes);  // 将加密后的字节数组转换为 Base64 字符串，便于存储或传输
            }
        }
    }
}
