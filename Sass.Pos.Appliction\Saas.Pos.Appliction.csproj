﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C8B52C23-7473-4B86-986E-B0154CB35D5C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Saas.Pos.Appliction</RootNamespace>
    <AssemblyName>Saas.Pos.Appliction</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentApplicationServiceInterface">
      <HintPath>..\Sass.Pos.Model\Packages\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="ComponentCore">
      <HintPath>..\Sass.Pos.Model\Packages\ComponentCore.dll</HintPath>
    </Reference>
    <Reference Include="Dapper">
      <HintPath>..\Sass.Pos.Model\Packages\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data">
      <HintPath>..\Sass.Pos.Model\Packages\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="NET_ADO">
      <HintPath>..\Sass.Pos.Model\Packages\NET_ADO.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Lib\Bar\WineStockApp.cs" />
    <Compile Include="Lib\Bar\WineStockReportApp.cs" />
    <Compile Include="Lib\DbFood\AppBase.cs" />
    <Compile Include="Lib\DbFood\EmpGift_RecordApp.cs" />
    <Compile Include="Lib\DbFood\FdCashApp.cs" />
    <Compile Include="Lib\DbFood\FdCashBakApp.cs" />
    <Compile Include="Lib\DbFood\FdCashOrderApp.cs" />
    <Compile Include="Lib\DbFood\FdInvApp.cs" />
    <Compile Include="Lib\DbFood\FdUserApp.cs" />
    <Compile Include="Lib\DbFood\FoodApp.cs" />
    <Compile Include="Lib\DbFood\FoodLabelApp.cs" />
    <Compile Include="Lib\DbFood\GiftAccountApp.cs" />
    <Compile Include="Lib\DbFood\GiftAccountSceneAllocationApp.cs" />
    <Compile Include="Lib\DbFood\GiftRoleApp.cs" />
    <Compile Include="Lib\DbFood\Inv_TimeSectionApp.cs" />
    <Compile Include="Lib\DbFood\Limit_ConfigInfoApp.cs" />
    <Compile Include="Lib\DbFood\MobileFtTypeApp.cs" />
    <Compile Include="Lib\DbFood\RoomApp.cs" />
    <Compile Include="Lib\DbFood\SceneRole_ConfigApp.cs" />
    <Compile Include="Lib\DbFood\T4Application.cs">
      <DependentUpon>T4Application.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Lib\DbFood\UserInfo_BindingApp.cs" />
    <Compile Include="Lib\DbFood\wxPayInfoApp.cs" />
    <Compile Include="Lib\GrouponBase\AppBase.cs" />
    <Compile Include="Lib\GrouponBase\NDistributeSetGrouponApp.cs" />
    <Compile Include="Lib\GrouponBase\NGrouponCodeInfoApp.cs" />
    <Compile Include="Lib\GrouponBase\T4Application.cs">
      <DependentUpon>T4Application.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Lib\MIMS\AppBase.cs" />
    <Compile Include="Lib\MIMS\MemberInfoApp.cs" />
    <Compile Include="Lib\MIMS\T4Application.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>T4Application.tt</DependentUpon>
    </Compile>
    <Compile Include="Lib\Rms\AppBase.cs" />
    <Compile Include="Lib\Rms\birthday_gift_recordApp.cs" />
    <Compile Include="Lib\Rms\bookcacheinfoApp.cs" />
    <Compile Include="Lib\Rms\depositinfoApp.cs" />
    <Compile Include="Lib\Rms\opencacheinfoApp.cs" />
    <Compile Include="Lib\Rms\printrecordApp.cs" />
    <Compile Include="Lib\Rms\rminfoApp.cs" />
    <Compile Include="Lib\Rms\shopinfoApp.cs" />
    <Compile Include="Lib\Rms\shoporderinfoApp.cs" />
    <Compile Include="Lib\Rms\shoptimeinfoApp.cs" />
    <Compile Include="Lib\Rms\T4Application.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>T4Application.tt</DependentUpon>
    </Compile>
    <Compile Include="Lib\SaasPos\Activity_AttendRecordApp.cs" />
    <Compile Include="Lib\SaasPos\AppBase.cs" />
    <Compile Include="Lib\SaasPos\Commission_EmpRecordApp.cs" />
    <Compile Include="Lib\SaasPos\Commission_SchemesInfoApp.cs" />
    <Compile Include="Lib\SaasPos\Coupon_InfoApp.cs" />
    <Compile Include="Lib\SaasPos\Energy_Metering_RecordApp.cs" />
    <Compile Include="Lib\SaasPos\Order_RelevanceApp.cs" />
    <Compile Include="Lib\SaasPos\Pos_Storage_FoodApp.cs" />
    <Compile Include="Lib\SaasPos\Shop_BookCacheInfoApp.cs" />
    <Compile Include="Lib\SaasPos\Shop_BookGoodApp.cs" />
    <Compile Include="Lib\SaasPos\Shop_TimeRtInfoDepositApp.cs" />
    <Compile Include="Lib\SaasPos\User_OrderApp.cs" />
    <Compile Include="Lib\SaasPos\Way_FoodMapApp.cs" />
    <Compile Include="Lib\SaasPos\T4Application.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>T4Application.tt</DependentUpon>
    </Compile>
    <Compile Include="Lib\SaasPos\Way_VerifyItemApp.cs" />
    <Compile Include="Lib\SongBase\AppBase.cs" />
    <Compile Include="Lib\SongBase\SongScanApp.cs" />
    <Compile Include="Lib\SongBase\T4Application.cs">
      <DependentUpon>T4Application.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Sass.Pos.Domain\Saas.Pos.Domain.csproj">
      <Project>{901140E8-4C47-4B7A-9F14-1D2CD5335042}</Project>
      <Name>Saas.Pos.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sass.Pos.Model\Saas.Pos.Model.csproj">
      <Project>{d3e4e488-5933-4b09-88d9-a5adbf6389ce}</Project>
      <Name>Saas.Pos.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sass.Pos.RepositoryFactory\Saas.Pos.RepositoryFactory.csproj">
      <Project>{84A399DD-EF35-4905-A665-5351FF9F5DD4}</Project>
      <Name>Saas.Pos.RepositoryFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sass.Pos.Repository\Saas.Pos.Repository.csproj">
      <Project>{f2e91580-65ab-41d3-a269-3f140ffebfa0}</Project>
      <Name>Saas.Pos.Repository</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="Lib\DbFood\T4Application.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Application.cs</LastGenOutput>
    </Content>
    <Content Include="Lib\GrouponBase\T4Application.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Application.cs</LastGenOutput>
    </Content>
    <Content Include="Lib\MIMS\T4Application.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Application.cs</LastGenOutput>
    </Content>
    <Content Include="Lib\Rms\T4Application.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Application.cs</LastGenOutput>
    </Content>
    <Content Include="Lib\SaasPos\T4Application.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Application.cs</LastGenOutput>
    </Content>
    <Content Include="Lib\SongBase\T4Application.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>T4Application.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>