﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Metadata.W3cXsd2001;
using System.Text;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Response
{
    public class GetOrderShareInfoResponse
    {
        public OrderInfo order { get; set; }
        public List<ReceiptList> receipt_list { get; set; }
    }

    public class OrderInfo
    {
        public string unified_order_id { get; set; }
        public long order_id { get; set; }
        public string product_snapshot_content_struct { get; set; }
        public int status { get; set; }
    }

    public class ReceiptList 
    {
        public string receipt_code { get; set; }
        /// <summary>
        /// 券状态：1： 未使用、未过期2：已使用3：未使用、已过期
        /// 4：已退款 （未消费退款）6：已退款 （已消费退）
        /// </summary>
        public int status { get; set; }

        public long receipt_begin_time { get; set; }

        public long receipt_end_time { get; set; }
        public List<AmountShareList> amount_share_list { get; set; }
    }

    public class AmountShareList 
    {
        public int amount_type { get; set; }
        public string title { get; set; }
        public int value { get; set; }
    }
}
