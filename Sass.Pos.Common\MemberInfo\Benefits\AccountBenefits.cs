﻿using Saas.Pos.Common.MemberInfo.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MemberInfo.Benefits
{
    public class AccountBenefits : BenefitsBase<AccountBenefit>
    {
        public AccountBenefits(BenefitConfigContext<AccountBenefit> _Coupons) : base(_Coupons)
        {
        }

        public override bool Check(GetMemberConfigContext context)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 对于账户的处理
        /// </summary>
        /// <returns></returns>
        public AccountProcessModel AccountProcess(AccountProcessContext context)
        {
            return new AccountProcessModel();
        }
    }
}
