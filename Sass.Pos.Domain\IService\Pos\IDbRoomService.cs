﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IDbRoomService
    {
        [OperationContract]
        ResponseContext<GetRoomInfoModel> GetRoomInfo(GetRoomInfoContext context);

        [OperationContract]
        ResponseContext<GetRoomDataInfoModel> GetRoomData(GetRoomInfoContext context);

        [OperationContract]
        ResponseContext<CheckOutPayModel> CheckOutPay(CheckOutPayContext context);

        [OperationContract]
        ResponseContext<CheckOutPayModel> CheckOutPayByStore(CheckOutPayContext context);

        [OperationContract]
        ResponseContext<ReturnBool> FdReOrder(FdReOrderContext context);

        [OperationContract]
        ResponseContext<ReturnBool> ChangeRmStatus(ChangeRmStatusContext context);

        [OperationContract]
        ResponseContext<ReturnBool> ChangeRmStatusByStore(ChangeRmStatusContext context);

        [OperationContract]
        ResponseContext<SignNumberResuntModel> GetInvSignData(SignNumberContext context);
    }
}
