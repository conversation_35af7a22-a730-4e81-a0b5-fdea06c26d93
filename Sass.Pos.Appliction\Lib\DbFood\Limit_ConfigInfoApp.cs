﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class Limit_ConfigInfoApp : AppBase<Limit_ConfigInfo>
    {
        public List<GetGiftLimitConfigInfoModel> GetLimitConfigList(GetGiftLimitConfigInfoContext context) 
        {
            return Repository.Limit_ConfigInfo.GetLimitConfigList(context);
        }
    }
}
