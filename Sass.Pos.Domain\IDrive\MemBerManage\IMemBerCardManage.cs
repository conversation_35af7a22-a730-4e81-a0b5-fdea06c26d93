﻿
using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.MemBerManage
{
    public interface IMemBerCardManage
    {
        /// <summary>
        /// 查询会员卡状态
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        GetMemBerCardStateModel GetMemBerCardStateData(RelieveMemBerRiskControlExContext context);

        /// <summary>
        /// 查询会员卡开卡记录报表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<GetMemberCardRecordDataModel>> GetMemberCardRecordData(GetMemberCardRecordDataContext context);

        ResponseContext<ExcelMemberCardRecordDataModel> ExcelMemberCardRecordData(ExcelMemberCardRecordDataContext context);
    }
}
