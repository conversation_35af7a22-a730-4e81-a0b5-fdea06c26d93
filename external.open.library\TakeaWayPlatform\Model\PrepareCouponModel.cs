﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.Model
{
    public class PrepareCouponModel
    {
        /// <summary>
        /// 订单id
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// 抖音的核验token
        /// </summary>
        public string VerifyToken { get; set; }

        /// <summary>
        /// 卡券列表
        /// </summary>
        public List<Certificate> certificates { get; set; }
    }

    public class Certificate
    {
        /// <summary>
        /// 券码
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 商品Id
        /// </summary>
        public string ProductId { get; set; }
        /// <summary>
        /// 加密券码，抖音平台使用
        /// </summary>
        public string EncryptedCode { get; set; }
        /// <summary>
        /// 券码开始时间
        /// </summary>
        public DateTime StartTime { get; set; }
        /// <summary>
        /// 券码截止时间
        /// </summary>
        public DateTime EndTime { get; set; }

        public int Count { get; set; }

        public int TotalCount { get; set; }
        /// <summary>
        /// 原始金额
        /// </summary>
        public decimal OriginalAmount { get; set; }
        /// <summary>
        /// 商家实际售卖金额
        /// </summary>
        public decimal MerchantAmount { get; set; }

        /// <summary>
        /// 客户支付金额
        /// </summary>
        public decimal PayAmount { get; set; }

        /// <summary>
        /// 商家优惠金额
        /// </summary>
        public decimal BusDis { get; set; }

        /// <summary>
        /// 平台优惠金额
        /// </summary>
        public decimal PlatformDis { get; set; }

        /// <summary>
        /// 券名称
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 是否是次卡
        /// </summary>
        public bool IsTimeCard { get; set; }
    }
}
