﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Rms
{
    [ServiceContract]
    public interface IRmsReportService
    {
        [OperationContract]
        ResponseContext<GetTimeBookSummaryModel> GetTimeBookSummary(GetTimeBookSummaryContext context);

        [OperationContract]
        ResponseContext<List<GetBookReportModel>> GetBookList(GetTimeBookSummaryContext context);

        [OperationContract]
        ResponseContext<GetOpenedSummaryModel> GetOpenSummary(GetOpenedSummaryContext context);

        [OperationContract]
        ResponseContext<List<GetOpenedReportModel>> GetOpenedList(GetOpenedSummaryContext context);

        [OperationContract]
        ResponseContext<List<GetCustDemandBookInfoModel>> GetCustDemandBookInfo(GetCustDemandBookInfoContext context);
    }
}
