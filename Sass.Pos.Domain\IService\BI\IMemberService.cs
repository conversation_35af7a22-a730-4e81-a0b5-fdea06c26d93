﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IMemberService
    {
        /// <summary>
        /// 会员月度消费汇总
        /// </summary>
        [OperationContract]
        ResponseContext<List<MonthlyConsumptionSumModel>> MonthlyConsumptionSum(MonthlyConsumptionSumContext context);

        /// <summary>
        /// 导出会员月度消费汇总
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<ExcelMonthlyConsumptionSumModel> ExcelMonthlyConsumptionSum(ExcelMonthlyConsumptionSumContext context);
    }
}
