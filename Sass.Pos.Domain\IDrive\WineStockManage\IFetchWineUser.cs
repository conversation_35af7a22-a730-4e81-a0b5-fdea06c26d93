﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.WineStockManage
{
    public interface IFetchWineUser
    {
        ResponseContext<FetchWineUserExModel> FetchWineUser(FetchWineUserContext context);

        ResponseContext<FetchWineUserOrderModel> FetchWineUserOrder(FetchWineUserOrderContext context);
    }
}
