﻿using Saas.Pos.Application.Lib.DbFood;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class GiftAccountApp : AppBase<GiftAccount>
    {
        /// <summary>
        /// 查询场景资源分配信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetGiftAccSceDataModel> GetGiftAccSceData(GetGiftAccSceDataContext context)
        {
            return Repository.GiftAccount.GetGiftAccSceData(context);
        }

        /// <summary>
        /// 查询账户操作记录数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetGiftAccOpenterRecordDataModel> GetGiftAccOpenterRecordData(GetGiftAccOpenterRecordDataContext context)
        {
            return Repository.GiftAccount.GetGiftAccOpenterRecordData(context);
        }

        public List<GetAccountOperationRecordModel> GetAccountOperationRecord(GetAccountOperationRecordContext context)
        {
            return Repository.GiftAccount.GetAccountOperationRecord(context);
        }
    }
}
