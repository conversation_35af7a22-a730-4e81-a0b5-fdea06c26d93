﻿
using Saas.Pos.Common;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.DatResource
{
    /// <summary>
    /// 数据库资源代理
    /// </summary>
    public class DbResourceProxy : DatProxy
    {
        AppSession app = new AppSession();

        public List<rminfo> Rmlist
        {
            get
            {
                if (AppSingle.App.Middleware.DB_KV.KeyExists(GlobalConfig.Global.RedisKey.RmsInfoKey))
                {
                    return AppSingle.App.Middleware.DB_KV.GetVal<List<rminfo>>(GlobalConfig.Global.RedisKey.RmsInfoKey);
                }
                else
                {
                    var rmList = app.Rms.rminfo.IQueryable().ToList();
                    AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.RmsInfoKey, rmList);
                    return rmList;
                }
            }
        }

        public List<rtinfo> RtList
        {
            get
            {
                if (AppSingle.App.Middleware.DB_KV.KeyExists(GlobalConfig.Global.RedisKey.RtInfoKey))
                {
                    return AppSingle.App.Middleware.DB_KV.GetVal<List<rtinfo>>(GlobalConfig.Global.RedisKey.RtInfoKey);
                }
                else
                {
                    var rtList = app.Rms.rtinfo.IQueryable(w => w.IsWechatBook).ToList();
                    AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.RtInfoKey, rtList);
                    return rtList;
                }
            }
        }

        public List<shopinfo> ShopList
        {
            get
            {
                if (AppSingle.App.Middleware.DB_KV.KeyExists(GlobalConfig.Global.RedisKey.ShopInfoKey))
                {
                    return AppSingle.App.Middleware.DB_KV.GetVal<List<shopinfo>>(GlobalConfig.Global.RedisKey.ShopInfoKey);
                }
                else
                {
                    var shopList = app.Rms.shopinfo.IQueryable().ToList();
                    AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ShopInfoKey, shopList);
                    return shopList;
                }
            }
        }

        public List<timeinfo> TimeList
        {
            get
            {
                if (AppSingle.App.Middleware.DB_KV.KeyExists(GlobalConfig.Global.RedisKey.TimeInfoKey))
                {
                    return AppSingle.App.Middleware.DB_KV.GetVal<List<timeinfo>>(GlobalConfig.Global.RedisKey.TimeInfoKey);
                }
                else
                {
                    var timeList = app.Rms.timeinfo.IQueryable().ToList();
                    AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.TimeInfoKey, timeList);
                    return timeList;
                }
            }
        }

        public List<shoptimeinfo> ShopTimeList
        {
            get
            {
                if (AppSingle.App.Middleware.DB_KV.KeyExists(GlobalConfig.Global.RedisKey.ShopTimeInfoKey))
                {
                    return AppSingle.App.Middleware.DB_KV.GetVal<List<shoptimeinfo>>(GlobalConfig.Global.RedisKey.ShopTimeInfoKey);
                }
                else
                {
                    var shopTimeList = app.Rms.shoptimeinfo.IQueryable().ToList();
                    AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ShopTimeInfoKey, shopTimeList);
                    return shopTimeList;
                }
            }
        }

        public List<contypeinfo> ContypeList
        {
            get
            {
                if (AppSingle.App.Middleware.DB_KV.KeyExists(GlobalConfig.Global.RedisKey.ContypeKey))
                {
                    return AppSingle.App.Middleware.DB_KV.GetVal<List<contypeinfo>>(GlobalConfig.Global.RedisKey.ContypeKey);
                }
                else
                {
                    var contypeList = app.Rms.contypeinfo.IQueryable().ToList();
                    AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ContypeKey, contypeList);
                    return contypeList;
                }
            }
        }

        public List<Shop_BookRtInfo> ShopBookRtList
        {
            get
            {
                if (AppSingle.App.Middleware.DB_KV.KeyExists(GlobalConfig.Global.RedisKey.ShopBookRtKey))
                {
                    return AppSingle.App.Middleware.DB_KV.GetVal<List<Shop_BookRtInfo>>(GlobalConfig.Global.RedisKey.ShopBookRtKey);
                }
                else
                {
                    var shopBookRtList = app.SaasPos.Shop_BookRtInfo.IQueryable(w => w.IsEnable).ToList();
                    AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ShopBookRtKey, shopBookRtList);
                    return shopBookRtList;
                }
            }
        }

        public DbResourceProxy()
        {

        }

        public override void Init()
        {
            ///第一次初始化的代码，通过缓存获取Redis
            //this.Refresh();
        }

        public override void Refresh()
        {
            var rmlist = app.Rms.rminfo.IQueryable().ToList();
            var timeList = app.Rms.timeinfo.IQueryable().ToList();
            var shopList = app.Rms.shopinfo.IQueryable().ToList();
            var contypeList = app.Rms.contypeinfo.IQueryable().ToList();
            var shopTimeList = app.Rms.shoptimeinfo.IQueryable().ToList();
            var rtList = app.Rms.rtinfo.IQueryable(w => w.IsWechatBook).ToList();
            var shopBookRtList = app.SaasPos.Shop_BookRtInfo.IQueryable(w => w.IsEnable).ToList();

            AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.RmsInfoKey, rmlist);
            AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.RtInfoKey, rtList);
            AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ShopInfoKey, shopList);
            AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.TimeInfoKey, timeList);
            AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ContypeKey, contypeList);
            AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ShopTimeInfoKey, shopTimeList);
            AppSingle.App.Middleware.DB_KV.SetVal(GlobalConfig.Global.RedisKey.ShopBookRtKey, shopBookRtList);
        }

        public override void Dispose()
        {

        }

        public override void RunningEvent()
        {
            throw new NotImplementedException();
        }
    }
}
