﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Rms
{
    [ServiceContract]
    public interface IRmsCustomerService
    {
        [OperationContract]
        ResponseContext<CustInfo> GetCustInfo(GetCustInfoContext context);

        [OperationContract]
        ResponseContext<List<custbehainfo>> GetCustbehaInfoList(GetCustbehaInfoContext context);
    }
}
