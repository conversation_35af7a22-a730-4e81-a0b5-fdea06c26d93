﻿using Saas.Pos.Model.Collaboration.Context;
using Saas.Pos.Model.Collaboration.Model;
using Saas.Pos.Model.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Energy_Metering_RecordApp : AppBase<Energy_Metering_Record>
    {
        public List<GetRecordItemListModel> GetRecordItemList(GetRecordItemListContext context)
        {
            return Repository.Energy_Metering_Record.GetRecordItemList(context);
        }
    }
}
