﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IRepository.Rms
{
    public partial interface IshoptimeinfoRepository : IRepositoryBase<shoptimeinfo>
    {
        List<KeyValuePair<string, string>> GetTimeNoByShop(int shopId);
    }
}
