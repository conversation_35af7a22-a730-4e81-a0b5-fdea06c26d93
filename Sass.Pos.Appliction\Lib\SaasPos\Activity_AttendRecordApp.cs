﻿using Saas.Pos.Application.Lib.SaasPos;
using Saas.Pos.Model.Enum;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Activity_AttendRecordApp : AppBase<Activity_AttendRecord>
    {
        /// <summary>
        /// 查询活动赠送记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetActivityDataModel> GetActivityData(GetActivityDataContext context)
        {
            var list = Repository.Activity_AttendRecord.GetActivityData(context);

            var data = list.Select(i => new GetActivityDataModel()
            {
                ActivityResults = i.ActivityResults,
                CreateTime = i.CreateTime,
                Id = i.Id,
                IsCouponUser = i.UseId > 0,
                IsRevoke = i.IsRevoke,
                OpenId = i.OpenId,
                RevokeTime = i.RevokeTime,
                Type = i.Type,
                TypeName = Enum.GetName(typeof(ActivityTypeEnum), (i.Type)),
                UserCouponId = i.UserCouponId,
                Voucher = i.Voucher
            }).ToList();

            return data;
        }
    }
}
