﻿using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    /// <summary>
    /// 房间代理基类
    /// </summary>
    public abstract class RoomBaseProxy : BookDateBaseProxy
    {
        AppSession app = new AppSession();

        /// <summary>
        /// 自动匹配房型
        /// </summary>
        /// <returns></returns>
        public virtual string AutoMatchRtNo(int number)
        {
            //可用房型
            var usableRtNos = AppSingle.App.Storage.BookingDat.RoomDataStorage.Where(w => w.ShopId == Proxy.Context.ShopId && w.NumberMax > number).OrderBy(w => w.NumberMax).Select(x => x.RtNo).ToList();

            var rooms = app.Rms.rminfo.IQueryable(w => w.ShopId == Proxy.Context.ShopId && usableRtNos.Contains(w.RtNo) && w.RmsStatus == "W").GroupBy(w => w.RtNo)
                .Select(x => new
                {
                    RtNo = x.Key,
                    Count = x.Count(),
                }).ToList();

            string rtNo = string.Empty;
            foreach (var item in usableRtNos)
            {
                var room = rooms.FirstOrDefault(w => w.RtNo == item);
                if (room != null)
                {
                    if (room.Count > 0)
                    {
                        rtNo = room.RtNo;
                        break;
                    }
                }
            }

            if (string.IsNullOrEmpty(rtNo))
                throw new ExMessage("暂无剩余房间！");

            return rtNo;
        }

        /// <summary>
        /// 获取门店所有房间
        /// </summary>
        /// <returns></returns>
        public virtual List<OpeningModel> GetStoreRoom()
        {
            return app.Rms.rminfo.GetStoreRoom(Proxy.Context.ShopId);
        }
    }
}
