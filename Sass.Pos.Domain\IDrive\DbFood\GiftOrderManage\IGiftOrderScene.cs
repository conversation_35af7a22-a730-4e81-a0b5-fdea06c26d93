﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFood.GiftOrderManage
{
    /// <summary>
    /// 赠送下单场景赠送项目管理
    /// </summary>
    public interface IGiftOrderScene
    {
        ResponseContext<RespPaginationModel<GetGiftSceneDataModel>> GetGiftSceneData(GetGiftSceneDataContext context);

        ResponseContext<RespPaginationModel<GetGiftSceneDataModel>> GetGiftSceneDataByStore(GetGiftSceneDataContext context);

        ResponseContext<List<GetSceneSelectDataModel>> GetSceneSelectData(GetSceneSelectDataContext context);
        ResponseContext<List<GetSceneSelectDataModel>> GetSceneSelectDataByStore(GetSceneSelectDataContext context);

        ResponseContext<EditGiftSceneDataModel> EditGiftSceneData(EditGiftSceneDataContext context);
        ResponseContext<EditGiftSceneDataModel> EditGiftSceneDataByStore(EditGiftSceneDataContext context);

        ResponseContext<RespPaginationModel<GetGiftAccSceDataModel>> GetGiftAccSceData(GetGiftAccSceDataContext context);
        ResponseContext<RespPaginationModel<GetGiftAccSceDataModel>> GetGiftAccSceDataByStore(GetGiftAccSceDataContext context);

        ResponseContext<EditAccSceDataModel> EditAccSceDataByStore(EditAccSceDataContext context);

        ResponseContext<DeleteSceneRoleBindDataModel> DeleteSceneRoleBindDataByStore(DeleteSceneRoleBindDataContext context);

        ResponseContext<DistributionGiftAccSceDataModel> DistributionGiftAccSceData(DistributionGiftAccSceDataContext context);
        ResponseContext<DistributionGiftAccSceDataModel> DistributionGiftAccSceDataByStore(DistributionGiftAccSceDataContext context);

        ResponseContext<BindIngSceRoleDataModel> BindIngSceRoleData(BindIngSceRoleDataContext context);
        ResponseContext<BindIngSceRoleDataModel> BindIngSceRoleDataByStore(BindIngSceRoleDataContext context);

        ResponseContext<List<GetSceneTreeDataModel>> GetSceneTreeData(GetSceneTreeDataContext context);
        ResponseContext<List<GetSceneTreeDataModel>> GetSceneTreeDataByStore(GetSceneTreeDataContext context);
        /// <summary>
        /// 查询当前用户绑定角色下的所有可出现的场景信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<GetUserScenceData>> GetUserSceData(GetUserScenceDataContext context);
        ResponseContext<List<GetUserScenceDataModel>> GetUserSceDataByStore(GetUserScenceDataContext context);

        /// <summary>
        /// 获取当前用户绑定角色指定场景下的所有商品数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<GetUserScenceFdNoModel>> GetUserSceFdNoData(GetUserSceFdNoDataContext context);
        ResponseContext<List<GetUserScenceFdNoModel>> GetUserSceFdNoDataByStore(GetUserSceFdNoDataContext context);

        /// <summary>
        /// 保存角色场景商品信息配置
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<SaveSceneRoleConfigModel> SaveSceneRoleConfig(SaveSceneRoleConfigContext context);
        ResponseContext<SaveSceneRoleConfigModel> SaveSceneRoleConfigByStore(SaveSceneRoleConfigContext context);

        /// <summary>
        /// 删除角色场景商品信息配置
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<SaveSceneRoleConfigModel> DelSceneRoleConfig(DeleteSceneRoleConfigContext context);
        ResponseContext<SaveSceneRoleConfigModel> DelSceneRoleConfigByStore(DeleteSceneRoleConfigContext context);

        /// <summary>
        /// 查看角色场景商品信息配置
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<GetSceneRoleConfigModel> GetSceneRoleConfig(GetSceneRoleConfigContext context);
        ResponseContext<GetSceneRoleConfigModel> GetSceneRoleConfigByStore(GetSceneRoleConfigContext context);

        /// <summary>
        /// 查询场景角色绑定信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetSceneRoleBindDataModel>> GetSceneRoleBindData(GetSceneRoleBindDataContext context);
        ResponseContext<RespPaginationModel<GetSceneRoleBindDataModel>> GetSceneRoleBindDataByStore(GetSceneRoleBindDataContext context);

        /// <summary>
        /// 根据场景角色绑定ID查询下拉框信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<List<GetSceneRoleBindDataModel>> GetSceneRoleBindByIdData(GetSceneRoleBindByIdDataContext context);
        ResponseContext<List<GetSceneRoleBindDataModel>> GetSceneRoleBindByIdDataByStore(GetSceneRoleBindByIdDataContext context);

        /// <summary>
        /// 查询账户场景绑定信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        GiftAccountSceneInfo GetAccountScene(GetAccountSceneContext context);

        ResponseContext<EditAccSceDataModel> EditAccSceData(EditAccSceDataContext context);

        ResponseContext<DeleteSceneRoleBindDataModel> DeleteSceneRoleBindData(DeleteSceneRoleBindDataContext context);

        /// <summary>
        /// 查询场景角色配置信息
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        ResponseContext<RespPaginationModel<GetSceneRoleConfigExModel>> GetSceneRoleConfigData(GetSceneRoleConfigDataContext context);
        ResponseContext<RespPaginationModel<GetSceneRoleConfigExModel>> GetSceneRoleConfigDataByStore(GetSceneRoleConfigDataContext context);
    }
}
