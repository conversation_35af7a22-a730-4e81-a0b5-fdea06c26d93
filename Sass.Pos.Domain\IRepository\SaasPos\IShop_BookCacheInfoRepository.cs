﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IShop_BookCacheInfoRepository : IRepositoryBase<Shop_BookCacheInfo>
    {
        List<ShopBookCacheInfoModel> GetUserBookData(GetShopBookCacheInfoContext context);

        List<Shop_BookCacheInfo> GetExpireData(DateTime date);

        List<GetConsumeListModel> GetConsumeList(GetConsumePageingContext context);
    }
}
