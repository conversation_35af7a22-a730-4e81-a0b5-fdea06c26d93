﻿using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Appliction.Lib.Bar;
using Saas.Pos.Common.Log;
using Saas.Pos.Domain.IDrive.WineStockManage;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    public class WineStockReportDrive : WineStorkManageSubDriveBase<WineStockManageDriveBase>, IWineStockReport
    {
        public WineStockReportDrive(WineStockManageDriveBase imi, AppSession app)
            : base(imi, app)
        {


        }

        /// <summary>
        /// 存酒报表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<WineStorkReportModel>> GetWineStorkReportData(WineStorkReportContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context == null && context.StartTime == null && context.EndTime == null)
                        throw new ExMessage("参数不能为空!");

                    WineStockReportApp w = new WineStockReportApp();
                    var reportData = w.GetWineStorkReportData(context);

                    return reportData;
                }
                catch (Exception ex)
                {
                    LogHelper.Info("存酒报表\n参数" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                    throw new ExMessage(ex.Message);
                }

            });
        }
    }
}
