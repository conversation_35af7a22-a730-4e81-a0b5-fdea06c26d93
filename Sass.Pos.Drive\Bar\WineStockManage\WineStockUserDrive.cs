﻿using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Application.Lib.Bar;
using Saas.Pos.Appliction.Lib.Bar;
using Saas.Pos.Common;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.MiddlewareProxy;
using Saas.Pos.Common.Tools;
using Saas.Pos.Domain.IDrive.WineStockManage;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using Saas.Pos.Model.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    /// <summary>
    /// 用户存酒数据
    /// </summary>
    public class WineStockUserDrive : WineStorkManageSubDriveBase<WineStockManageDriveBase>, IWineStockUser
    {
        public WineStockUserDrive(WineStockManageDriveBase imi, AppSession app) : base(imi, app)
        {

        }
        /// <summary>
        /// 查询用户存酒数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<UserCustWineData>> GetUserDrinksData(UserCustDataContext context)
        {
            try
            {
                return ActionFun.Run(context, () =>
                {
                    if (string.IsNullOrEmpty(context.CustTel) && string.IsNullOrEmpty(context.MsgPassWord) && string.IsNullOrEmpty(context.OpenId))
                        throw new ExMessage("查询条件不能为空!");

                    WineStockApp w = new WineStockApp();

                    var data = w.GetUserCustData(context);

                    return data;
                });
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "用户存酒数据-查询用户存酒明细数据\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                throw new ExMessage("查询用户存酒数据失败!" + ex.Message);
            }
        }

        /// <summary>
        /// 查询商品类型信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetFdTypeDatasModel> GetFdTypeData(GetFdTypeDataContext context)
        {
            try
            {
                return ActionFun.Run(context, () =>
                {
                    if (context.ShopId <= 0)
                        throw new ExMessage("门店ID不能为空!");
                    WineStockApp w = new WineStockApp();

                    var data = w.GetFdTypeDatas(context);
                    var result = new GetFdTypeDatasModel()
                    {
                        FtTypeData = data,
                        UnitData = EnumHelper.EnumToDictionary<WineUserUnitEnum>()
                    };

                    return result;
                });
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "用户存酒数据-查询商品类型信息\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                throw new ExMessage("查询商品类型信息!" + ex.Message);
            }
        }

        /// <summary>
        /// 根据商品类型查询商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<GetFdDataModel>> GetFdData(GetFdDataContext context)
        {
            try
            {
                return ActionFun.Run(context, () =>
                {
                    if (string.IsNullOrEmpty(context.FtNo) || context.ShopId <= 0)
                        throw new ExMessage("参数不能为空!");
                    WineStockApp w = new WineStockApp();

                    var data = w.GetFdDatas(context);

                    return data;
                });
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "用户存酒数据-查询商品信息\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                throw new ExMessage("查询商品信息!" + ex.Message);
            }
        }

        /// <summary>
        /// 员工查询存酒信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetWineDataByBarIdModel> GetWineDataByBarId(GetWineDataByBarIdContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.NumberOrPwd))
                        throw new ExMessage("查询参数不能为空!");

                    if (context.OrderShopId <= 1)
                        throw new ExMessage("门店不能为空!");

                    //根据员工工号查询可以过期多少天的数字
                    var exData = app.MIMS.FdUserWeChatJurisdiction
                    .FindEntity(i => i.UserId == context.UserData.UserId &&
                    (i.ShopId == context.UserData.ShopId || i.ShopId <= 1));//权限信息
                    var exDays = 0;
                    var exQx = false;//是否没有权限
                    var resultBool = false;//是否需要发送短信
                    if (exData != null && exData.ExDays > 0)
                        exDays = exData.ExDays;
                    else//没有权限的人默认可以查询过期十五天的数据
                    {
                        exDays = 15;
                        exQx = true;

                        //手机号验证授权
                        resultBool = PhoneNumberAuth(context);

                        if (resultBool)
                            return new GetWineDataByBarIdModel() { IsAuthCode = resultBool };
                    }

                    WineStockApp w = new WineStockApp();

                    var data = w.GetWineDataByBarId(context, exDays);

                    //没有权限的人不展示序号、手机号
                    if (exQx)
                        data = data.Select(i => { i.MsgInfo.MsgPassword = "-"; i.MsgInfo.CustTel = Regex.Replace(i.MsgInfo.CustTel, @"(\d{3})\d{4}(\d{4})", @"$1****$2"); /*i.MsgInfo.IsAccredit = false*/; ; return i; }).ToList();

                    var resp = new GetWineDataByBarIdModel()
                    {
                        WineData = RespPaginationModel<GetWineDataByBarIdModelEx>.Package(context.Pagination, data),
                        IsAuthCode = resultBool
                    };

                    return resp;

                    //return RespPaginationModel<GetWineDataByBarIdModel>.Package(context.Pagination, data);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "用户存酒数据-员工查询存酒信息\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// vue管理平台查询用户存酒数据(用户信息)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetWineMainDataOnVueModel>> GetWineMainDataOnVue(GetUserWineDataOnVueContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();

                    var data = w.GetWineMainDataOnVue(context);

                    return RespPaginationModel<GetWineMainDataOnVueModel>.Package(context.Pagination, data);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "用户存酒数据-vue管理平台查询用户存酒数据\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                    throw new ExMessage("查询存酒信息失败!" + ex.Message);
                }
            });
        }

        /// <summary>
        /// vue管理平台查询用户存酒数据(存酒明细列表)
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<GetWineDetailDataOnVueModel>> GetWineDetailDataOnVue(GetWineDetailDataOnVueContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();

                    var data = w.GetWineDetailDataOnVue(context);

                    return data;
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "用户存酒数据-vue管理平台查询用户存酒数据\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                    throw new ExMessage("查询存酒明细信息失败!" + ex.Message);
                }
            });
        }

        /// <summary>
        /// 手机号验证授权
        /// </summary>
        /// <param name="context"></param>
        public bool PhoneNumberAuth(GetWineDataByBarIdContext context)
        {
            //发送短信格式
            var rep = new PhoneSendMessageContext();
            try
            {
                //redis缓存
                MiddlewareProxy redis = new MiddlewareProxy();

                var resultBool = false;

                //判断查询条件是否为手机号格式
                if (Regex.IsMatch(context.NumberOrPwd, @"^1[3-9]\d{9}$"))
                {
                    //手机号(custTel用于辨认)
                    var custTel = context.NumberOrPwd;

                    //当前时间
                    var nowDate = DateTime.Now;

                    Random r = new Random();
                    //六位数字验证码
                    int authNumber = r.Next(100000, 999999);

                    //发送短信格式
                    rep.ReqType = 1;
                    rep.PhoneNumber = custTel;
                    rep.No = 0;
                    if (context.OrderShopId == 11)
                        rep.No = 1;
                    rep.Message = string.Format(@"您正在进行授权取酒操作，验证码为 {0}。请将此验证码在10分钟内告知服务人员以完成取酒，逾时失效。如非您本人取酒，可忽略此信息。", authNumber);

                    //查询redis里有没有授权信息，有没有授权
                    var wineAuth = redis.DB_KV.GetVal<WineStockAccreditDataModel>($@"WineStork:{context.NumberOrPwd}");

                    //授权信息
                    var accredData = new WineStockAccreditDataModel()
                    {
                        AuthCode = authNumber.ToString(),
                        CustTel = custTel,
                        UserId = context.UserData.UserId
                    };

                    //如果没有授权信息需要发送验证码
                    if (wineAuth == null || string.IsNullOrEmpty(wineAuth.CustTel))
                    {
                        resultBool = true;
                        if (MessageSendHelper.PhoneSendMessage(rep))
                        {
                            accredData.ActhCodeEndTime = nowDate.AddMinutes(10);
                            redis.DB_KV.SetVal($@"WineStork:{custTel}", accredData, nowDate.AddMinutes(10));
                        }
                        else
                        {
                            LogHelper.Error("发送短信失败\n参数:" + JsonConvert.SerializeObject(context) + "\n原因:短信未发送成功!\n发送信息记录:" + JsonConvert.SerializeObject(rep));
                            throw new ExMessage("未授权,未发送验证码!");
                        }
                    }
                    else
                    {
                        //如果已授权但是授权时间已经过了需要发送验证码
                        if (wineAuth.EndAuthTime < nowDate)
                        {
                            resultBool = true;
                            if (MessageSendHelper.PhoneSendMessage(rep))
                            {
                                accredData.ActhCodeEndTime = nowDate.AddMinutes(10);
                                redis.DB_KV.SetVal($@"WineStork:{custTel}", accredData, nowDate.AddMinutes(10));
                            }
                            else
                            {
                                LogHelper.Error("发送短信失败\n参数:" + JsonConvert.SerializeObject(context) + "\n原因:短信未发送成功!\n发送信息记录:" + JsonConvert.SerializeObject(rep));
                                throw new ExMessage("未授权,未发送验证码!");
                            }
                        }
                    }
                }
                return resultBool;
            }
            catch (Exception ex)
            {
                LogHelper.Error("发送短信失败\n参数:" + JsonConvert.SerializeObject(context) + "\n原因:短信未发送成功!\n发送信息记录:" + JsonConvert.SerializeObject(rep));
                throw new ExMessage("授权发送验证码失败!" + ex.Message);
            }
        }

        /// <summary>
        /// 员工查询存酒数据手机号授权
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<WineStorkAuthModel> WineStorkAuth(WineStorkAuthContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.CustTel))
                        throw new ExMessage("电话号不能为空!");
                    if (string.IsNullOrEmpty(context.AuthCode))
                        throw new ExMessage("验证码不能为空!");

                    //redis缓存
                    MiddlewareProxy redis = new MiddlewareProxy();

                    //查询redis里验证码信息
                    var wineAuth = redis.DB_KV.GetVal<WineStockAccreditDataModel>($@"WineStork:{context.CustTel}");

                    //判断有无成功发送验证码
                    if (wineAuth == null || string.IsNullOrEmpty(wineAuth.CustTel))
                        throw new ExMessage("验证码发送失败!");

                    var nowDate = DateTime.Now;

                    //判断验证码过期时间
                    if (wineAuth.ActhCodeEndTime < nowDate)
                        throw new ExMessage("验证码已超时,请重新获取!");

                    //判断验证码是否正确
                    if (wineAuth.AuthCode != context.AuthCode)
                        throw new ExMessage("验证码错误!");
                    else
                    {
                        //更新redis中的授权时间
                        wineAuth.EndAuthTime = nowDate.AddMinutes(10);
                        redis.DB_KV.SetVal($@"WineStork:{context.CustTel}", wineAuth, nowDate.AddMinutes(10));
                    }

                    return new WineStorkAuthModel();
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "用户存酒数据-手机号查询授权\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                    throw new ExMessage("员工查询存酒数据手机号授权失败!" + ex.Message);
                }
            });
        }
    }
}
