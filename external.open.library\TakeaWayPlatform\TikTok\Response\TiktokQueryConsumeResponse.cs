﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace external.open.library.TakeaWayPlatform.Tiktok.Response
{
    public class TiktokQueryConsumeResponse
    {
        /// <summary>
        /// 错误码，0为成功
        /// </summary>
        public int error_code { get; set; }
        /// <summary>
        /// 错误码描述
        /// </summary>
        public string description { get; set; }
        /// <summary>
        /// 一次验券的标识, 在验券接口传入
        /// </summary>
        public string verify_token { get; set; }
        /// <summary>
        /// 抖音订单id
        /// </summary>
        public string order_id { get; set; }
        /// <summary>
        /// 可用团购券列表
        /// </summary>
        public List<Certificates> certificates { get; set; }
    }

    public class Certificates
    {
        /// <summary>
        /// 券id
        /// </summary>
        public long certificate_id { get; set; }
        /// <summary>
        /// 加密券码, 在验券接口传入
        /// </summary>
        public string encrypted_code { get; set; }
        /// <summary>
        /// 券码有效期，截至时间，时间戳，单位秒
        /// </summary>
        public long expire_time { get; set; }
        /// <summary>
        /// 券码有效期，开始时间，时间戳，单位秒
        /// </summary>
        public long start_time { get; set; }

        public TimeCard time_card { get; set; }

        public CertificateAmount amount { get; set; }

        public Sku sku { get; set; }
    }

    public class TimeCard
    {
        public int times_count { get; set; }

        public int times_used { get; set; }

        public List<SerialAmount> serial_amount_list { get; set; }
    }

    public class SerialAmount
    {
        public int serial_numb { get; set; }

        public Amount amount { get; set; }
    }

    public class Amount
    {
        public int original_amount { get; set; }
        public int pay_amount { get; set; }
        public int merchant_ticket_amount { get; set; }
        public int list_market_amount { get; set; }
        public int platform_discount_amount { get; set; }
        public int payment_discount_amount { get; set; }
        public int coupon_pay_amount { get; set; }
    }

    public class CertificateAmount
    {
        public int original_amount { get; set; }
        public int list_market_amount { get; set; }
        public int pay_amount { get; set; }
        public int merchant_ticket_amount { get; set; }
        public int payment_discount_amount { get; set; }
        public int platform_discount_amount { get; set; }
        public int coupon_pay_amount { get; set; }
    }

    public class Sku
    {
        public string sku_id { get; set; }
        public string title { get; set; }
        public int groupon_type { get; set; }
        public long market_price { get; set; }
        public long sold_start_time { get; set; }
        public string third_sku_id { get; set; }
        public string account_id { get; set; }
    }
}
