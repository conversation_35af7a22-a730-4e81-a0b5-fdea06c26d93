﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ include file="EF.Utility.CS.ttinclude"#>
<#@ output extension=".cs" #>
<#
CodeGenerationTools code =new CodeGenerationTools(this);
MetadataLoader Loader =new MetadataLoader(this);
CodeRegion region=new CodeRegion(this,1);
MetadataTools ef=new MetadataTools(this);
string inputFile=@"E:\CompanyFile\Codes\ServiceApplication\Sass.Pos\Sass.Pos\Sass.Pos.Model\DbFood\DbFood_DB.edmx";
EdmItemCollection Itemcollection = Loader.CreateEdmItemCollection(inputFile);
string namespaceName=code.VsNamespaceSuggestion();
EntityFrameworkTemplateFileManager fileManager = EntityFrameworkTemplateFileManager.Create(this);
#>

using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
<#
foreach(EntityType entity in Itemcollection.GetItems<EntityType>().OrderBy(e=>e.Name))
{
#>
 public partial interface I<#=entity.Name#>Repository : IRepositoryBase<<#=entity.Name#>> {}
  

<#}#>
}
