﻿using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Model;

namespace Saas.Pos.Domain.IDrive
{
    public interface IShopBookGoodRtInfo
    {
        Shop_BookRtInfo GetShopRtInfo(GetShopBookGoodRtInfoContext context);

        ResponseContext<RespPaginationModel<GetRtInfoDataModel>> GetRtInfoData(GetRtInfoDataContext context);

        ResponseContext<DeleteRtInfoDataModel> DeleteRtInfoData(DeleteRtInfoDataContext context);

        ResponseContext<SaveRtInfoDataModel> SaveRtInfoData(SaveRtInfoDataContext context);
    }
}
