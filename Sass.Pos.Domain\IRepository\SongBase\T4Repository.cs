﻿

using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SongBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SongBase
{
 public partial interface IHotsong_tjRepository : IRepositoryBase<Hotsong_tj> {}
  

 public partial interface ISongCollectionInfoRepository : IRepositoryBase<SongCollectionInfo> {}
  

 public partial interface ISongPutRepository : IRepositoryBase<SongPut> {}
  

 public partial interface ISongScanRepository : IRepositoryBase<SongScan> {}
  

 public partial interface ISongScanBillRepository : IRepositoryBase<SongScanBill> {}
  

 public partial interface ISongSizeRepository : IRepositoryBase<SongSize> {}
  

}
