﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IDrive.DbFood.Bill
{
    public interface IBillPrepaymentRecordDrive
    {
        ResponseContext<GetPrepaymentRecordModel> GetPrepaymentRecordByStory(GetPrepaymentRecordContext context);

        ResponseContext<GetPrepaymentRecordModel> GetPrepaymentRecord(GetPrepaymentRecordContext context);

        ResponseContext<ReturnBool> SavePreRecordByStore(SavePreRecordContext context);

        ResponseContext<ReturnBool> SavePreRecord(SavePreRecordContext context);
    }
}
