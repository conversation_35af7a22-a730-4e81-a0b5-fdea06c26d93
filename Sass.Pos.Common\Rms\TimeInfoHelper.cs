﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Rms
{
    public class TimeInfoHelper
    {
        /// <summary>
        /// 检查时段是否过了指定时间
        /// </summary>
        /// <returns>true:已过当前时间，false：未过当前时间</returns>
        public static bool CheckTime(string timeName, DateTime time)
        {
            var timeLine = timeName.Split('-');
            if (timeLine.Length == 1 || timeLine[1].ToLower() == "end")
                return false;

            var endTimeStr = time.ToString("yyyy-MM-dd") + " " + timeLine[1];
            var endTime = DateTime.Parse(endTimeStr);
            if (time > endTime)
                return true;
            else
                return false;
        }

        /// <summary>
        /// 将字符串转换成当时日期的时间点
        /// </summary>
        /// <param name="date">日期</param>
        /// <param name="timeSepart">时间分隔</param>
        /// <returns>如果时间间隔为空，默认返回date的一整天到第二天早上六点</returns>
        public static Tuple<DateTime, DateTime> TimeConvert(DateTime date, string timeSepart)
        {
            var endTime = date.Date.AddDays(1).AddHours(6);
            if (string.IsNullOrEmpty(timeSepart))
                return new Tuple<DateTime, DateTime>(date.Date, endTime);

            var timeline = timeSepart.Split('-');
            if (timeline.Length > 0)
            {
                var fullTime = date.Date.ToString("yyyy-MM-dd");
                fullTime += " " + timeline[0];

                date = DateTime.Parse(fullTime);
            }
            if (timeline.Length > 1)
            {
                if (timeline[1].ToLower() != "end")
                {
                    var fullTime = date.Date.ToString("yyyy-MM-dd");
                    if (timeline[1] != "24:00")
                    {
                        fullTime += " " + timeline[1];
                        endTime = DateTime.Parse(fullTime);
                    }
                    else
                        endTime = endTime.Date;
                }
            }

            return new Tuple<DateTime, DateTime>(date, endTime);
        }
    }
}
