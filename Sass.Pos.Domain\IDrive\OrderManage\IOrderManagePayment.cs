﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    //订单支付管理
    public interface IOrderManagePayment
    {

        /// <summary>
        /// 创建订单添加支付信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        int CreateOrderPaymentData(OrderPaymentDataContext context);

        /// <summary>
        /// 调用支付
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<CreateOrderExModel> InvokePayOrder(CreateOrderInfoExContext context);

        /// <summary>
        /// 计算商品支付金额
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        TotalMoneyDataModel GetTotalMoneyData(List<GetTotalCash> context);
    }
}
