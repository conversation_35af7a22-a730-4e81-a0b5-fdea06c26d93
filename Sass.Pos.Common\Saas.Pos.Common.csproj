﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{53316D66-D568-4B6C-9445-8C95E2C8AF83}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Saas.Pos.Common</RootNamespace>
    <AssemblyName>Saas.Pos.Common</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Castle.Core">
      <HintPath>..\Sass.Pos.Model\Packages\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="ComponentApplicationServiceInterface">
      <HintPath>..\Sass.Pos.Model\Packages\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="ComponentCore, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Sass.Pos.Model\Packages\ComponentCore.dll</HintPath>
    </Reference>
    <Reference Include="CoreDll">
      <HintPath>..\Sass.Pos.Model\Packages\CoreDll.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=1.39.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Sass.Pos.Model\Packages\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=4.4.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Sass.Pos.Model\Packages\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="JWT">
      <HintPath>..\Sass.Pos.Model\Packages\JWT.dll</HintPath>
    </Reference>
    <Reference Include="MiddlewareLibrary">
      <HintPath>..\Sass.Pos.Model\Packages\MiddlewareLibrary.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=6.5.7.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Sass.Pos.Model\Packages\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="NET_ADO, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Sass.Pos.Model\Packages\NET_ADO.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Sass.Pos.Model\Packages\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="RabbitMQ.Client">
      <HintPath>..\Sass.Pos.Model\Packages\RabbitMQ.Client.dll</HintPath>
    </Reference>
    <Reference Include="Select.HtmlToPdf">
      <HintPath>..\Sass.Pos.Model\Packages\Select.HtmlToPdf.dll</HintPath>
    </Reference>
    <Reference Include="SERVICE.PROXY">
      <HintPath>..\Sass.Pos.Model\Packages\SERVICE.PROXY.dll</HintPath>
    </Reference>
    <Reference Include="Spring.Core">
      <HintPath>..\Sass.Pos.Model\Packages\Spring.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Attributes\Filter\AuthorizationAttribute.cs" />
    <Compile Include="Attributes\ValidationFilter\CompareAttribute.cs" />
    <Compile Include="Attributes\ValidationFilter\Context\ValidationContext.cs" />
    <Compile Include="Attributes\ValidationFilter\RangeAttribute.cs" />
    <Compile Include="Attributes\ValidationFilter\RegularExpressionAttribute.cs" />
    <Compile Include="Attributes\ValidationFilter\RequiredAttribute.cs" />
    <Compile Include="Attributes\ValidationFilter\StringLengthAttribute.cs" />
    <Compile Include="Attributes\ValidationFilter\ValidationFilterAttribute.cs" />
    <Compile Include="Attributes\Filter\FilterAttribute.cs" />
    <Compile Include="Cache\CustormMemoryCache.cs" />
    <Compile Include="Exceptions\ExceptionHandler.cs" />
    <Compile Include="Exceptions\ClientException.cs" />
    <Compile Include="Extend\TypeConvertExtend.cs" />
    <Compile Include="Factory\FilterFactory.cs" />
    <Compile Include="Attributes\Filter\LogFilterAttribute.cs" />
    <Compile Include="Attributes\Filter\GeneralInterceptor.cs" />
    <Compile Include="Factory\AppFactory.cs" />
    <Compile Include="Global\Configs\Bar\WineStorkManageConfig.cs" />
    <Compile Include="Global\Configs\Comm\CommConfig.cs" />
    <Compile Include="Global\Configs\Comm\RedisKeyConfig.cs" />
    <Compile Include="Global\Configs\DbFood\OrderConfig.cs" />
    <Compile Include="Global\Configs\DbFood\RoomConfig.cs" />
    <Compile Include="Global\Configs\GlobalConfig.cs" />
    <Compile Include="Global\Configs\Rms\BookConfig.cs" />
    <Compile Include="Global\Configs\SaasPos\OrderManageConfig.cs" />
    <Compile Include="Global\Configs\SaasPos\ShopBookConfig.cs" />
    <Compile Include="Global\Configs\SaasPos\ShopConfig.cs" />
    <Compile Include="GrouponBase\GrouponCodeHelper.cs" />
    <Compile Include="Log\LogHelper.cs" />
    <Compile Include="MemberInfo\Benefits\AccountBenefits.cs" />
    <Compile Include="MemberInfo\Benefits\BenefitsBase.cs" />
    <Compile Include="MemberInfo\Benefits\CouponBenefits.cs" />
    <Compile Include="MemberInfo\Context\GetMemberConfigContext.cs" />
    <Compile Include="MemberInfo\Model\MemberBenefitModel.cs" />
    <Compile Include="MemberInfo\Context\BenefitConfigContext.cs" />
    <Compile Include="MemberInfo\MemberInfoService.cs" />
    <Compile Include="MiddlewareProxy\MiddlewareProxy.cs" />
    <Compile Include="MiddlewareProxy\RabbitMq\MqBase.cs" />
    <Compile Include="MiddlewareProxy\RabbitMq\RabbitMQ.cs" />
    <Compile Include="Objects\IOC.cs" />
    <Compile Include="Tools\EncryptionHelper.cs" />
    <Compile Include="Tools\PageConfigHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Rms\BookingHelper.cs" />
    <Compile Include="Rms\NumberPoolHelper.cs" />
    <Compile Include="Rms\NumberPool\BookNumberModel.cs" />
    <Compile Include="Rms\NumberPool\NumberPoolDisOrderService.cs" />
    <Compile Include="Rms\NumberPool\NumberPoolOrderService.cs" />
    <Compile Include="Rms\NumberPool\NumberPoolOrderServiceCopy.cs" />
    <Compile Include="Rms\NumberPool\NumberPoolServiceBase.cs" />
    <Compile Include="Rms\NumberPool\SystemConfigModel.cs" />
    <Compile Include="Rms\TimeInfoHelper.cs" />
    <Compile Include="ServiceClient\ServiceClientBase.cs" />
    <Compile Include="Tools\AssemblyHelper.cs" />
    <Compile Include="Tools\ConvertToPdfHelper.cs" />
    <Compile Include="Tools\DateTimeHelper.cs" />
    <Compile Include="Tools\EntityConversion.cs" />
    <Compile Include="Tools\EnumHelper.cs" />
    <Compile Include="Tools\FileHelper.cs" />
    <Compile Include="Tools\GenerateHelper.cs" />
    <Compile Include="Tools\HttpHelper.cs" />
    <Compile Include="Tools\JwtAuthorizationHelper.cs" />
    <Compile Include="Tools\MessageSendHelper.cs" />
    <Compile Include="Tools\PhoneMessageHelper.cs" />
    <Compile Include="Tools\PhoneNumberEncipherHlper.cs" />
    <Compile Include="Tools\SaasPosErrHelper.cs" />
    <Compile Include="Tools\StoreHelper.cs" />
    <Compile Include="Tools\StringReplaceHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Computer\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>