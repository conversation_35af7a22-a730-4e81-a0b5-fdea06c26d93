﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IWay_VerifyItemRepository : IRepositoryBase<Way_VerifyItem>
    {
        List<GetRecordListModel> GetRecordList(GetRecordContext context);

        GetVerifyRecordInfoModel GetVerifyInfo(string code);

        VerifyRecordDataModel GetVerifyData(QuickOrderContext context);

        GetRecordDetailModel GetVerifyItem(GetVerifyItemContext context);

        List<List<string>> ExportVerifyItem(GetRecordContext context);

        VerifyItemModel GetVerifyInfo(GetVerifyInfoContext context);
    }
}
