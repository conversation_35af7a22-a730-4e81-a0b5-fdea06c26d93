﻿using Saas.Pos.Model.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    public class TimeInfo_Applet_Proxy : TimeInfoBaseProxy
    {
        /// <summary>
        /// 获取是否存在门店时段配置
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public static bool GetWorkNotShopTime(int shopId, string timeNo, DateTime startTime, DateTime endTime)
        {
            return AppSingle.App.Storage.BookingDat.WorkNotShopTimeList.FirstOrDefault(x => x.ShopId == shopId && x.WorkTimeStart <= startTime && x.WorkTimeEnd >= endTime && timeNo == x.TimeNo) == null;
        }

        /// <summary>
        /// 获取是否存在门店时段配置
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="timeNo"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="isAdvance"></param>
        /// <param name="isDeposit"></param>
        /// <returns>true表示可预订，false不可预订</returns>
        public static bool GetWorkNotShopTimeMode(int shopId, string timeNo, DateTime bookDate, bool isAdvance, bool isDeposit)
        {
            var timeInfo = AppSingle.App.Storage.BookingDat.WorkNotShopTimeList.FirstOrDefault(x => x.ShopId == shopId && x.WorkTimeStart <= bookDate && x.WorkTimeEnd >= bookDate && timeNo == x.TimeNo);

            //没有配置就直接过
            if (timeInfo != null)
            {
                if (string.IsNullOrEmpty(timeInfo.StartHour) && string.IsNullOrEmpty(timeInfo.EndHour))
                {
                    //全都为true表示该模式已被禁止售卖  特权和订金都表示特权支付
                    if ((isAdvance || isDeposit) && timeInfo.Advance.HasValue && timeInfo.Advance.Value)
                        return false;

                    //非特权非订金，表示普通预约
                    if (!isDeposit && !isAdvance && timeInfo.Ordinary.HasValue && timeInfo.Ordinary.Value)
                        return false;
                }
            }

            return true;
        }

    }
}
