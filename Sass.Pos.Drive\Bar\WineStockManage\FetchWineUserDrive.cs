﻿
using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Application.Lib.Bar;
using Saas.Pos.Appliction.Lib.Bar;
using Saas.Pos.Common.Log;
using Saas.Pos.Common.MiddlewareProxy;
using Saas.Pos.Common.ServiceClient;
using Saas.Pos.Domain.IDrive.WineStockManage;
using Saas.Pos.Drive.DbFood;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    /// <summary>
    /// 取酒
    /// </summary>
    public class FetchWineUserDrive : WineStorkManageSubDriveBase<WineStockManageDriveBase>, IFetchWineUser
    {
        public FetchWineUserDrive(WineStockManageDriveBase imi, AppSession app) : base(imi, app)
        {
        }

        /// <summary>
        /// 用户取酒下单
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<FetchWineUserExModel> FetchWineUser(FetchWineUserContext context)
        {
            try
            {
                return ActionFun.Run(context, () =>
                {
                    if (context == null || string.IsNullOrEmpty(context.MsgPassWord) || string.IsNullOrEmpty(context.RmNo))
                        throw new ExMessage("取酒编号和Key不能为空!");
                    if (context.ShopId <= 0)
                        throw new ExMessage("门店ID不能为空!");

                    WineStockApp w = new WineStockApp();
                    //用户名下存酒明细
                    var drinksData = w.GetDrinkList(context.MsgPassWord, context.ShopId);
                    if (drinksData.Count() <= 0)
                        throw new ExMessage("名下暂无存酒!");
                    if (drinksData.Where(i => !string.IsNullOrEmpty(i.FdNo)).Count() != drinksData.Count())
                        throw new ExMessage("数据有异,请联系管理人员解决!");

                    var pos = ServiceClientBase.GetPosClient(context.ShopId);

                    var roomContext = new SERVICE.PROXY.PosService.GetRoomInfoContext() { RmNo = context.RmNo };
                    var data = pos.GetRoomInfo(roomContext);
                    if (data.state != ResponseType.success || !data.data.IsOrder)
                        throw new ExMessage("该房间状态不可下单!");

                    var result = new FetchWineUserExModel();

                    //找到商品信息
                    var fdnos = drinksData.Select(i => i.FdNo).ToArray();
                    var fdnoExContext = new SERVICE.PROXY.PosService.GetFdDataExContext() { FdNos = fdnos, ShopId = context.ShopId };
                    var fdnoExData = pos.GetFdDataExByStore(fdnoExContext);
                    if (fdnoExData.state != ResponseType.success)
                        throw new ExMessage(fdnoExData.message);
                    if (fdnoExData.data.Count() != drinksData.Count())
                        throw new ExMessage("数据有异,请联系管理人员解决!");

                    //将数据连接起来下单
                    var subFdDatas = (from d in drinksData
                                      join e in fdnoExData.data on d.FdNo equals e.FdNo
                                      select new FoodDatas()
                                      {
                                          FdNos = e.FdNo,
                                          FdPrice1 = e.FdPrice1,
                                          FdQtys = d.DrinksQty
                                      }).ToList();
                    if (subFdDatas.Count != drinksData.Count)
                        throw new ExMessage("数据有异,请联系管理人员解决!");

                    if (subFdDatas.Count() > 0)
                    {
                        var DrMemory = "微信取酒!";
                        if (w.UpdateMsgInfoState(context.MsgPassWord, context.BarName, DrMemory, context.ShopId, context.RmNo) <= 0)
                            throw new ExMessage("修改状态失败!");

                        var placeContext = new SERVICE.PROXY.PosService.PlaceOrderModel()
                        {
                            CashType = "Z",
                            RmNo = context.RmNo,
                            StoreId = context.ShopId,
                            CashUserId = "9949",
                            InputUserId = "9949",
                            Items = subFdDatas.Select(i => new SERVICE.PROXY.PosService.OrderItem()
                            {
                                FdNo = i.FdNos,
                                FdPrice = i.FdPrice1,
                                FdQty = i.FdQtys
                            }).ToArray()
                        };

                        result.IsFetch = pos.PlaceOrder(placeContext).data;
                    }


                    return result;
                });
            }
            catch (Exception ex)
            {
                LogHelper.Info(DateTime.Now + "用户取酒下单\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                throw new ExMessage("取酒失败!" + ex.Message);
            }
        }

        /// <summary>
        /// 员工取酒下单
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<FetchWineUserOrderModel> FetchWineUserOrder(FetchWineUserOrderContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.OrderShopId <= 1)
                        throw new ExMessage("用户选择的门店不可下单!");
                    if (string.IsNullOrEmpty(context.RmNo))
                        throw new ExMessage("房号不能为空!");
                    if (string.IsNullOrEmpty(context.UserData.UserId) || string.IsNullOrEmpty(context.UserData.FdUid))
                        throw new ExMessage("用户ID不能为空!");

                    var shopIdEx = context.OrderShopId <= 1 ? context.UserData.ShopId : context.OrderShopId;

                    var pos = ServiceClientBase.GetPosClient(shopIdEx);
                    //判断房间是否可下单
                    var roomContext = new SERVICE.PROXY.PosService.GetRoomInfoContext() { RmNo = context.RmNo };
                    var data = pos.GetRoomInfo(roomContext);
                    if (data.state != ResponseType.success || !data.data.IsOrder)
                        throw new ExMessage("该房间状态不可下单!");

                    //根据员工工号查询可以过期多少天的数字
                    var exData = app.MIMS.FdUserWeChatJurisdiction
                    .FindEntity(i => i.UserId == context.UserData.UserId &&       
                    (i.ShopId == context.UserData.ShopId || i.ShopId <= 1));//权限信息
                    var exDays = 0;
                    if (exData != null && exData.ExDays > 0)
                        exDays = exData.ExDays;
                    else
                    {
                        exDays = 15;

                        //判断查询条件是否为手机号格式
                        if (Regex.IsMatch(context.NumberOrPwd, @"^1[3-9]\d{9}$"))
                        {
                            //redis缓存
                            MiddlewareProxy redis = new MiddlewareProxy();

                            //查询redis里有没有授权信息，有没有授权
                            var wineAuth = redis.DB_KV.GetVal<WineStockAccreditDataModel>($@"WineStork:{context.NumberOrPwd}");
                            if (wineAuth == null || wineAuth.EndAuthTime < DateTime.Now)
                                throw new ExMessage("取酒授权时间已结束,请重新授权!");
                        }
                        //throw new ExMessage("用户没有取酒权限!");
                    };

                    WineStockApp w = new WineStockApp();
                    //用户名下存酒明细
                    var drinksData = w.GetDrinkListByUser(context.iKeyMsg, exDays, shopIdEx);
                    if (drinksData.MsgInfo == null || drinksData.Drinks == null)
                        throw new ExMessage("名下暂无存酒!");
                    if (drinksData.Drinks.Count() <= 0)
                        throw new ExMessage("名下暂无存酒!");
                    if (drinksData.Drinks.Where(i => !string.IsNullOrEmpty(i.FdNo)).Count() != drinksData.Drinks.Count())
                        throw new ExMessage("数据有异,请联系管理人员解决!");

                    //有存酒明细数据才进行下一步操作
                    if (drinksData.Drinks.Count() > 0)
                    {
                        var DrMemory = "微信取酒!" + context.DrMemory;
                        //修改存酒状态
                        if (w.UpdateMsgInfoStateByKey(context.iKeyMsg, context.UserData.Name, DrMemory, shopIdEx, context.RmNo) <= 0)
                            throw new ExMessage("修改状态失败!");

                        //微信取酒
                        var userid = "9949";
                        //存酒状态为过期的时候才使用的工号:过期取酒
                        if (drinksData.MsgInfo.MsgStatus == 3)
                            userid = "9925";

                        //合并下单参数
                        var placeContext = new SERVICE.PROXY.PosService.PlaceOrderModel()
                        {
                            CashType = "Z",
                            RmNo = context.RmNo,
                            StoreId = shopIdEx,
                            CashUserId = userid,
                            InputUserId = context.UserData.FdUid,
                            Items = drinksData.Drinks.Select(i => new SERVICE.PROXY.PosService.OrderItem()
                            {
                                FdNo = i.Unit != "支" && i.Unit != "罐" ? "E020" : i.FdNo,
                                FdPrice = 0,
                                FdQty = i.DrinksQty
                            }).ToArray()
                        };
                        //下单
                        if (!pos.PlaceOrder(placeContext).data)
                            throw new ExMessage("取酒下单失败!");
                    }
                    return new FetchWineUserOrderModel();
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "员工取酒下单\n参数:" + JsonConvert.SerializeObject(context) + "\n错误原因:" + ex.Message);
                    throw new ExMessage("取酒失败!" + ex.Message);
                }
            });
        }

    }
}
