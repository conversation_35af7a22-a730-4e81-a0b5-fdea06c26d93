﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.DbFoodOrderMenu
{
    public interface IDbFoodOrderMenuSub
    {
        /// <summary>
        /// 结账
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        ResponseContext<ReturnBool> OrderMenuSub(OrderMenuSubContext context);

        ResponseContext<List<GetFdUserAllModel>> GetFdUserAllByStore(GetFdUserAllContext context);

        ResponseContext<List<GetFdUserAllModel>> GetFdUserAll(GetFdUserAllContext context);

        ResponseContext<ReturnBool> OrderMenuSubByStore(OrderMenuSubContext context);

        ResponseContext<ReturnBool> OrderMenuSubV1(OrderMenuSubContext context);

        ResponseContext<ReturnBool> OrderMenuSubByStoreV1(OrderMenuSubContext context);

        ResponseContext<ReturnBool> ReplacePackageByStore(OrderMenuSubContext context);
    }
}
