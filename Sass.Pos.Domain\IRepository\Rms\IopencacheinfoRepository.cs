﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.Report.Model;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.Rms
{
    public partial interface IopencacheinfoRepository : IRepositoryBase<opencacheinfo>
    {
        List<OpenCacheNumberModel> GetOpenCacheList(GetBookCacheContext context);

        List<GetOnlineOpenDataModel> GetOnlineOpenData(GetOnlineIkeyContext context);

        List<GetOpenRoomRecordsModel> GetOpenRoomRecords(GetOpenRoomRecordsContext context);
    }
}
