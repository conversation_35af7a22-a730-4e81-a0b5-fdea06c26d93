﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.MemberInfo.Context
{
    public class MemberBenefitModel
    {
        /// <summary>
        /// 会员卡券配置
        /// </summary>
        public BenefitsHold<ConponBenefit> Coupons { get; set; }
        /// <summary>
        /// 会员账户配置
        /// </summary>
        public BenefitsHold<AccountBenefit> Account { get; set; }

    }

    public class AccountProcessModel
    {
        /// <summary>
        /// 未处理数据
        /// </summary>
        public AccountProcessContext OriginalData { get; set; }
        /// <summary>
        /// 匹配到的规则
        /// </summary>
        public MemberBenefitModel BenefitConfig { get; set; }
        /// <summary>
        /// 处理后返回的数据
        /// </summary>
        public AccountProcessContext NewData { get; set; }
    }

    /// <summary>
    /// 由于数据库实体还没处理，暂时当这两个为数据库实体
    /// </summary>
    public class AccountBenefit { }
    public class ConponBenefit { }
}
