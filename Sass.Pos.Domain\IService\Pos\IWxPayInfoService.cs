﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Domain.IService.Pos
{
    [ServiceContract]
    public interface IWxPayInfoService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetWxPayInfoModel>> GetWxPayList(GetWxPayInfoContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetWxPayInfoModel>> GetWxPayListByStore(GetWxPayInfoContext context);

        [OperationContract]
        ResponseContext<ReturnBool> DeleteWxPayInfo(DeleteWxPayInfoContext context);

        [OperationContract]
        ResponseContext<ReturnBool> DeleteWxPayInfoByStore(DeleteWxPayInfoContext context);
    }
}
