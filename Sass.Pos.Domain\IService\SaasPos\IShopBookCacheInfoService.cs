﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IShopBookCacheInfoService
    {
        /// <summary>
        /// 用户查看预订信息记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<RespPaginationModel<ShopBookCacheInfoModel>> GetUserBookData(GetShopBookCacheInfoContext context);

        /// <summary>
        /// 通过订单编号或者主键获取预约详细信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<GetShopBookingCacheDetailModel> GetSaasBookInfo(GetSaasBookInfoContext context);

        /// <summary>
        /// 管理平台导出在线预订订单相关信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<UserOrderReturnModel> GetConsumeList(GetConsumePageingContext context);

        /// <summary>
        /// 管理平台分页查看在线预订订单相关信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<RespPaginationModel<GetConsumeListModel>> GetConsumePageingList(GetConsumePageingContext context);

        /// <summary>
        /// 获取预约状态枚举下拉列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [OperationContract]
        ResponseContext<List<KeyValueCommonModel>> GetCacheStatus(GetCacheStatusContext context);

        [OperationContract]
        ResponseContext<ReturnBool> MakeUpOrder(MakeUpOrderContext context);

        [OperationContract]
        ResponseContext<bool> ChangeCacheStatus(ChangeCacheStatusContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetConsumePageList>> GetConsumePageList(GetConsumePageContext context);

        [OperationContract]
        ResponseContext<ReturnBool> AddBlackList(AddBlackListContext context);

        [OperationContract]
        ResponseContext<ReturnBool> ChangeBlackStatus(ChangeBlackStatusContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<Shop_BookCache_BlackList>> GetBlackList(GetBlackListContext context);
    }
}
