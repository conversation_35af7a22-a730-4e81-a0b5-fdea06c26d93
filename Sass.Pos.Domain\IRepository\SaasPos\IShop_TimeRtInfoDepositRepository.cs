﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IShop_TimeRtInfoDepositRepository : IRepositoryBase<Shop_TimeRtInfoDeposit>
    {
        List<GetShopTimeRtInfoDepositListModel> GetDepositList(GetDepositListContext context);
    }
}
