﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IwxPayInfoRepository : IRepositoryBase<wxPayInfo>
    {
        bool AddwxPayInfo(Bill_AddWxPayInfoContext context);
    }
}
