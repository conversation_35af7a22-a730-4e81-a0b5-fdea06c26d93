﻿using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
    public partial class GiftAccountSceneAllocationApp : AppBase<GiftAccountSceneAllocation>
    {
        public GiftAccountSceneInfo GetAccountScene(GetAccountSceneContext context)
        {
            return Repository.GiftAccountSceneAllocation.GetAccountScene(context);
        }

        public AccountSceneInfo GetAccountSceneInfo(GetAccountSceneContext context)
        {
            return Repository.GiftAccountSceneAllocation.GetAccountSceneInfo(context);
        }
    }
}
