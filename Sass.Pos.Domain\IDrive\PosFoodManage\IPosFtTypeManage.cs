﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.PosFoodManage
{
    public interface IPosFtTypeManage
    {
        ResponseContext<List<GetPosFtSelectDataModel>> GetPosFtSelectData(GetPosFtSelectDataContext context);

        ResponseContext<RespPaginationModel<GetPosFtDataModel>> GetPosFtData(GetPosFtDataContext context);

        ResponseContext<FdDataRetBool> InsertPosFtData(InsertFtDataContext context);

        ResponseContext<FdDataRetBool> DeletePosFtData(DeletePosFtDataContext context);

        ResponseContext<FdDataRetBool> EditPosFtData(EditPosFtDataContext context);
    }
}
