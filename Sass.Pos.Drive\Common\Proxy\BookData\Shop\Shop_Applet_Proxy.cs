﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Drive.Common.Proxy.BookData
{
    public class Shop_Applet_Proxy : ShopDataBaseProxy
    {
        /// <summary>
        /// 获取是否存在门店配置
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public static bool GetBookShop(int shopId, DateTime startTime, DateTime endTime)
        {
            return AppSingle.App.Storage.BookingDat.WorkNotShopList.FirstOrDefault(x => x.ShopId == shopId && x.WorkTimeStart <= startTime && x.WorkTimeEnd >= endTime) == null;
        }

    }
}
