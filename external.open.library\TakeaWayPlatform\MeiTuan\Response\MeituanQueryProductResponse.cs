﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.MeiTuan.Response
{
    public class MeituanQueryProductResponse
    {
        /// <summary>
        /// 套餐id
        /// </summary>
        public long deal_id { get; set; }
        /// <summary>
        /// 团购id
        /// </summary>
        public long dealgroup_id { get; set; }
        /// <summary>
        /// 团购开始售卖时间
        /// </summary>
        public string begin_date { get; set; }
        /// <summary>
        /// 团购结束售卖时间
        /// </summary>
        public string end_date { get; set; }
        /// <summary>
        /// 套餐名称
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 套餐价格
        /// </summary>
        public double price { get; set; }
        /// <summary>
        /// 套餐原价
        /// </summary>
        public double marketprice { get; set; }
        /// <summary>
        /// 团购券开始服务时间	
        /// </summary>
        public string receipt_begin_date { get; set; }
        /// <summary>
        /// 团购券结束服务时间	
        /// </summary>
        public string receipt_end_date { get; set; }
        /// <summary>
        /// 售卖状态
        /// </summary>
        public int sale_status { get; set; }
        /// <summary>
        /// 团购类型
        /// </summary>
        public string deal_type { get; set; }
        /// <summary>
        /// 团购状态
        /// </summary>
        public int dealgroup_status { get; set; }
    }
}
