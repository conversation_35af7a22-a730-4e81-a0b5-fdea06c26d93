﻿using external.open.library.TakeaWayPlatform.Tiktok.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace external.open.library.TakeaWayPlatform.TikTok.Response
{
    public class TiktokQueryCertificateResponse
    {
        public string description { get; set; }
        public int error_code { get; set; }
        public List<TikTokCertificateInfo> certificates { get; set; }
    }

    public class TikTokCertificateInfo 
    {
        public TiktokAmount amount { get; set; }
        public string encrypted_code { get; set; }
        public Sku sku { get; set; }
        public long certificate_id { get; set; }
        public string code { get; set; }
        public long expire_time { get; set; }
        /// <summary>
        /// 1:未使用2：已使用4：退款成功
        /// </summary>
        public int status { get; set; }
        public TimeCard time_card { get; set; }
        public List<VerifyRecords> verify_records { get; set; }
    }
    public class TiktokAmount 
    {
        public int original_amount { get; set; }
        public int pay_amount { get; set; }
        public int coupon_pay_amount { get; set; }
        public int list_market_amount { get; set; }
        public int merchant_ticket_amount { get; set; }
        public int origin_list_market_amount { get; set; }
        public int original_currency { get; set; }
        public int payment_discount_amount { get; set; }
        public int platform_discount_amount { get; set; }
    }
}
