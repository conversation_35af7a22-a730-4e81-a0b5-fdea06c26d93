﻿
using ComponentApplicationServiceInterface.Web;
using Saas.Pos.Model.DbFood;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.DbFood
{
 public partial class AddItemApp : AppBase<AddItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<AddItem> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.AddItem;
        }
   
        
 
 }
  

 public partial class AiTypeApp : AppBase<AiType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<AiType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.AiType;
        }
   
        
 
 }
  

 public partial class AmountLogApp : AppBase<AmountLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<AmountLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.AmountLog;
        }
   
        
 
 }
  

 public partial class BankInfoApp : AppBase<BankInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<BankInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.BankInfo;
        }
   
        
 
 }
  

 public partial class CarLeaveLogApp : AppBase<CarLeaveLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<CarLeaveLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.CarLeaveLog;
        }
   
        
 
 }
  

 public partial class CashierApp : AppBase<Cashier> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Cashier> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Cashier;
        }
   
        
 
 }
  

 public partial class ClearDataLogApp : AppBase<ClearDataLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ClearDataLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.ClearDataLog;
        }
   
        
 
 }
  

 public partial class DbFooddtpropertiesApp : AppBase<DbFooddtproperties> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<DbFooddtproperties> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.DbFooddtproperties;
        }
   
        
 
 }
  

 public partial class DbFoodTh_RoomCommissionAllotApp : AppBase<DbFoodTh_RoomCommissionAllot> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<DbFoodTh_RoomCommissionAllot> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.DbFoodTh_RoomCommissionAllot;
        }
   
        
 
 }
  

 public partial class DeadLockLogApp : AppBase<DeadLockLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<DeadLockLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.DeadLockLog;
        }
   
        
 
 }
  

 public partial class DepositInfoApp : AppBase<DepositInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<DepositInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.DepositInfo;
        }
   
        
 
 }
  

 public partial class DeptApp : AppBase<Dept> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Dept> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Dept;
        }
   
        
 
 }
  

 public partial class DeptBanSetApp : AppBase<DeptBanSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<DeptBanSet> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.DeptBanSet;
        }
   
        
 
 }
  

 public partial class EmpGift_CustRecordApp : AppBase<EmpGift_CustRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<EmpGift_CustRecord> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.EmpGift_CustRecord;
        }
   
        
 
 }
  

 public partial class EmpGift_ItemApp : AppBase<EmpGift_Item> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<EmpGift_Item> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.EmpGift_Item;
        }
   
        
 
 }
  

 public partial class EmpGift_RecordApp : AppBase<EmpGift_Record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<EmpGift_Record> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.EmpGift_Record;
        }
   
        
 
 }
  

 public partial class FdCashApp : AppBase<FdCash> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdCash> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdCash;
        }
   
        
 
 }
  

 public partial class FdCash_TrackApp : AppBase<FdCash_Track> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdCash_Track> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdCash_Track;
        }
   
        
 
 }
  

 public partial class FdCashBakApp : AppBase<FdCashBak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdCashBak> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdCashBak;
        }
   
        
 
 }
  

 public partial class FdCashBak_BApp : AppBase<FdCashBak_B> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdCashBak_B> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdCashBak_B;
        }
   
        
 
 }
  

 public partial class FdCashBak_BakApp : AppBase<FdCashBak_Bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdCashBak_Bak> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdCashBak_Bak;
        }
   
        
 
 }
  

 public partial class FdCashOrderApp : AppBase<FdCashOrder> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdCashOrder> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdCashOrder;
        }
   
        
 
 }
  

 public partial class FdCashPackRecordApp : AppBase<FdCashPackRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdCashPackRecord> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdCashPackRecord;
        }
   
        
 
 }
  

 public partial class FdDetTypeApp : AppBase<FdDetType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdDetType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdDetType;
        }
   
        
 
 }
  

 public partial class FdImageApp : AppBase<FdImage> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdImage> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdImage;
        }
   
        
 
 }
  

 public partial class FdInvApp : AppBase<FdInv> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdInv> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdInv;
        }
   
        
 
 }
  

 public partial class FdInv_BApp : AppBase<FdInv_B> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdInv_B> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdInv_B;
        }
   
        
 
 }
  

 public partial class FdInv_BakApp : AppBase<FdInv_Bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdInv_Bak> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdInv_Bak;
        }
   
        
 
 }
  

 public partial class FdInv_ExchangeLogApp : AppBase<FdInv_ExchangeLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdInv_ExchangeLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdInv_ExchangeLog;
        }
   
        
 
 }
  

 public partial class FdInvCashItemApp : AppBase<FdInvCashItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdInvCashItem> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdInvCashItem;
        }
   
        
 
 }
  

 public partial class FdInvDescApp : AppBase<FdInvDesc> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdInvDesc> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdInvDesc;
        }
   
        
 
 }
  

 public partial class FdTicketApp : AppBase<FdTicket> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdTicket> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdTicket;
        }
   
        
 
 }
  

 public partial class FdTimePriceApp : AppBase<FdTimePrice> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdTimePrice> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdTimePrice;
        }
   
        
 
 }
  

 public partial class FdTimeZoneApp : AppBase<FdTimeZone> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdTimeZone> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdTimeZone;
        }
   
        
 
 }
  

 public partial class FdTypeApp : AppBase<FdType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdType;
        }
   
        
 
 }
  

 public partial class FdUserApp : AppBase<FdUser> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdUser> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdUser;
        }
   
        
 
 }
  

 public partial class FdUserGradeApp : AppBase<FdUserGrade> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdUserGrade> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdUserGrade;
        }
   
        
 
 }
  

 public partial class FdUserRightsApp : AppBase<FdUserRights> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FdUserRights> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FdUserRights;
        }
   
        
 
 }
  

 public partial class FestivalTimeApp : AppBase<FestivalTime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FestivalTime> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FestivalTime;
        }
   
        
 
 }
  

 public partial class FoodApp : AppBase<Food> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Food> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Food;
        }
   
        
 
 }
  

 public partial class FoodCalApp : AppBase<FoodCal> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FoodCal> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FoodCal;
        }
   
        
 
 }
  

 public partial class FoodLabelApp : AppBase<FoodLabel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FoodLabel> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FoodLabel;
        }
   
        
 
 }
  

 public partial class FoodOrderMApp : AppBase<FoodOrderM> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FoodOrderM> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FoodOrderM;
        }
   
        
 
 }
  

 public partial class FPrnApp : AppBase<FPrn> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FPrn> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FPrn;
        }
   
        
 
 }
  

 public partial class FPrnDataApp : AppBase<FPrnData> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FPrnData> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FPrnData;
        }
   
        
 
 }
  

 public partial class FPrnData_BakApp : AppBase<FPrnData_Bak> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FPrnData_Bak> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FPrnData_Bak;
        }
   
        
 
 }
  

 public partial class FreePackageCoupon_RecordApp : AppBase<FreePackageCoupon_Record> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FreePackageCoupon_Record> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FreePackageCoupon_Record;
        }
   
        
 
 }
  

 public partial class FtInfoApp : AppBase<FtInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<FtInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.FtInfo;
        }
   
        
 
 }
  

 public partial class GDDB20InfoApp : AppBase<GDDB20Info> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GDDB20Info> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GDDB20Info;
        }
   
        
 
 }
  

 public partial class GDDBInfoApp : AppBase<GDDBInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GDDBInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GDDBInfo;
        }
   
        
 
 }
  

 public partial class GiftAccountApp : AppBase<GiftAccount> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftAccount> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GiftAccount;
        }
   
        
 
 }
  

 public partial class GiftAccountOperationRecordApp : AppBase<GiftAccountOperationRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftAccountOperationRecord> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GiftAccountOperationRecord;
        }
   
        
 
 }
  

 public partial class GiftAccountSceneAllocationApp : AppBase<GiftAccountSceneAllocation> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftAccountSceneAllocation> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GiftAccountSceneAllocation;
        }
   
        
 
 }
  

 public partial class GiftRoleApp : AppBase<GiftRole> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftRole> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GiftRole;
        }
   
        
 
 }
  

 public partial class GiftSceneApp : AppBase<GiftScene> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftScene> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GiftScene;
        }
   
        
 
 }
  

 public partial class GiftSceneEquityConfigApp : AppBase<GiftSceneEquityConfig> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftSceneEquityConfig> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GiftSceneEquityConfig;
        }
   
        
 
 }
  

 public partial class GiftSceneRoleBindingApp : AppBase<GiftSceneRoleBinding> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<GiftSceneRoleBinding> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.GiftSceneRoleBinding;
        }
   
        
 
 }
  

 public partial class HappyRabApp : AppBase<HappyRab> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<HappyRab> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.HappyRab;
        }
   
        
 
 }
  

 public partial class HolidayApp : AppBase<Holiday> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Holiday> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Holiday;
        }
   
        
 
 }
  

 public partial class HotFdTypeApp : AppBase<HotFdType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<HotFdType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.HotFdType;
        }
   
        
 
 }
  

 public partial class HotFoodApp : AppBase<HotFood> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<HotFood> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.HotFood;
        }
   
        
 
 }
  

 public partial class Inv_MemberOperationApp : AppBase<Inv_MemberOperation> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Inv_MemberOperation> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Inv_MemberOperation;
        }
   
        
 
 }
  

 public partial class Inv_TimeSectionApp : AppBase<Inv_TimeSection> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Inv_TimeSection> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Inv_TimeSection;
        }
   
        
 
 }
  

 public partial class InvRollBackApp : AppBase<InvRollBack> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<InvRollBack> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.InvRollBack;
        }
   
        
 
 }
  

 public partial class LanIdApp : AppBase<LanId> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<LanId> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.LanId;
        }
   
        
 
 }
  

 public partial class LanStringApp : AppBase<LanString> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<LanString> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.LanString;
        }
   
        
 
 }
  

 public partial class LastInvNoApp : AppBase<LastInvNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<LastInvNo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.LastInvNo;
        }
   
        
 
 }
  

 public partial class LastRefNoApp : AppBase<LastRefNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<LastRefNo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.LastRefNo;
        }
   
        
 
 }
  

 public partial class Limit_ConfigInfoApp : AppBase<Limit_ConfigInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Limit_ConfigInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Limit_ConfigInfo;
        }
   
        
 
 }
  

 public partial class meal_distribution_infoApp : AppBase<meal_distribution_info> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<meal_distribution_info> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.meal_distribution_info;
        }
   
        
 
 }
  

 public partial class meal_infoApp : AppBase<meal_info> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<meal_info> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.meal_info;
        }
   
        
 
 }
  

 public partial class MembAmountEditLogApp : AppBase<MembAmountEditLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MembAmountEditLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MembAmountEditLog;
        }
   
        
 
 }
  

 public partial class MemberApp : AppBase<Member> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Member> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Member;
        }
   
        
 
 }
  

 public partial class MemberCheckoutInfoApp : AppBase<MemberCheckoutInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberCheckoutInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MemberCheckoutInfo;
        }
   
        
 
 }
  

 public partial class MemberDeductionInfoApp : AppBase<MemberDeductionInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberDeductionInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MemberDeductionInfo;
        }
   
        
 
 }
  

 public partial class MemberGiveSetApp : AppBase<MemberGiveSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MemberGiveSet> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MemberGiveSet;
        }
   
        
 
 }
  

 public partial class MembSetApp : AppBase<MembSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MembSet> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MembSet;
        }
   
        
 
 }
  

 public partial class MGradeFdDiscApp : AppBase<MGradeFdDisc> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MGradeFdDisc> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MGradeFdDisc;
        }
   
        
 
 }
  

 public partial class MobileFdGiveApp : AppBase<MobileFdGive> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobileFdGive> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobileFdGive;
        }
   
        
 
 }
  

 public partial class MobileFoodApp : AppBase<MobileFood> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobileFood> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobileFood;
        }
   
        
 
 }
  

 public partial class MobileFoodDiscApp : AppBase<MobileFoodDisc> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobileFoodDisc> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobileFoodDisc;
        }
   
        
 
 }
  

 public partial class MobileFtTypeApp : AppBase<MobileFtType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobileFtType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobileFtType;
        }
   
        
 
 }
  

 public partial class MobilePackGiveApp : AppBase<MobilePackGive> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobilePackGive> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobilePackGive;
        }
   
        
 
 }
  

 public partial class MobilOrderItemApp : AppBase<MobilOrderItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobilOrderItem> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobilOrderItem;
        }
   
        
 
 }
  

 public partial class MobilOrderTitleApp : AppBase<MobilOrderTitle> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobilOrderTitle> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobilOrderTitle;
        }
   
        
 
 }
  

 public partial class MobilUserOrderTitleApp : AppBase<MobilUserOrderTitle> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<MobilUserOrderTitle> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.MobilUserOrderTitle;
        }
   
        
 
 }
  

 public partial class NewFdGiveApp : AppBase<NewFdGive> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NewFdGive> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.NewFdGive;
        }
   
        
 
 }
  

 public partial class NewFdTypeApp : AppBase<NewFdType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NewFdType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.NewFdType;
        }
   
        
 
 }
  

 public partial class NewFdTypeLinkApp : AppBase<NewFdTypeLink> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NewFdTypeLink> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.NewFdTypeLink;
        }
   
        
 
 }
  

 public partial class NewMemberLogApp : AppBase<NewMemberLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<NewMemberLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.NewMemberLog;
        }
   
        
 
 }
  

 public partial class ParamSetApp : AppBase<ParamSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ParamSet> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.ParamSet;
        }
   
        
 
 }
  

 public partial class pre_orderApp : AppBase<pre_order> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<pre_order> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.pre_order;
        }
   
        
 
 }
  

 public partial class PreOrderSendMsgInfoApp : AppBase<PreOrderSendMsgInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<PreOrderSendMsgInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.PreOrderSendMsgInfo;
        }
   
        
 
 }
  

 public partial class PrepaymentItemApp : AppBase<PrepaymentItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<PrepaymentItem> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.PrepaymentItem;
        }
   
        
 
 }
  

 public partial class PrepaymentRecordApp : AppBase<PrepaymentRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<PrepaymentRecord> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.PrepaymentRecord;
        }
   
        
 
 }
  

 public partial class PriceNoApp : AppBase<PriceNo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<PriceNo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.PriceNo;
        }
   
        
 
 }
  

 public partial class QrInfoApp : AppBase<QrInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<QrInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.QrInfo;
        }
   
        
 
 }
  

 public partial class RecordRoomTimeApp : AppBase<RecordRoomTime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RecordRoomTime> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RecordRoomTime;
        }
   
        
 
 }
  

 public partial class RefToZDLogApp : AppBase<RefToZDLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RefToZDLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RefToZDLog;
        }
   
        
 
 }
  

 public partial class RightSetApp : AppBase<RightSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RightSet> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RightSet;
        }
   
        
 
 }
  

 public partial class RmAccountInfoApp : AppBase<RmAccountInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmAccountInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmAccountInfo;
        }
   
        
 
 }
  

 public partial class RmAreaApp : AppBase<RmArea> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmArea> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmArea;
        }
   
        
 
 }
  

 public partial class RmClearLogApp : AppBase<RmClearLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmClearLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmClearLog;
        }
   
        
 
 }
  

 public partial class RmCloseInfoApp : AppBase<RmCloseInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmCloseInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmCloseInfo;
        }
   
        
 
 }
  

 public partial class RmCloseInfo_CollectApp : AppBase<RmCloseInfo_Collect> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmCloseInfo_Collect> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmCloseInfo_Collect;
        }
   
        
 
 }
  

 public partial class RmExchangeDetailApp : AppBase<RmExchangeDetail> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmExchangeDetail> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmExchangeDetail;
        }
   
        
 
 }
  

 public partial class RmExchangeLogApp : AppBase<RmExchangeLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmExchangeLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmExchangeLog;
        }
   
        
 
 }
  

 public partial class RmFtPrnIndexApp : AppBase<RmFtPrnIndex> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmFtPrnIndex> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmFtPrnIndex;
        }
   
        
 
 }
  

 public partial class RmOrderApp : AppBase<RmOrder> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmOrder> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmOrder;
        }
   
        
 
 }
  

 public partial class RmOrderDelLogApp : AppBase<RmOrderDelLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmOrderDelLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmOrderDelLog;
        }
   
        
 
 }
  

 public partial class RmOrderLogApp : AppBase<RmOrderLog> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmOrderLog> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmOrderLog;
        }
   
        
 
 }
  

 public partial class RmsRoomApp : AppBase<RmsRoom> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmsRoom> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmsRoom;
        }
   
        
 
 }
  

 public partial class RmTypeApp : AppBase<RmType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RmType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RmType;
        }
   
        
 
 }
  

 public partial class RoomApp : AppBase<Room> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Room> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Room;
        }
   
        
 
 }
  

 public partial class Room_Consume_NumberApp : AppBase<Room_Consume_Number> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Room_Consume_Number> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Room_Consume_Number;
        }
   
        
 
 }
  

 public partial class Room_Consume_Number_ItmeApp : AppBase<Room_Consume_Number_Itme> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Room_Consume_Number_Itme> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Room_Consume_Number_Itme;
        }
   
        
 
 }
  

 public partial class RoomCloseLabelApp : AppBase<RoomCloseLabel> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RoomCloseLabel> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RoomCloseLabel;
        }
   
        
 
 }
  

 public partial class RoomCloseLabelTypeDetailApp : AppBase<RoomCloseLabelTypeDetail> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RoomCloseLabelTypeDetail> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RoomCloseLabelTypeDetail;
        }
   
        
 
 }
  

 public partial class RoomCommissionApp : AppBase<RoomCommission> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RoomCommission> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RoomCommission;
        }
   
        
 
 }
  

 public partial class RoomExtendApp : AppBase<RoomExtend> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RoomExtend> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RoomExtend;
        }
   
        
 
 }
  

 public partial class RoomtestApp : AppBase<Roomtest> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Roomtest> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Roomtest;
        }
   
        
 
 }
  

 public partial class RtAutoApp : AppBase<RtAuto> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RtAuto> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RtAuto;
        }
   
        
 
 }
  

 public partial class RtAutoZDApp : AppBase<RtAutoZD> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RtAutoZD> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RtAutoZD;
        }
   
        
 
 }
  

 public partial class RtTimePriceApp : AppBase<RtTimePrice> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<RtTimePrice> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.RtTimePrice;
        }
   
        
 
 }
  

 public partial class S_AccTypeApp : AppBase<S_AccType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<S_AccType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.S_AccType;
        }
   
        
 
 }
  

 public partial class S_CashItemApp : AppBase<S_CashItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<S_CashItem> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.S_CashItem;
        }
   
        
 
 }
  

 public partial class S_PrnTypeApp : AppBase<S_PrnType> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<S_PrnType> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.S_PrnType;
        }
   
        
 
 }
  

 public partial class S_RmStatusApp : AppBase<S_RmStatus> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<S_RmStatus> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.S_RmStatus;
        }
   
        
 
 }
  

 public partial class SceneRole_ConfigApp : AppBase<SceneRole_Config> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SceneRole_Config> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.SceneRole_Config;
        }
   
        
 
 }
  

 public partial class SceneRole_Config_ExtraApp : AppBase<SceneRole_Config_Extra> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SceneRole_Config_Extra> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.SceneRole_Config_Extra;
        }
   
        
 
 }
  

 public partial class SchedulingRecordApp : AppBase<SchedulingRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SchedulingRecord> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.SchedulingRecord;
        }
   
        
 
 }
  

 public partial class SDateApp : AppBase<SDate> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<SDate> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.SDate;
        }
   
        
 
 }
  

 public partial class ShareSetInfoApp : AppBase<ShareSetInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ShareSetInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.ShareSetInfo;
        }
   
        
 
 }
  

 public partial class ShiftInfoApp : AppBase<ShiftInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<ShiftInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.ShiftInfo;
        }
   
        
 
 }
  

 public partial class StarInfoApp : AppBase<StarInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<StarInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.StarInfo;
        }
   
        
 
 }
  

 public partial class TestTableApp : AppBase<TestTable> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<TestTable> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.TestTable;
        }
   
        
 
 }
  

 public partial class triggerRecordApp : AppBase<triggerRecord> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<triggerRecord> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.triggerRecord;
        }
   
        
 
 }
  

 public partial class UserAmountApp : AppBase<UserAmount> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserAmount> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserAmount;
        }
   
        
 
 }
  

 public partial class UserAmountDetailApp : AppBase<UserAmountDetail> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserAmountDetail> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserAmountDetail;
        }
   
        
 
 }
  

 public partial class UserFtZDApp : AppBase<UserFtZD> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserFtZD> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserFtZD;
        }
   
        
 
 }
  

 public partial class UserInfo_BindingApp : AppBase<UserInfo_Binding> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserInfo_Binding> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserInfo_Binding;
        }
   
        
 
 }
  

 public partial class UserIOApp : AppBase<UserIO> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserIO> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserIO;
        }
   
        
 
 }
  

 public partial class UserZDItemApp : AppBase<UserZDItem> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserZDItem> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserZDItem;
        }
   
        
 
 }
  

 public partial class UserZDItemDetailApp : AppBase<UserZDItemDetail> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserZDItemDetail> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserZDItemDetail;
        }
   
        
 
 }
  

 public partial class UserZDSetApp : AppBase<UserZDSet> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<UserZDSet> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.UserZDSet;
        }
   
        
 
 }
  

 public partial class VesaApp : AppBase<Vesa> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<Vesa> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.Vesa;
        }
   
        
 
 }
  

 public partial class WebOrderTableApp : AppBase<WebOrderTable> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<WebOrderTable> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.WebOrderTable;
        }
   
        
 
 }
  

 public partial class WeChatFoodOrderMsgApp : AppBase<WeChatFoodOrderMsg> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<WeChatFoodOrderMsg> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.WeChatFoodOrderMsg;
        }
   
        
 
 }
  

 public partial class WeChatFoodOrderMsg2App : AppBase<WeChatFoodOrderMsg2> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<WeChatFoodOrderMsg2> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.WeChatFoodOrderMsg2;
        }
   
        
 
 }
  

 public partial class WindTicketApp : AppBase<WindTicket> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<WindTicket> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.WindTicket;
        }
   
        
 
 }
  

 public partial class wx_shopmall_worktimeApp : AppBase<wx_shopmall_worktime> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<wx_shopmall_worktime> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.wx_shopmall_worktime;
        }
   
        
 
 }
  

 public partial class wxpay_FdCashOrderApp : AppBase<wxpay_FdCashOrder> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<wxpay_FdCashOrder> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.wxpay_FdCashOrder;
        }
   
        
 
 }
  

 public partial class wxPayCheckInfoApp : AppBase<wxPayCheckInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<wxPayCheckInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.wxPayCheckInfo;
        }
   
        
 
 }
  

 public partial class wxPayInfoApp : AppBase<wxPayInfo> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<wxPayInfo> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.wxPayInfo;
        }
   
        
 
 }
  

 public partial class wxPayInfo_DelApp : AppBase<wxPayInfo_Del> {
  protected override ComponentApplicationServiceInterface.Repository.IRepositoryBase<wxPayInfo_Del> SetRepository(RepositoryFactory.T4.DbFood.RepositorySession Session)
        {
            return Session.wxPayInfo_Del;
        }
   
        
 
 }
  

}
