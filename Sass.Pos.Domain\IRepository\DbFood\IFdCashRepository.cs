﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IFdCashRepository : IRepositoryBase<FdCash>
    {
        List<GetBillDetailsModel> GetBillDetail(GetBillDataContext context);

        bool Bill_Fd_DelOrder(Bill_Fd_DelOrderContext context);

        bool Bill_RefToZD(Bill_RefToZDContext context);

        bool Bill_RefReCall(Bill_RefReCallContext context);

        bool Bill_AddQrInfo(Bill_AddQrInfoContext context);

        GetFdCashInfoModel GetFdCashInfo(string refNo);
    }
}
