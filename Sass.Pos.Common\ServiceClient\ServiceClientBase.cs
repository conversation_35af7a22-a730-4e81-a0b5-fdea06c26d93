﻿
using Saas.Pos.Common.Tools;
using SERVICE.PROXY.PosService;
using SERVICE.PROXY.SaasPosService;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Common.ServiceClient
{
    public class ServiceClientBase
    {
        static object lockObj = new object();
        static Dictionary<int, IPos> PosClientDic = new Dictionary<int, IPos>();
        static ISaasPos SaasPos;

        /// <summary>
        /// 获取Pos服务客户端
        /// </summary>
        /// <param name="storeId"></param>
        /// <returns></returns>
        public static IPos GetPosClient(int storeId)
        {
            lock (lockObj)
            {
                if (!PosClientDic.ContainsKey(storeId))
                {
                    //云服务
                    var storeConfig = StoreHelper.StoreManage.GetStoreInfo(storeId);
                    if (storeConfig == null)
                        throw new ExMessage("门店配置不存在，请检查！");
                    var binding = new BasicHttpBinding();
                    binding.MaxBufferPoolSize = 2147483647;
                    binding.MaxBufferSize = 2147483647;
                    binding.MaxReceivedMessageSize = 2147483647;
                    var endpoint = new EndpointAddress(storeConfig.Url);
                    var posClient = new PosClient(binding, endpoint);

                    PosClientDic.Add(storeId, posClient);
                    return posClient;
                }
                else
                    return PosClientDic[storeId];
            }
        }

        public static ISaasPos GetSaasPosClient()
        {
            if (SaasPos == null)
            {
                var binding = new BasicHttpBinding();
                binding.MaxBufferPoolSize = 2147483647;
                binding.MaxBufferSize = 2147483647;
                binding.MaxReceivedMessageSize = 2147483647;
                var endpoint = new EndpointAddress(ConfigurationManager.AppSettings["SaasPosIP"].ToString());
                SaasPos = new SaasPosClient(binding, endpoint);
            }

            return SaasPos;
        }
    }
}
