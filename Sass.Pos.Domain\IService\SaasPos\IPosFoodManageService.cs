﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.SaasPos
{
    [ServiceContract]
    public interface IPosFoodManageService
    {
        [OperationContract]
        ResponseContext<RespPaginationModel<GetPosFdDataModel>> GetPosFdData(GetPosFdDataContext context);

        [OperationContract]
        ResponseContext<FdDataRetBool> InsertPosFdData(InsertPosFdDataContext context);

        [OperationContract]
        ResponseContext<List<GetPosFtSelectDataModel>> GetPosFtSelectData(GetPosFtSelectDataContext context);

        [OperationContract]
        ResponseContext<FdDataRetBool> DeletePosFdData(DeletePosFdDataContext context);

        [OperationContract]
        ResponseContext<FdDataRetBool> EditPosFoodData(EditPosFoodDataContext context);

        [OperationContract]
        ResponseContext<RespPaginationModel<GetPosFtDataModel>> GetPosFtData(GetPosFtDataContext context);

        [OperationContract]
        ResponseContext<FdDataRetBool> InsertPosFtData(InsertFtDataContext context);

        [OperationContract]
        ResponseContext<FdDataRetBool> DeletePosFtData(DeletePosFtDataContext context);

        [OperationContract]
        ResponseContext<FdDataRetBool> EditPosFtData(EditPosFtDataContext context);
    }
}
