﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IDrive.OrderManage
{
    /// <summary>
    /// 订单商品管理
    /// </summary>
    public interface IOrderManageCoupon
    {
        /// <summary>
        /// 根据优惠券计算优惠后金额
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        CouponComputeDataModel CouponComputeData(CouponComputeDataContext context);
    }
}
