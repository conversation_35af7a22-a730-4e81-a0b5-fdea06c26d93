﻿using Saas.Pos.Application.Lib.SaasPos;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class Pos_Storage_FoodApp : AppBase<Pos_Storage_Food>
    {
        /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetPosFdDataModel> GetPosFdData(GetPosFdDataContext context)
        {
            return Repository.Pos_Storage_Food.GetPosFdData(context);
        }
    }
}
