﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.SaasPos
{
    public partial interface IShop_BookGoodRepository : IRepositoryBase<Shop_BookGood>
    {
        List<GetBookSkuModel> GetSkuData(GetBookSkuDataContext context);

        List<GetOrderBookSkuModel> GetOrderSkuData(List<GetOrderBookSkuContext> contexts);

        List<GetOrderBookSkuModel> GetSkuList(GetSkuListContext context);

        GetOrderDetailsModel GetOrderDetails(QueryOrderPlaceContext context);

        List<GetNightBookModel> GetNightBook(GetNightBookContext context);

        GetShopBookGoodModel GetBookGoodList(GetShopBookGoodListContextEx context);

        List<GetShopBookGoodListModel> GetSameBookGood(GetBookSkuDataContextEx context);

        List<GetOrderDetailsModel> GetOrderDetailList(List<int> orderIds);

        List<GetSkuDataModel> GetSkuData(GetSkuDataContext context);

        List<Shop_GoodsPayMethodExModel> GetShopSkuPayMethodList(GetShopPayMethodsListContext context);

        List<GetModeLinkDataModel> GetModeLinkData(GetModeLinkDataContext context);

        List<GetBookGoodDataModel> GetBookGoodData(GetBookGoodDataContext context);

        List<GetPermitDataExModel> GetPermitDataEx(GetPermitDataExContext context);

        List<GetRtInfoDataModel> GetRtInfoData(GetRtInfoDataContext context);
    }
}
