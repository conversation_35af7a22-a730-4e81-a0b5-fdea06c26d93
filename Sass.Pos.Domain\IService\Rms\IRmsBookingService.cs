﻿using ComponentApplicationServiceInterface.Context.Response;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Saas.Pos.Domain.IService.Rms
{
    [ServiceContract]
    public interface IRmsBookingService
    {
        [OperationContract]
        ResponseContext<GetBookSysDataModel> GetBookSysData(GetBookShopContext context);

        [OperationContract]
        ResponseContext<List<BookTimeInfoModel>> GetTimeSelction(GetTimeSelctionContext context);

        [OperationContract]
        ResponseContext<List<GetBookRtTypeModel>> GetRtList(GetRtListContext context);

        [OperationContract]
        ResponseContext<BookingModel> Book(BookingContext context);

        [OperationContract]
        ResponseContext<List<GetBookRoomInfoModel>> GetBookRoomData(GetBookRoomDataContext context);

        [OperationContract]
        ResponseContext<GetBookingNumberModel> GetBookingNumber(GetBookingNumberContext context);

        [OperationContract]
        ResponseContext<List<GetBookingCustModel>> GetBookCustList(GetBookCustContext context);

        [OperationContract]
        ResponseContext<string> ChangeBook(ChangeBookContext context);

        [OperationContract]
        ResponseContext<int> DelBook(DeleteBookContext context);

        [OperationContract]
        ResponseContext<List<BookTimeInfoModel>> GetProsesTime(GetProsesTimeContext context);

        [OperationContract]
        ResponseContext<bool> RefreshBasicData(RefreshBasicDataContext context);
    }
}
