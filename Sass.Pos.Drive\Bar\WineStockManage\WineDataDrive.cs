﻿using ComponentApplicationServiceInterface.Context.Response;
using Newtonsoft.Json;
using Saas.Pos.Application.Lib.Bar;
using Saas.Pos.Common.Log;
using Saas.Pos.Domain.IDrive.WineStockManage;
using Saas.Pos.Drive.Lib;
using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Saas.Pos.Drive.Bar.WineStockManage
{
    /// <summary>
    /// 酒水信息管理
    /// </summary>
    public class WineDataDrive : WineStorkManageSubDriveBase<WineStockManageDriveBase>, IWineData
    {
        public WineDataDrive(WineStockManageDriveBase imi, AppSession app) : base(imi, app)
        {
        }

        /// <summary>
        /// 查询酒水明细
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<GetWineDataModel> GetWineData(GetWineDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();
                    //酒水明细
                    var data = w.GetWineData(context);

                    return data;
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-查询酒水明细\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询酒水明细失败!");
                }
            });
        }

        /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetfdListEx>> GetFdList(GetfdListContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();

                    var data = w.GetFdList(context);

                    return RespPaginationModel<GetfdListEx>.Package(context.Pagination, data);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-查询商品信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询商品信息失败!");
                }
            });
        }

        /// <summary>
        /// 批量新增商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<EditFdDataModel> InsertFdData(List<EditFdDataContext> context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.Count <= 0)
                        throw new ExMessage("请输入数据!");
                    if (context.Where(i => string.IsNullOrEmpty(i.FdNo)).Count() > 0)
                        throw new ExMessage("商品编号不能为空!");
                    WineStockApp w = new WineStockApp();

                    return w.InsertFdData(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-批量新增商品信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// 编辑商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<EditFdDataModel> EditFdData(EditFdDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.FdNo))
                        throw new ExMessage("商品编号不能为空!");

                    WineStockApp w = new WineStockApp();

                    return w.EditFdData(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-编辑商品信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// 删除商品信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<DeleteFdDataModel> DeleteFdData(DeleteFdDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.FdNo))
                        throw new ExMessage("商品编号不能为空!");

                    WineStockApp w = new WineStockApp();

                    return w.DeleteFdData(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-删除商品信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// 查询商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetfdTypeList>> GetFtList(GetftListContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();

                    var data = w.GetFtList(context);

                    return RespPaginationModel<GetfdTypeList>.Package(context.Pagination, data);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-查询商品分类信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询商品分类信息失败!");
                }
            });
        }

        /// <summary>
        /// 根据门店查询商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<List<GetfdTypeList>> GetfdTypeLists(GetfdTypeListsContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.ShopId <= 0)
                        return new List<GetfdTypeList>();
                    WineStockApp w = new WineStockApp();

                    var data = w.GetfdTypeLists(context.ShopId);

                    return data;
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-根据门店查询商品分类信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询商品分类信息失败!");
                }
            });
        }

        /// <summary>
        /// 批量新增商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<EditFtDataModel> InsertFtData(List<EditFtDataContext> context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.Count <= 0)
                        throw new ExMessage("请输入数据!");
                    if (context.Where(i => string.IsNullOrEmpty(i.FtNo)).Count() > 0)
                        throw new ExMessage("商品分类编号不能为空!");
                    WineStockApp w = new WineStockApp();

                    return w.InsertFtData(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-批量新增商品分类信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// 编辑商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<EditFtDataModel> EditFtData(EditFtDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.FtNo))
                        throw new ExMessage("商品分类编号不能为空!");

                    WineStockApp w = new WineStockApp();

                    return w.EditFtData(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-编辑商品分类信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// 删除商品分类信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<DeleteFtDataModel> DeleteFtData(DeleteFtDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (string.IsNullOrEmpty(context.FtNo))
                        throw new ExMessage("商品分类编号不能为空!");

                    WineStockApp w = new WineStockApp();

                    return w.DeleteFtData(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-删除商品分类信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// 查询单位信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<RespPaginationModel<GetUnitList>> GetUnitList(GetUnitListContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();

                    var data = w.GetUnitList(context);

                    return RespPaginationModel<GetUnitList>.Package(context.Pagination, data);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-查询单位信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage("查询单位信息失败!");
                }
            });
        }

        /// <summary>
        /// 编辑单位信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<EditUnitDataModel> EditUnitData(EditUnitDataContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    WineStockApp w = new WineStockApp();

                    return w.EditUnitData(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-编辑单位信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }

        /// <summary>
        /// 删除单位信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public ResponseContext<DeleteUnitModel> DeleteUnit(DeleteUnitContext context)
        {
            return ActionFun.Run(context, () =>
            {
                try
                {
                    if (context.Id <= 0)
                        throw new ExMessage("单位ID不能为空!");

                    WineStockApp w = new WineStockApp();

                    return w.DeleteUnit(context);
                }
                catch (Exception ex)
                {
                    LogHelper.Info(DateTime.Now + "酒水信息管理-删除单位信息\n参数" + JsonConvert.SerializeObject(context) + "\n错误信息" + ex.Message);
                    throw new ExMessage(ex.Message);
                }
            });
        }
    }
}
