﻿using Saas.Pos.Common.Rms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Security.Cryptography;

namespace Saas.Pos.Common.Tools
{
    /// <summary>
    /// 编号生成帮助类
    /// </summary>
    public class GenerateHelper
    {
        /// <summary>
        /// 获取预约号
        /// </summary>
        /// <returns></returns>
        public static string GetBookCode(int storeId, DateTime bookDate)
        {
            return NumberPoolHelper.Single.GenerateNumber(storeId, bookDate);
        }

        /// <summary>
        /// 获取Spu编号
        /// </summary>
        /// <returns></returns>
        public static string GetSpuCode()
        {
            return "P" + GenerateCode(9);
        }

        /// <summary>
        /// 获取Sku编号
        /// </summary>
        /// <returns></returns>
        public static string GetSkuCode()
        {
            return "K" + GenerateCode(9);
        }

        /// <summary>
        /// 通过时间戳和随机数生成一个n位数编号并返回
        /// </summary>
        /// <returns></returns>
        private static string GenerateCode(int length)
        {
            long timespan = DateTime.Now.Ticks;
            var randomNumber = new Random().Next(1000000, 10000000);
            //结合时间戳和随机数生成较长的唯一标识符
            string uniqueIdentifier = (timespan + randomNumber).ToString();
            return uniqueIdentifier.Substring(length);
        }

        /// <summary>
        /// 获取卡券编号
        /// </summary>
        /// <returns></returns>
        public static string GetCouponCode()
        {
            byte[] randomBytes = new byte[16];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(randomBytes);
            }

            return $"{randomBytes[0] % 9 + 1}-{BitConverter.ToUInt16(randomBytes, 1) % 9000 + 1000}-{BitConverter.ToUInt16(randomBytes, 3) % 9000 + 1000}-{BitConverter.ToUInt16(randomBytes, 5) % 9000 + 1000}";
        }

        /// <summary>
        /// 获取外部平台发券订单号
        /// </summary>
        /// <returns></returns>
        public static string GetGrouponBillTradeCode()
        {
            // 生成 GUID
            Guid newGuid = Guid.NewGuid();
            return "D" + newGuid.ToString("N").Substring(0, 20);
        }
    }
}
