# KTV营业数据查询实施指南

## 实施步骤概览

### 阶段一：环境准备和配置（预计1-2天）
1. 数据库连接测试
2. 链接服务器配置
3. 权限和安全设置
4. 基础测试

### 阶段二：存储过程开发（预计2-3天）
1. 创建主存储过程
2. 创建辅助存储过程
3. 错误处理和日志
4. 性能优化

### 阶段三：应用层集成（预计1-2天）
1. C#调用代码开发
2. 数据模型定义
3. 异常处理
4. 单元测试

### 阶段四：测试和优化（预计2-3天）
1. 功能测试
2. 性能测试
3. 压力测试
4. 优化调整

## 详细实施步骤

### 步骤1：数据库连接测试

```sql
-- 在operatedata数据库服务器(192.168.2.5)上执行
-- 测试到rms2019服务器的连接
SELECT 1 as TestConnection

-- 测试远程数据库连接
EXEC('SELECT TOP 1 * FROM opencacheinfo') AT [193.112.2.229]
```

### 步骤2：配置链接服务器

```sql
-- 在operatedata数据库服务器上执行
USE master
GO

-- 删除已存在的链接服务器（如果有）
IF EXISTS(SELECT * FROM sys.servers WHERE name = 'RMS2019_LINK')
    EXEC sp_dropserver 'RMS2019_LINK', 'droplogins'
GO

-- 创建链接服务器
EXEC sp_addlinkedserver 
    @server = 'RMS2019_LINK',
    @srvproduct = 'SQL Server',
    @provider = 'SQLNCLI',
    @datasrc = '193.112.2.229',
    @catalog = 'dbfood'
GO

-- 配置安全性
EXEC sp_addlinkedsrvlogin 
    @rmtsrvname = 'RMS2019_LINK',
    @useself = 'false',
    @locallogin = NULL,
    @rmtuser = 'sa',
    @rmtpassword = 'Musicbox@123'
GO

-- 配置链接服务器选项
EXEC sp_serveroption 'RMS2019_LINK', 'collation compatible', 'false'
EXEC sp_serveroption 'RMS2019_LINK', 'data access', 'true'
EXEC sp_serveroption 'RMS2019_LINK', 'dist', 'false'
EXEC sp_serveroption 'RMS2019_LINK', 'pub', 'false'
EXEC sp_serveroption 'RMS2019_LINK', 'rpc', 'true'
EXEC sp_serveroption 'RMS2019_LINK', 'rpc out', 'true'
EXEC sp_serveroption 'RMS2019_LINK', 'sub', 'false'
EXEC sp_serveroption 'RMS2019_LINK', 'connect timeout', '0'
EXEC sp_serveroption 'RMS2019_LINK', 'collation name', null
EXEC sp_serveroption 'RMS2019_LINK', 'lazy schema validation', 'false'
EXEC sp_serveroption 'RMS2019_LINK', 'query timeout', '0'
EXEC sp_serveroption 'RMS2019_LINK', 'use remote collation', 'true'
GO

-- 测试链接
SELECT TOP 5 * FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo
```

### 步骤3：创建完整存储过程

```sql
USE operatedata
GO

-- 创建主存储过程
IF OBJECT_ID('dbo.sp_GetKtvBusinessData', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_GetKtvBusinessData
GO

CREATE PROCEDURE [dbo].[sp_GetKtvBusinessData]
    @QueryDate DATE = NULL,
    @ShopId INT = NULL,
    @Debug BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    -- 参数验证
    IF @QueryDate IS NULL
        SET @QueryDate = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE)
    
    DECLARE @StartTime DATETIME = @QueryDate
    DECLARE @EndTime DATETIME = DATEADD(DAY, 1, @QueryDate)
    DECLARE @ErrorMessage NVARCHAR(4000)
    DECLARE @StartExecTime DATETIME = GETDATE()
    
    IF @Debug = 1
        PRINT '开始执行KTV营业数据查询，查询日期: ' + CAST(@QueryDate AS NVARCHAR(10))
    
    BEGIN TRY
        -- 创建临时表存储业务数据
        CREATE TABLE #BusinessData (
            InvNo NVARCHAR(50) NOT NULL,
            ShopId INT,
            RmNo NVARCHAR(20),
            OpenTime DATETIME,
            CustName NVARCHAR(100),
            Numbers INT,
            CashAmount DECIMAL(18,2) DEFAULT 0,
            VesaAmount DECIMAL(18,2) DEFAULT 0,
            WXPayAmount DECIMAL(18,2) DEFAULT 0,
            TotalAmount DECIMAL(18,2) DEFAULT 0,
            PaymentMethod NVARCHAR(100),
            IsDirect BIT DEFAULT 0,
            ChannelType NVARCHAR(50),
            OrderUserName NVARCHAR(100),
            CtName NVARCHAR(100)
        )
        
        -- 创建索引提高性能
        CREATE CLUSTERED INDEX IX_BusinessData_InvNo ON #BusinessData(InvNo)
        
        IF @Debug = 1
            PRINT '临时表创建完成'
        
        -- 获取开台数据
        INSERT INTO #BusinessData (
            InvNo, ShopId, RmNo, OpenTime, CustName, Numbers, OrderUserName, CtName
        )
        SELECT 
            o.Invno,
            o.ShopId,
            o.RmNo,
            o.BookDateTime,
            o.CustName,
            ISNULL(o.Numbers, 0),
            o.OrderUserName,
            o.CtName
        FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo o
        WHERE o.BookDateTime >= @StartTime 
            AND o.BookDateTime < @EndTime
            AND o.Invno IS NOT NULL 
            AND o.Invno != ''
            AND (@ShopId IS NULL OR o.ShopId = @ShopId)
        
        DECLARE @OpenRecordCount INT = @@ROWCOUNT
        
        IF @Debug = 1
            PRINT '开台数据获取完成，记录数: ' + CAST(@OpenRecordCount AS NVARCHAR(10))
        
        -- 更新结账数据
        UPDATE bd
        SET 
            CashAmount = ISNULL(c.Cash, 0),
            VesaAmount = ISNULL(c.Vesa, 0),
            WXPayAmount = ISNULL(c.WXPay, 0),
            TotalAmount = ISNULL(c.Cash, 0) + ISNULL(c.Vesa, 0) + ISNULL(c.WXPay, 0),
            IsDirect = 1,
            PaymentMethod = CASE 
                WHEN ISNULL(c.WXPay, 0) > 0 THEN '微信支付'
                WHEN ISNULL(c.Vesa, 0) > 0 THEN '会员卡'
                WHEN ISNULL(c.Cash, 0) > 0 THEN '现金'
                ELSE '未支付'
            END,
            ChannelType = CASE 
                WHEN ISNULL(c.WXPay, 0) > 0 THEN '线上'
                WHEN ISNULL(c.Vesa, 0) > 0 THEN '会员'
                WHEN ISNULL(c.Cash, 0) > 0 THEN '现金'
                ELSE '其他'
            END
        FROM #BusinessData bd
        INNER JOIN dbo.rmcloseinfo c ON bd.InvNo = c.InvNo
        
        DECLARE @CloseRecordCount INT = @@ROWCOUNT
        
        IF @Debug = 1
            PRINT '结账数据关联完成，匹配记录数: ' + CAST(@CloseRecordCount AS NVARCHAR(10))
        
        -- 返回结果集1：业务数据
        SELECT 
            '业务数据' as DataType,
            InvNo,
            ShopId,
            RmNo,
            OpenTime,
            CustName,
            Numbers,
            CashAmount,
            VesaAmount,
            WXPayAmount,
            TotalAmount,
            PaymentMethod,
            IsDirect,
            ChannelType,
            OrderUserName,
            CtName
        FROM #BusinessData
        ORDER BY OpenTime, InvNo
        
        -- 返回结果集2：直落统计
        SELECT 
            '直落统计' as DataType,
            COUNT(*) as TotalOrders,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectOrders,
            SUM(CASE WHEN IsDirect = 0 THEN 1 ELSE 0 END) as NonDirectOrders,
            CASE WHEN COUNT(*) > 0 
                THEN CAST(SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2))
                ELSE 0 
            END as DirectRate,
            SUM(CASE WHEN IsDirect = 1 THEN TotalAmount ELSE 0 END) as DirectAmount,
            SUM(CASE WHEN IsDirect = 0 THEN TotalAmount ELSE 0 END) as NonDirectAmount
        FROM #BusinessData
        
        -- 返回结果集3：渠道统计
        SELECT 
            '渠道统计' as DataType,
            ChannelType,
            COUNT(*) as OrderCount,
            SUM(TotalAmount) as TotalAmount,
            CASE WHEN COUNT(*) > 0 
                THEN CAST(SUM(TotalAmount) / COUNT(*) AS DECIMAL(18,2))
                ELSE 0 
            END as AvgAmount,
            CASE WHEN (SELECT COUNT(*) FROM #BusinessData) > 0 
                THEN CAST(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM #BusinessData) AS DECIMAL(5,2))
                ELSE 0 
            END as Percentage
        FROM #BusinessData
        GROUP BY ChannelType
        ORDER BY TotalAmount DESC
        
        -- 返回结果集4：时段统计
        SELECT 
            '时段统计' as DataType,
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END as TimeSlot,
            COUNT(*) as OrderCount,
            SUM(TotalAmount) as TotalAmount,
            SUM(CASE WHEN IsDirect = 1 THEN 1 ELSE 0 END) as DirectCount
        FROM #BusinessData
        WHERE OpenTime IS NOT NULL
        GROUP BY 
            CASE 
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 10 AND 13 THEN '上午(10-13)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 14 AND 17 THEN '下午(14-17)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 18 AND 21 THEN '晚上(18-21)'
                WHEN DATEPART(HOUR, OpenTime) BETWEEN 22 AND 23 OR DATEPART(HOUR, OpenTime) BETWEEN 0 AND 2 THEN '深夜(22-02)'
                ELSE '其他时段'
            END
        ORDER BY TotalAmount DESC
        
        -- 返回结果集5：执行信息
        SELECT 
            '执行信息' as DataType,
            @QueryDate as QueryDate,
            @OpenRecordCount as OpenRecordCount,
            @CloseRecordCount as CloseRecordCount,
            DATEDIFF(MILLISECOND, @StartExecTime, GETDATE()) as ExecutionTimeMs,
            'SUCCESS' as Status
        
        DROP TABLE #BusinessData
        
        IF @Debug = 1
            PRINT '存储过程执行完成，总耗时: ' + CAST(DATEDIFF(MILLISECOND, @StartExecTime, GETDATE()) AS NVARCHAR(10)) + 'ms'
        
    END TRY
    BEGIN CATCH
        IF OBJECT_ID('tempdb..#BusinessData') IS NOT NULL
            DROP TABLE #BusinessData
            
        SET @ErrorMessage = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()
        DECLARE @ErrorLine INT = ERROR_LINE()
        
        -- 返回错误信息
        SELECT 
            '错误信息' as DataType,
            @ErrorMessage as ErrorMessage,
            @ErrorSeverity as ErrorSeverity,
            @ErrorState as ErrorState,
            @ErrorLine as ErrorLine,
            DATEDIFF(MILLISECOND, @StartExecTime, GETDATE()) as ExecutionTimeMs,
            'ERROR' as Status
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH
END
GO
```

### 步骤4：创建测试和验证存储过程

```sql
-- 创建数据验证存储过程
CREATE PROCEDURE [dbo].[sp_ValidateKtvBusinessData]
    @QueryDate DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @QueryDate IS NULL
        SET @QueryDate = CAST(DATEADD(DAY, -1, GETDATE()) AS DATE)
    
    DECLARE @StartTime DATETIME = @QueryDate
    DECLARE @EndTime DATETIME = DATEADD(DAY, 1, @QueryDate)
    
    -- 检查链接服务器连接
    BEGIN TRY
        DECLARE @TestCount INT
        SELECT @TestCount = COUNT(*) FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo WHERE 1=0
        SELECT '链接服务器连接' as CheckType, 'SUCCESS' as Status, '连接正常' as Message
    END TRY
    BEGIN CATCH
        SELECT '链接服务器连接' as CheckType, 'ERROR' as Status, ERROR_MESSAGE() as Message
        RETURN -1
    END CATCH
    
    -- 数据质量检查
    DECLARE @OpenCount INT, @CloseCount INT, @MatchCount INT
    
    SELECT @OpenCount = COUNT(*) 
    FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo 
    WHERE BookDateTime >= @StartTime 
        AND BookDateTime < @EndTime 
        AND Invno IS NOT NULL 
        AND Invno != ''
    
    SELECT @CloseCount = COUNT(*) FROM dbo.rmcloseinfo
    
    SELECT @MatchCount = COUNT(*) 
    FROM [RMS2019_LINK].dbfood.dbo.opencacheinfo o
    INNER JOIN dbo.rmcloseinfo c ON o.Invno = c.InvNo
    WHERE o.BookDateTime >= @StartTime 
        AND o.BookDateTime < @EndTime
        AND o.Invno IS NOT NULL 
        AND o.Invno != ''
    
    SELECT 
        '数据质量检查' as CheckType,
        'INFO' as Status,
        @QueryDate as QueryDate,
        @OpenCount as OpenRecords,
        @CloseCount as CloseRecords,
        @MatchCount as MatchedRecords,
        CASE WHEN @OpenCount > 0 
            THEN CAST(@MatchCount * 100.0 / @OpenCount AS DECIMAL(5,2))
            ELSE 0 
        END as MatchRate
END
GO
```

### 步骤5：C#应用层集成代码

```csharp
// 数据库连接配置类
public class DatabaseConfig
{
    public static string OperateDataConnectionString =
        "Data Source=192.168.2.5;Initial Catalog=operatedata;User ID=sa;Password=***********;Connection Timeout=30;Command Timeout=300;";
}

// 数据访问层
public class KtvBusinessDataRepository
{
    private readonly string _connectionString;

    public KtvBusinessDataRepository()
    {
        _connectionString = DatabaseConfig.OperateDataConnectionString;
    }

    public async Task<KtvBusinessDataResult> GetBusinessDataAsync(DateTime queryDate, int? shopId = null)
    {
        var result = new KtvBusinessDataResult();

        using (var connection = new SqlConnection(_connectionString))
        {
            using (var command = new SqlCommand("sp_GetKtvBusinessData", connection))
            {
                command.CommandType = CommandType.StoredProcedure;
                command.CommandTimeout = 300; // 5分钟超时
                command.Parameters.AddWithValue("@QueryDate", queryDate.Date);
                if (shopId.HasValue)
                    command.Parameters.AddWithValue("@ShopId", shopId.Value);
                command.Parameters.AddWithValue("@Debug", 1);

                await connection.OpenAsync();

                using (var reader = await command.ExecuteReaderAsync())
                {
                    // 读取业务数据
                    result.BusinessData = await ReadBusinessDataAsync(reader);

                    // 读取直落统计
                    if (await reader.NextResultAsync())
                        result.DirectAnalysis = await ReadDirectAnalysisAsync(reader);

                    // 读取渠道统计
                    if (await reader.NextResultAsync())
                        result.ChannelAnalysis = await ReadChannelAnalysisAsync(reader);

                    // 读取时段统计
                    if (await reader.NextResultAsync())
                        result.TimeSlotAnalysis = await ReadTimeSlotAnalysisAsync(reader);

                    // 读取执行信息
                    if (await reader.NextResultAsync())
                        result.ExecutionInfo = await ReadExecutionInfoAsync(reader);
                }
            }
        }

        return result;
    }
}
```
