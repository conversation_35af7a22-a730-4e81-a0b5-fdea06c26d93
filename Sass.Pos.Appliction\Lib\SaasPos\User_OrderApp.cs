﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Application.Lib.DbFood;
using Saas.Pos.Model.SaasPos;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using Saas.Pos.RepositoryFactory.T4.DbFood;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Application.Lib.SaasPos
{
    public partial class User_OrderApp : AppBase<User_Order>
    {
        /// <summary>
        /// 管理平台查询核销记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetSkuDescriptionModel> GetUserOrderGoodName(int orderId)
        {
            return Repository.User_Order.GetUserOrderGoodName(orderId);
        }

        /// <summary>
        /// 获取订单信息
        /// </summary>
        /// <param name="orderid"></param>
        /// <returns></returns>
        public GetOrderDetailModel GetOrderData(int orderId)
        {
            return Repository.User_Order.GetOrderData(orderId);
        }

        /// <summary>
        /// 获取用户绑定未使用优惠卷
        /// </summary>
        /// <param name="OpenId"></param>
        /// <returns></returns>
        public int GetUserCouData(string OpenId, string CamId)
        {
            return Repository.User_Order.GetUserCouData(OpenId, CamId);
        }

        // <summary>
        /// 获取用户绑定未使用优惠卷数量Ex
        /// </summary>
        /// <param name="OpenId"></param>
        /// <returns></returns>
        public GetCoupon_CampaignModelEx GetUserCouDataEx(string OpenId, string CamId)
        {
            return Repository.User_Order.GetUserCouDataEx(OpenId, CamId);
        }

        /// <summary>
        /// 获取用户还能使用优惠卷数量
        /// </summary>
        /// <param name="openid"></param>
        /// <returns></returns>
        public int GetIsUseCouCount(string openid)
        {
            return Repository.User_Order.GetIsUseCouCount(openid);
        }

        public int GetIsUseCouCountByMiddleId(string openid, int MiddleId)
        {
            return Repository.User_Order.GetIsUseCouCountByMiddleId(openid, MiddleId);
        }

        /// <summary>
        /// 根据订单号找开房信息
        /// </summary>
        /// <param name="orderId"></param>
        /// <returns></returns>
        public GetBookCaheOpenDataModel GetBookCaheOpenData(int orderId)
        {
            return Repository.User_Order.GetBookCaheOpenData(orderId);
        }

        /// <summary>
        /// 获取用户预定数据
        /// </summary>
        /// <returns></returns>
        public GetUserOrderDataModel GetUserOrderData(GetUserOrderDataContext context)
        {
            return Repository.User_Order.GetUserOrderData(context);
        }

        /// <summary>
        /// 获取每日使用数据汇总
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetOrderMakeByDay> GetShopMakeData(GetShopMakeDataContext context)
        {
            return Repository.User_Order.GetShopMakeData(context);
        }

        /// <summary>
        /// 获取用户订单数
        /// </summary>
        /// <param name="openid"></param>
        /// <returns></returns>
        public GetOrderCountModel GetOrderCount(string openid)
        {
            return Repository.User_Order.GetOrderCount(openid);
        }

        /// <summary>
        /// 获取用户领取活动优惠卷数
        /// </summary>
        /// <param name="openid"></param>
        /// <returns></returns>
        public GetCouCountModel GetCouCount(string openid)
        {
            return Repository.User_Order.GetCouCount(openid);
        }

        /// <summary>
        /// 查询个人导出报表数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetMakeFileDataModel> GetMakeFileData(GetMakeFileDataContext context)
        {
            return Repository.User_Order.GetMakeFileData(context);
        }

        /// <summary>
        /// 查询在线预定订单明细
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetShopBookCacheOrderDetailModel> GetShopBookCacheOrderDetail(GetOrderDetailDataExContext context)
        {
            return Repository.User_Order.GetShopBookCacheOrderDetail(context);
        }

        /// <summary>
        /// 查询在线预订数据统计数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetOrderDataStatisModel> GetOrderDataStatis(GetOrderDataStatisContext context)
        {
            return Repository.User_Order.GetOrderDataStatis(context);
        }

        /// <summary>
        /// 导出在线预订数据统计数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<ExportOrderDataStatisModel> ExportOrderDataStatis(GetOrderDataStatisContext context)
        {
            return Repository.User_Order.ExportOrderDataStatis(context);
        }

        public GetOrderDataInfoModel GetOrder(int orderId)
        {
            return Repository.User_Order.GetOrder(orderId);
        }
    }
}
