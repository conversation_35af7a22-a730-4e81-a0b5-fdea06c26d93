﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Tools
{
    public class StoreHelper
    {
        private static List<StoreConfig> StoreList = new List<StoreConfig>();
        static StoreHelper _StoreHelper;
        public static StoreHelper StoreManage
        {
            get
            {
                if (_StoreHelper == null)
                    _StoreHelper = new StoreHelper();
                return _StoreHelper;
            }
        }

        StoreHelper()
        {
            var configStr = System.IO.File.ReadAllText(GlobalConfig.Global.OrderConfig.ShopFilePath, Encoding.UTF8);
            StoreList = configStr.ToList<StoreConfig>();
        }

        public List<StoreConfig> GetStoreList()
        {
            return StoreList;
        }

        public StoreConfig GetStoreInfo(int shopId)
        {
            return StoreList.FirstOrDefault(w => w.Id == shopId);
        }
    }

    public class StoreConfig
    {
        public int Id { get; set; }

        public string StoreName { get; set; }

        public string Url { get; set; }
    }
}
