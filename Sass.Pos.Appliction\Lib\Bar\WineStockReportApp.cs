﻿using Saas.Pos.Model.Bar.Context;
using Saas.Pos.Model.Bar.Model;
using Saas.Pos.Repository.Lib.Bar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Bar
{
    public class WineStockReportApp
    {
        public WineStockRepository WineRepository = new WineStockRepository();
        /// <summary>
        /// 存酒报表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<WineStorkReportModel> GetWineStorkReportData(WineStorkReportContext context)
        {
            return WineRepository.GetWineStorkReportData(context);
        }
    }
}
