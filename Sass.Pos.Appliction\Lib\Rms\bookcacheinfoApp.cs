﻿using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Model;
using Saas.Pos.Model.SaasPos.Context;
using Saas.Pos.Model.SaasPos.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class bookcacheinfoApp : AppBase<bookcacheinfo>
    {
        public List<GetBookCacheModel> GetBookList(GetBookCacheContext context)
        {
            return Repository.bookcacheinfo.GetBookList(context);
        }

        public List<BookCaCheModel> GetCaCheData(DateTime startTime, DateTime endTime)
        {
            return Repository.bookcacheinfo.GetCaCheData(startTime, endTime);
        }

        public int GetOverTimeCount(int shopId, string comeDate, string timeName)
        {
            return Repository.bookcacheinfo.GetOverTimeCount(shopId, comeDate, timeName);
        }

        public List<GetOrderMakeByDay> GetShopMakeData(GetShopMakeDataContext context)
        {
            return Repository.bookcacheinfo.GetShopMakeData(context);
        }

        /// <summary>
        /// 查询每日数据汇总
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public List<GetMakeDataByDay> GetShopMakeExData(GetShopMakeDataContextByDay context)
        {
            return Repository.bookcacheinfo.GetShopMakeExData(context);
        }

        /// <summary>
        /// 获取rms的在线预订中的普通预约数
        /// </summary>
        /// <returns></returns>
        public int GetOrdinaryCount(string comeDate, string custTel)
        {
            return Repository.bookcacheinfo.GetOrdinaryCount(comeDate, custTel);
        }

    }
}
