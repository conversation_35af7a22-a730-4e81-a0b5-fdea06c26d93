﻿using Saas.Pos.Model.Rms;
using Saas.Pos.Model.Rms.Context;
using Saas.Pos.Model.Rms.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Appliction.Lib.Rms
{
    public partial class birthday_gift_recordApp : AppBase<birthday_gift_record>
    {
        public List<GetBirthdayGiftRecordModel> GetBirthdayList(GetBirthdayGiftRecordContext context) 
        {
            return Repository.birthday_gift_record.GetBirthdayList(context);
        }
    }
}
