﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>

  <log4net>
    <!--<logger name="InfoLogTest">
      <level value="INFO"/>
      <appender-ref ref="InfoLogTestappender" />
    </logger>-->

    <!--根配置-->
    <root>
      <!--日志级别:可选值: ERROR > WARN > INFO > DEBUG -->
      <level value="ERROR"/>
      <level value="WARN"/>
      <level value="INFO"/>
      <level value="DEBUG"/>
      <appender-ref ref="ErrorLog" />
      <appender-ref ref="WarnLog" />
      <appender-ref ref="InfoLog" />
      <appender-ref ref="DebugLog" />
    </root>

    <!-- 信息 Info.log-->
    <appender name="InfoLogTestappender" type="log4net.Appender.RollingFileAppender">
      <!--目录路径，可以是相对路径或绝对路径-->
      <param name="File" value="App_Data/LoginfoTest/"/>
      <!--文件名，按日期生成文件夹-->
      <param name="DatePattern" value="/yyyy-MM-dd/&quot;Info.log&quot;"/>
      <!--追加到文件-->
      <appendToFile value="true"/>
      <!--创建日志文件的方式，可选值：Date[日期],文件大小[Size],混合[Composite]-->
      <rollingStyle value="Composite"/>
      <!--写到一个文件-->
      <staticLogFileName value="false"/>
      <!--单个文件大小。单位:KB|MB|GB-->
      <maximumFileSize value="200MB"/>
      <!--最多保留的文件数，设为"-1"则不限-->
      <maxSizeRollBackups value="-1"/>
      <!--日志格式-->
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%message"/>
      </layout>

    </appender>



    <!-- 错误 Error.log-->
    <appender name="ErrorLog" type="log4net.Appender.RollingFileAppender">
      <!--目录路径，可以是相对路径或绝对路径-->
      <param name="File" value="App_Data/Log/"/>
      <!--文件名，按日期生成文件夹-->
      <param name="DatePattern" value="/yyyy-MM-dd/&quot;Error.log&quot;"/>
      <!--追加到文件-->
      <appendToFile value="true"/>
      <!--创建日志文件的方式，可选值：Date[日期],文件大小[Size],混合[Composite]-->
      <rollingStyle value="Composite"/>
      <!--写到一个文件-->
      <staticLogFileName value="false"/>
      <!--单个文件大小。单位:KB|MB|GB-->
      <maximumFileSize value="200MB"/>
      <!--最多保留的文件数，设为"-1"则不限-->
      <maxSizeRollBackups value="-1"/>
      <!--日志格式-->
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%n异常时间：%d [%t]  %n异常级别：%-5p %n异 常 类：%c [%x]  %n%m %n ---------------------------"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="ERROR" />
        <param name="LevelMax" value="ERROR" />
      </filter>
    </appender>

    <!-- 警告 Warn.log-->
    <appender name="WarnLog" type="log4net.Appender.RollingFileAppender">
      <!--目录路径，可以是相对路径或绝对路径-->
      <param name="File" value="App_Data/Log/"/>
      <!--文件名，按日期生成文件夹-->
      <param name="DatePattern" value="/yyyy-MM-dd/&quot;Warn.log&quot;"/>
      <!--追加到文件-->
      <appendToFile value="true"/>
      <!--创建日志文件的方式，可选值：Date[日期],文件大小[Size],混合[Composite]-->
      <rollingStyle value="Composite"/>
      <!--写到一个文件-->
      <staticLogFileName value="false"/>
      <!--单个文件大小。单位:KB|MB|GB-->
      <maximumFileSize value="200MB"/>
      <!--最多保留的文件数，设为"-1"则不限-->
      <maxSizeRollBackups value="-1"/>
      <!--日志格式-->
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%message"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="WARN" />
        <param name="LevelMax" value="WARN" />
      </filter>
    </appender>

    <!-- 信息 Info.log-->
    <appender name="InfoLog" type="log4net.Appender.RollingFileAppender">
      <!--目录路径，可以是相对路径或绝对路径-->
      <param name="File" value="App_Data/Log/"/>
      <!--文件名，按日期生成文件夹-->
      <param name="DatePattern" value="/yyyy-MM-dd/&quot;Info.htm&quot;"/>
      <!--追加到文件-->
      <appendToFile value="true"/>
      <!--创建日志文件的方式，可选值：Date[日期],文件大小[Size],混合[Composite]-->
      <rollingStyle value="Composite"/>
      <!--写到一个文件-->
      <staticLogFileName value="false"/>
      <!--单个文件大小。单位:KB|MB|GB-->
      <maximumFileSize value="200MB"/>
      <!--最多保留的文件数，设为"-1"则不限-->
      <maxSizeRollBackups value="-1"/>
      <!--日志格式-->
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="&lt;HR COLOR=blue&gt;%n日志时间：%d [%t] &lt;BR&gt;%n日志级别：%-5p &lt;BR&gt;%n日 志 类：%c [%x] &lt;BR&gt;%n%m &lt;BR&gt;%n &lt;HR Size=1&gt;"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="INFO" />
        <param name="LevelMax" value="INFO" />
      </filter>
    </appender>




    <!-- 调试 Debug.log-->
    <appender name="DebugLog" type="log4net.Appender.RollingFileAppender">
      <!--目录路径，可以是相对路径或绝对路径-->
      <param name="File" value="App_Data/Log/"/>
      <!--文件名，按日期生成文件夹-->
      <param name="DatePattern" value="/yyyy-MM-dd/&quot;Debug.log&quot;"/>
      <!--追加到文件-->
      <appendToFile value="true"/>
      <!--创建日志文件的方式，可选值：Date[日期],文件大小[Size],混合[Composite]-->
      <rollingStyle value="Composite"/>
      <!--写到一个文件-->
      <staticLogFileName value="false"/>
      <!--单个文件大小。单位:KB|MB|GB-->
      <maximumFileSize value="200MB"/>
      <!--最多保留的文件数，设为"-1"则不限-->
      <maxSizeRollBackups value="-1"/>
      <!--日志格式-->
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%message"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="DEBUG" />
        <param name="LevelMax" value="DEBUG" />
      </filter>
    </appender>

  </log4net>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
</configuration>