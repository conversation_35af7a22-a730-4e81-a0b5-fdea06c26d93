﻿using ComponentApplicationServiceInterface.Repository;
using Saas.Pos.Model.DbFood;
using Saas.Pos.Model.DbFood.Context;
using Saas.Pos.Model.DbFood.Model;
using Saas.Pos.Model.SaasPos.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Saas.Pos.Domain.IRepository.DbFood
{
    public partial interface IRoomRepository : IRepositoryBase<Room>
    {
        GetRoomInfoModel GetRoomInfo(GetRoomInfoContext context);

        bool CheckOutPay(CheckOutPayContext context);

        bool ContinueOrder(ContinueOrderContext context);

        NewRoom GetRoom(string rmNo);

        GetRtInfoModel GetRmType(string rtNo);

        GetRoomCurrentNumber GetRoomNumber(string rmNo, string invNo);

        int UpdateRoomRate(int DiscRate, int FixedDisc, string RmNo);

        int ChangeRmStatus(ChangeRmStatusContext context);
    }
}
