﻿namespace Saas.Pos.Common.Rms.NumberPool
{
    public class SystemConfigModel
    {
        /// <summary>
        /// 调用门店
        /// </summary>
        public int ShopId { get; set; }

        public string ShopName { get; set; }

        /// <summary>
        /// 生成长度
        /// </summary>
        public int Length { get; set; }

        /// <summary>
        /// 生成模式1：无规律生成 2：顺序生成
        /// </summary>
        public int GenerateMode { get; set; }

        /// <summary>
        /// 是否含有首字母为true则字段值必须存在
        /// </summary>
        public string Initial { get; set; }

        /// <summary>
        /// 不允许出现的数字，可以多个 用,拼接
        /// </summary>
        public string existNumbers { get; set; }
    }
}