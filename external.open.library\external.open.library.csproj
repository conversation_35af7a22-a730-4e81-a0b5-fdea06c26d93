﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{83EE90BB-211A-4CAA-86A8-9AA0FF223D92}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>external.open.library</RootNamespace>
    <AssemblyName>external.open.library</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentCore">
      <HintPath>..\Sass.Pos.Model\Packages\ComponentCore.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Class1.cs" />
    <Compile Include="TakeaWayPlatform\LocalLifeBase.cs" />
    <Compile Include="TakeaWayPlatform\LocalLifeFactory.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\DeliveryMeituan.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Request\ConsumeRequest.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Request\GetAccecctokenRequest.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Request\GetOrderShareInfoRequest.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Request\MeituanQueryCouponRequest.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Request\MeituanRequestBase.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Request\QueryProductRequest.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Request\ReverseConsumeRequest.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Response\ConsumeResponse.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Response\GetAccesstokenResponse.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Response\GetOrderShareInfoResponse.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Response\MeituanQueryCouponResponse.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Response\MeituanQueryProductResponse.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Response\MeituanResponseBase.cs" />
    <Compile Include="TakeaWayPlatform\MeiTuan\Response\ReverseConsumeResponse.cs" />
    <Compile Include="TakeaWayPlatform\Model\ConsumeModel.cs" />
    <Compile Include="TakeaWayPlatform\Model\ConsumeResponseModel.cs" />
    <Compile Include="TakeaWayPlatform\Model\PrepareCouponModel.cs" />
    <Compile Include="TakeaWayPlatform\Model\QueryCouponModel.cs" />
    <Compile Include="TakeaWayPlatform\Model\QueryCouponResponse.cs" />
    <Compile Include="TakeaWayPlatform\Model\QueryOrderModel.cs" />
    <Compile Include="TakeaWayPlatform\Model\QueryOrderResponse.cs" />
    <Compile Include="TakeaWayPlatform\Model\QueryProductModel.cs" />
    <Compile Include="TakeaWayPlatform\Model\QueryProductResponse.cs" />
    <Compile Include="TakeaWayPlatform\Model\ReverseConsumeModel.cs" />
    <Compile Include="TakeaWayPlatform\Model\ReverseConsumeResponseModel.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\DeliveryTiktok.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Request\TiktokConsumeRequest.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Request\TiktokQueryOrderRequest.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Request\TiktokQueryProductRequest.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Request\TiktokReverseConsumeRequest.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\GetAccesstokenResponse.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokConsumeResponse.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokQueryCertificateResponse.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokQueryConsumeResponse.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokQueryCouponResponse.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokQueryOrderResponse.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokQueryProductResponse.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokResponseBase.cs" />
    <Compile Include="TakeaWayPlatform\TikTok\Response\TiktokReverseConsumeResponse.cs" />
    <Compile Include="TakeaWayPlatform\WayPlatformException.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Sass.Pos.Common\Saas.Pos.Common.csproj">
      <Project>{53316D66-D568-4B6C-9445-8C95E2C8AF83}</Project>
      <Name>Saas.Pos.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Sass.Pos.Model\Saas.Pos.Model.csproj">
      <Project>{d3e4e488-5933-4b09-88d9-a5adbf6389ce}</Project>
      <Name>Saas.Pos.Model</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>