﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Saas.Pos.Common.Global.Configs.Rms
{
    /// <summary>
    /// Rms预约模块全局配置
    /// </summary>
    public class BookConfig
    {
        

        /// <summary>
        /// 操作预约人员ID
        /// </summary>
        public string BookUserId = "00001";

        /// <summary>
        /// 操作预约人员名称
        /// </summary>
        public string BookUserName = "在线预订";

        /// <summary>
        /// 预约配置文件保存地址
        /// </summary>
        public string BookConfigPath = AppDomain.CurrentDomain.BaseDirectory + "\\Configs\\Rms\\numberPoolConfig.json";

        public string BookingPath = AppDomain.CurrentDomain.BaseDirectory + "\\Configs\\Book\\BookConfig.json";

        BookingConfigModel _BookingConfig;
        public BookingConfigModel BookingConfig
        {
            get
            {
                if (_BookingConfig == null)
                {
                    var jsonStr = File.ReadAllText(BookingPath);
                    _BookingConfig = jsonStr.ToObject<BookingConfigModel>();
                }
                return _BookingConfig;
            }
        }
    }

    public class BookingConfigModel
    {
        public int TimeInterval { get; set; }
    }
}
