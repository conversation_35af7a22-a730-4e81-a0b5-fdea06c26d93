﻿using external.open.library.TakeaWayPlatform.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;

namespace external.open.library.TakeaWayPlatform
{
    public abstract class LocalLifeBase
    {
        /// <summary>
        /// 验券核销
        /// </summary>
        /// <param name="consume"></param>
        /// <returns></returns>
        public abstract List<ConsumeResponseModel> Consume(ConsumeModel consume);

        /// <summary>
        /// 取消核验
        /// </summary>
        public abstract ReverseConsumeResponseModel ReverseConsume(ReverseConsumeModel model);

        /// <summary>
        /// 查验券是否可以核销
        /// </summary>
        /// <returns></returns>
        public abstract QueryCouponResponse QueryCoupon(QueryCouponModel query);

        /// <summary>
        /// 查询所有商品
        /// </summary>
        /// <returns></returns>
        public abstract List<QueryProductResponse> QueryProduct(QueryProductModel model);

        /// <summary>
        /// 验券准备
        /// </summary>
        /// <param name="query"></param>
        public abstract PrepareCouponModel Prepare(QueryCouponModel query);

        /// <summary>
        /// 查询订单信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public abstract QueryOrderResponse QueryOrder(QueryOrderModel model);

        /// <summary>
        /// 重试机制
        /// </summary>
        /// <param name="action"></param>
        /// <param name="errorAction"></param>
        /// <param name="errorMsg"></param>
        /// <param name="count"></param>
        protected void Retry(Action action, Action errorAction, List<string> errorMsg, int count = 1)
        {
            int index = 0;
            while (index < count)
            {
                try
                {
                    action();
                    return;
                }
                catch (WayPlatformException ex)
                {
                    if (errorMsg.Any(x => ex.Message.Contains(x)))
                    {
                        index++;
                        if (index < count)
                            errorAction();
                        else
                            throw new Exception(string.Format("重试{0}次发生异常！异常原因：{1}", index, ex.Message));
                    }
                    else
                    {
                        throw new Exception(ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception("未知异常：" + ex.Message);
                }
            }
        }

        protected bool CheckValidation(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            return true;
        }
    }
}
